{"name": "lycaeum-admin-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.6", "@react-google-maps/api": "^2.20.5", "@types/react-datepicker": "^7.0.0", "axios": "^1.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "core-js-pure": "^3.30.1", "date-fns": "^3.6.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "input-otp": "^1.2.4", "jodit-react": "^4.1.2", "lottie-web": "^5.9.4", "moment": "^2.29.3", "next": "15.1.5", "next-auth": "^4.3.4", "qrcode.react": "^4.2.0", "react": "19.0.0", "react-activity": "^2.1.3", "react-avatar": "^5.0.3", "react-datepicker": "^7.6.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-draft-wysiwyg": "^1.15.0", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.4.1", "react-icons": "^4.4.0", "react-loading-skeleton": "^3.1.0", "react-select": "^5.7.3", "sass": "^1.52.2", "sharp": "^0.34.2", "swr": "^2.1.5", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.2", "xlsx": "https://cdn.sheetjs.com/xlsx-0.19.2/xlsx-0.19.2.tgz", "yup": "^1.0.0", "zod": "^3.22.2"}, "devDependencies": {"@types/draft-js": "^0.11.18", "@types/draftjs-to-html": "^0.8.4", "@types/google-map-react": "^2.1.10", "@types/node": "^18", "@types/react": "19.0.7", "@types/react-datetime-picker": "^3.4.1", "@types/react-dom": "19.0.3", "@types/react-draft-wysiwyg": "^1.13.8", "@types/react-lottie": "^1.2.6", "autoprefixer": "^10.4.15", "eslint": "^8", "eslint-config-next": "15.1.5", "postcss": "^8", "prettier": "^3.0.1", "prettier-plugin-tailwindcss": "^0.5.2", "tailwindcss": "^3.3.3", "typescript": "^5"}, "resolutions": {"@types/react": "19.0.7", "@types/react-dom": "19.0.3"}}