export default function nFormatter(num: number) {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1).replace(/\.0$/, "") + "G";
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, "") + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, "") + "K";
  }
  return num;
}

// export default function nFormatter(num: number | null | undefined) {
//   console.log("Under FC :", num);
//   if (num != null) {
//     if (num >= 1000000000) {
//       return (num / 1000000000).toFixed(1).replace(/\.0$/, "") + "G";
//     }
//     if (num >= 1000000) {
//       return (num / 1000000).toFixed(1).replace(/\.0$/, "") + "M";
//     }
//     if (num >= 1000) {
//       return (num / 1000).toFixed(1).replace(/\.0$/, "") + "K";
//     }
//     return num;
//   }
//   //   else return 0;
// }
