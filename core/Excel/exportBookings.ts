import moment from "moment";
import { getSession } from "next-auth/react";
import { toast } from "react-hot-toast";
import { PgObject } from "types/dashboard";
import * as XLSX from "xlsx";

function flattenData(data: any) {
  return data.map((item: any) => {
    const { fields, ...rest } = item;
    return { ...rest, ...fields };
  });
}

export function generateExcelFile(data: any) {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Create a new worksheet
  const worksheet = XLSX.utils.json_to_sheet(data);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Confirmed Bookings");

  // Write the workbook to a file
  XLSX.writeFile(workbook, "Confirmed Bookings.xlsx");
}

export async function DataGrabber(id: string) {
  toast.loading("Exporting Data");
  const session = await getSession();
  const token = session?.token;

  let Tutors: Array<any> = [];

  const res = await fetch(
    `https://api.lycaeumapp.com/product/bookings/${id}?p=1`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    },
  );
  const paginagion = res.headers.get("pagination");
  const pgobject: PgObject = JSON.parse(paginagion ? paginagion : "");

  toast.dismiss();
  toast.loading(`Exporting ${pgobject.totalItems} bookings.`);

  for (let i = 1; i <= pgobject.totalPages; i++) {
    toast.dismiss();
    toast.loading(`Exporting Page ${i}`);
    const res = await fetch(
      `https://api.lycaeumapp.com/product/bookings/${id}?p=${i}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      },
    );

    var data = await res.json();

    // Flatten the `fields` object and merge it with the main data object
    const flattenedData = data.map((item: any) => ({
      ...item,
      ...item.fields, // Spread the `fields` object into the main object
      fields: undefined, // Optionally remove the `fields` key

      // Add a new formatted `dateTime`
      dateTime: moment(item.dateTime)
        .add(new Date().getTimezoneOffset() * -1, "minute")
        .format("DD MMMM YYYY, hh:mm A"),
    }));

    Tutors.push(...flattenedData);
  }

  toast.dismiss();
  generateExcelFile(Tutors);
  toast.success("File Exported!");
}
