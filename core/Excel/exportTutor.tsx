import { getSession } from "next-auth/react";
import { toast } from "react-hot-toast";
import { PgObject } from "types/dashboard";
import * as XLSX from "xlsx";

export function generateExcelFile(data: any) {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Create a new worksheet
  const worksheet = XLSX.utils.json_to_sheet(data);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Tutors");

  // Write the workbook to a file
  XLSX.writeFile(workbook, "Tutors.xlsx");
}

export async function DataGrabber(query: any) {
  toast.loading("Exporting Data");
  const session = await getSession();
  const token = session?.token;

  let Tutors: Array<any> = [];

  const res = await fetch(
    "https://api.lycaeumapp.com/tutor/list?p=1&items=300" + query,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    },
  );
  const paginagion = res.headers.get("pagination");
  const pgobject: PgObject = JSON.parse(paginagion ? paginagion : "");

  toast.dismiss();
  toast.loading(`Exporting ${pgobject.totalItems} tutors.`);

  for (let i = 1; i <= pgobject.totalPages; i++) {
    toast.dismiss();
    toast.loading(`Exporting Page ${i}`);
    const res = await fetch(
      `https://api.lycaeumapp.com/tutor/list?p=${i}&items=300` + query,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      },
    );

    var data = await res.json();
    Tutors.push(...data);
  }

  toast.dismiss();
  generateExcelFile(Tutors);
  toast.success("File Exported!");
}

// Survey Export Functions
export function generateSurveyExcelFile(data: any) {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // First, collect all unique questions from all surveys to create consistent columns
  const allQuestions = new Set<string>();
  data.forEach((survey: any) => {
    if (survey.surveyQuestions && survey.surveyQuestions.length > 0) {
      survey.surveyQuestions.forEach((qa: any) => {
        allQuestions.add(qa.question);
      });
    }
  });

  // Convert to array for consistent ordering
  const questionArray = Array.from(allQuestions);

  // Flatten survey data for Excel export
  const flattenedData = data.map((survey: any) => {
    const rowData: any = {
      "Student ID": survey.studentId,
      Name: survey.name,
      "Survey Option": survey.option,
      "Total Referred": survey.totalReferred,
      "Referred Emails":
        survey.surveyReferred?.map((ref: any) => ref.email).join(", ") || "",
    };

    // Create a map of questions to answers for this survey
    const questionAnswerMap = new Map<string, string>();
    if (survey.surveyQuestions && survey.surveyQuestions.length > 0) {
      survey.surveyQuestions.forEach((qa: any) => {
        questionAnswerMap.set(qa.question, qa.answer);
      });
    }

    // Add answers for each question (questions become column headers)
    questionArray.forEach((question: string) => {
      rowData[question] = questionAnswerMap.get(question) || "";
    });

    return rowData;
  });

  // Create a new worksheet
  const worksheet = XLSX.utils.json_to_sheet(flattenedData);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Surveys");

  // Write the workbook to a file
  XLSX.writeFile(workbook, "Surveys.xlsx");
}

export async function SurveyDataGrabber(query: any) {
  toast.loading("Exporting Survey Data");
  const session = await getSession();
  const token = session?.token;

  let Surveys: Array<any> = [];

  const res = await fetch(
    "https://api.lycaeumapp.com/survey/list?p=1&items=300" + query,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    },
  );
  const paginagion = res.headers.get("pagination");
  const pgobject: PgObject = JSON.parse(paginagion ? paginagion : "");

  toast.dismiss();
  toast.loading(`Exporting ${pgobject.totalItems} surveys.`);

  for (let i = 1; i <= pgobject.totalPages; i++) {
    toast.dismiss();
    toast.loading(`Exporting Page ${i}`);
    const res = await fetch(
      `https://api.lycaeumapp.com/survey/list?p=${i}&items=300` + query,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      },
    );

    var data = await res.json();
    Surveys.push(...data);
  }

  toast.dismiss();
  generateSurveyExcelFile(Surveys);
  toast.success("Survey File Exported!");
}
