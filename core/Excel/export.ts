import { getSession } from "next-auth/react";
import { toast } from "react-hot-toast";
import { PgObject } from "types/dashboard";
import * as XLSX from "xlsx";

export function generateExcelFile(data: any) {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Create a new worksheet
  const worksheet = XLSX.utils.json_to_sheet(data);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Students");

  // Write the workbook to a file
  XLSX.writeFile(workbook, "Students.xlsx");
}

export async function dataGrabber(query: any) {
  toast.loading("Exporting Data");
  const session = await getSession();
  const token = session?.token;

  let Students: Array<any> = [];

  const res = await fetch(
    "https://api.lycaeumapp.com/student/list?p=1&items=300" + query,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    }
  );
  const paginagion = res.headers.get("pagination");
  const pgobject: PgObject = JSON.parse(paginagion ? paginagion : "");

  toast.dismiss();
  toast.loading(`Exporting ${pgobject.totalItems} students.`);

  for (let i = 1; i <= pgobject.totalPages; i++) {
    toast.dismiss();
    toast.loading(`Exporting Page ${i}`);
    const res = await fetch(
      `https://api.lycaeumapp.com/student/list?p=${i}&items=300` + query,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    var data = await res.json();
    Students.push(...data);
  }

  toast.dismiss();
  generateExcelFile(Students);
  toast.success("File Exported!");
}
