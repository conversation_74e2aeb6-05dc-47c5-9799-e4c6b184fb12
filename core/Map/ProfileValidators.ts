import axios from "axios";
import api from "../../fetchers/BaseUrl";
import * as yup from "yup";

export const ProfileValidation = yup.object({
  fullname: yup.string().required("Name is required!"),
  // phoneNumber: yup.string().required("Phone Number is required!"),
  // Address: yup.string().required("Address is required!"),
  postCode: yup
    .string()
    .required("Postcode is required!")
    .test(
      "",
      "Can not locate your Postcode!",
      async (value: any, values: any) => checkPostCode(value)
    ),
  //   bankName: yup.string().required("bankName is required!"),
  //   accNo: yup.string().required("Account number is required!"),
  //   sc: yup.string().required("Sort code is required!"),
});

export const checkPostCode = async (value: any) => {
  try {
    const res = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?key=AIzaSyCS4J4FaeUIyhGM_XaDielBHx20yo9dzSM&address=${value}`
    );
    if (res.data.status === "OK") return true;
  } catch (e) {
    return false;
  }
  return false;
};

// export const checkEmailAddress = async (value: any) => {
//   if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(value)) {
//     try {
//       const res = await api.post("/auth/check-email", { email: value });
//       if (res.status === 200) return true;
//     } catch {
//       return false;
//     }
//   }
//   return false;
// };
