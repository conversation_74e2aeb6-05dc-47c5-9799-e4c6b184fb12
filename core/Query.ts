export function GenerateQuery(params: any) {
    let searchParams = new URLSearchParams();
    for (let key in params) {
        if (params[key] !== null
            && params[key] !== ""
            && params[key] !== undefined
            && params[key] !== 0) {

            searchParams.append(key, params[key]);
        }
    }
    var string = searchParams.toString();
    
    if (string)
        return "&" + string;
    else
        return ""
}