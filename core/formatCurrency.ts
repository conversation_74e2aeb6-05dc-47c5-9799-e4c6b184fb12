export function formatCurrency(
  amount: string | number | null | undefined,
  currency: string,
): string {
  if (amount == null || amount === "" || isNaN(Number(amount))) {
    return `${currency}0`; // Handle null, undefined, empty string, or invalid numbers
  }

  const numAmount = Number(amount); // Convert to number
  const absAmount = Math.abs(numAmount);

  // If the number is an integer, display without decimals; otherwise, show two decimal places
  const formattedAmount =
    absAmount % 1 === 0 ? absAmount : absAmount.toFixed(2);

  return `${numAmount < 0 ? "-" : ""}${currency}${formattedAmount}`;
}

// export function formatCurrency(
//   amount: number | null | undefined,
//   currency: string,
// ) {
//   if (amount == null) return `${currency}0`; // Handle null or undefined

//   const absAmount = Math.abs(amount);
//   const formattedAmount = Number.isInteger(absAmount)
//     ? absAmount
//     : absAmount.toFixed(2); // Show decimals only if not an integer

//   return `${amount < 0 ? "-" : ""}${currency}${formattedAmount}`;
// }
