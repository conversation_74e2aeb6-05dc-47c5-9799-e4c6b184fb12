import axios from "axios";
import api from "fetchers/BaseUrl";

export const checkEmailAddress = async (value: any) => {
  if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(value)) {
    try {
      const res = await api.post("/auth/check-email", { email: value });
      if (res.status === 200) return true;
    } catch {
      return false;
    }
  }
  return false;
};

export const checkPostCode = async (value: any) => {
  try {
    const res = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?key=AIzaSyCS4J4FaeUIyhGM_XaDielBHx20yo9dzSM&address=${value}`,
    );
    if (res.data.status === "OK") return true;
  } catch (e) {
    return false;
  }
  return false;
};
