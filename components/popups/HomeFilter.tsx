import React, { useState } from "react";
import { tUserS } from "types/tutor";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import InputSelector from "./InputSelector";
import useSWR from "swr";
import { fetcher } from "fetchers/fetcher";
import { tChildF } from "types/dashboard";
import styles from "styles/FilterPopup.module.css";

type Props = {
  handleQuery: any;
  onClose: any;
  toast: any;
};

export default function HomeFilter({ handleQuery, onClose, toast }: Props) {
  const [status, setStatus] = useState<string>("");
  const [sDate, setSDate] = useState<Date>(new Date());
  const [eDate, setEDate] = useState<Date>(new Date());
  const [tutor, setTutor] = useState<string>("All");
  const [selectedTutor, setselectedTutor] = useState<string>("");
  const [isTFocused, setIsTFocused] = useState<Boolean>(false);
  // State For Student
  const [student, setStudent] = useState<string>("All");
  const [selectedStudent, setSelectedStudent] = useState<string>("");
  const [isSFocused, setIsSFocused] = useState<Boolean>(false);

  const [selectedChild, setSelectedChild] = useState<string>("");
  const { data: children, error } = useSWR<Array<tChildF>>(
    selectedStudent ? `/student/${selectedStudent}/children` : null,
    fetcher,
  );
  //State for date
  const [date, setDate] = useState<boolean>(false);
  const [range, setRange] = useState<boolean>(false);

  // State For Button

  const [openButton, setOpenButton] = useState<Boolean>(false);

  const updateStatus = (e: any) => {
    setStatus(e.target.value);
  };
  const updateStartDate = (e: Date | any) => {
    setSDate(e);
    if (e >= eDate) setEDate(e);
  };

  const updateEndDate = (e: Date | any) => {
    setEDate(e);
  };

  const handleTutorSearch = (e: any) => {
    setTutor(e.target.value);
  };

  const handleTutorSelect = (tutor: tUserS) => {
    setIsTFocused(false);
    setselectedTutor(tutor.id);
    setTutor(tutor.fullName);
  };

  const handleTutorFocus = () => {
    setIsTFocused(true);
    if (tutor === "All") setTutor("");
  };

  // Handler For Student ..

  const handleStudentSearch = (e: any) => {
    setStudent(e.target.value);
  };

  const handleStudentSelect = (student: tUserS) => {
    setIsSFocused(false);
    setSelectedStudent(student.id);
    setStudent(student.fullname);
    setSelectedChild("");
  };

  const handleStudentFocus = () => {
    setIsSFocused(true);
    if (student === "All") setStudent("");
  };

  const handleChildSelect = (e: any) => {
    e.preventDefault();
    setSelectedChild(e.target.value);
  };

  const handleReset = () => {
    setStatus("");
    setStudent("All");
    setTutor("All");
    setselectedTutor("");
    setSelectedStudent("");
    setSelectedChild("");
    setIsSFocused(false);
    setIsTFocused(false);
    setDate(false);
    setRange(false);
    setSDate(new Date());
    setEDate(new Date());
    handleQuery("");
  };

  const generateQuery = () => {
    let query = "";
    if (selectedStudent || selectedTutor || date || range) query = query + "&";

    if (selectedStudent) query = query + `student=${selectedStudent}`;

    if (selectedChild && selectedStudent) query = query + "&";

    if (selectedChild) query = query + `child=${selectedChild}`;

    if (selectedTutor && (selectedStudent || selectedChild))
      query = query + "&";

    if (selectedTutor) query = query + `tutor=${selectedTutor}`;

    if (date && (selectedTutor || selectedStudent || selectedChild))
      query = query + "&";

    if (date) query = query + `start=${sDate.toLocaleDateString()}`;

    if (range && (selectedStudent || selectedChild || selectedTutor || date))
      query = query + "&";

    if (range) query = query + `end=${eDate.toLocaleDateString()}`;

    if (
      status &&
      (selectedStudent || selectedChild || selectedTutor || date || range)
    )
      query = query + "&";

    if (status) query = query + `status=${status}`;
    handleQuery(query);
    toast.success("Filter applied.");

    onClose();
  };

  return (
    <div className={styles.Main}>
      <div className={styles.Content}>
        <div className={styles.Title}>Filter Status</div>
        <div className={styles.inputContainer}>
          <select
            className={styles.Field}
            value={status}
            onChange={updateStatus}
          >
            <option value="">All</option>
            <option value="Pending">Pending</option>
            <option value="Confirmed">Confirmed</option>
            <option value="Late Cancelled By Student">
              Late Cancelled By Student
            </option>
            <option value="Cancelled By Tutor">Cancelled By Tutor</option>
            <option value="Cancelled By Student">Cancelled By Student</option>
          </select>
        </div>
        <div className={styles.Title}>Tutor</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={tutor}
            onChange={handleTutorSearch}
            onFocus={handleTutorFocus}
          />
          <InputSelector
            isOpen={isTFocused}
            onChange={handleTutorSelect}
            searchValue={tutor}
            apiRoute="/tutor/search"
          />
        </div>
        <div className={styles.Title}>Student</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={student}
            onChange={handleStudentSearch}
            onFocus={handleStudentFocus}
          />
          <InputSelector
            isOpen={isSFocused}
            onChange={handleStudentSelect}
            searchValue={student}
            apiRoute="/student/search"
          />
        </div>
        {selectedStudent && (
          <>
            <div className={styles.Title}>Filter Child</div>
            <div className={styles.inputContainer}>
              <select
                className={styles.Field}
                value={selectedChild}
                onChange={handleChildSelect}
              >
                <option value="">All</option>
                {children?.map((child: tChildF) => {
                  return (
                    <option key={child.id} value={child.id}>
                      {child.name} ({child.postCode})
                    </option>
                  );
                })}
              </select>
            </div>
          </>
        )}
        <div className={styles.checkboxContainer}>
          <input
            type="checkbox"
            className={styles.Field}
            checked={date}
            onChange={() => setDate(!date)}
          />
          Filter Date
        </div>
        <div className={styles.checkboxContainer}>
          <input
            type="checkbox"
            className={styles.Field}
            checked={range}
            onChange={() => setRange(!range)}
          />
          Filter Range
        </div>
        {date && (
          <>
            <div className={styles.Title}>Start From</div>
            <div className={styles.inputContainer}>
              <DatePicker
                className={styles.Field}
                onChange={updateStartDate}
                selected={sDate}
                dateFormat="dd/MM/yyyy"
              />
            </div>
          </>
        )}
        {range && (
          <>
            <div className={styles.Title}>End At</div>
            <div className={styles.inputContainer}>
              <DatePicker
                className={styles.Field}
                onChange={updateEndDate}
                selected={eDate}
                dateFormat="dd/MM/yyyy"
              />
            </div>
          </>
        )}
      </div>

      <div className={styles.Footer}>
        <button className={styles.rButton} onClick={handleReset}>
          Reset
        </button>
        <button className={styles.sButton} onClick={() => generateQuery()}>
          Apply Filter
        </button>
      </div>
    </div>
  );
}
