import React, { useState } from "react";
import "react-datepicker/dist/react-datepicker.css";
import styles from "styles/CustomPopup.module.css";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  children: any;
};

export default function FilterPopup({ isOpen, onClose, children }: Props) {
  return (
    <div className={isOpen ? styles.Modal : styles.Hidden}>
      <div onClick={onClose} className={styles.Overlay}></div>
      <div className={styles.Contents}>
        <div className={styles.Content}>{children}</div>
      </div>
      <button className={styles.closeButton} onClick={onClose}>
        Close
      </button>
    </div>
  );
}
