import { GenerateQuery } from "core/Query";
import moment from "moment";
import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Selects from "react-select";
import styles from "styles/FilterPopup.module.css";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";
import { Input } from "@components/ui/input";

type Props = {
  handleQuery: any;
  onClose: any;
  name: string;
  query: string;
  setName: any;
  pageReset: any;
  clearQuery: any;
};

type tSelector = {
  value: any;
  label: string;
};
const Selector: Array<tSelector> = [
  { value: "", label: "All" },
  { value: true, label: "True" },
  { value: false, label: "False" },
];

export default function StudentFilter({
  handleQuery,
  onClose,
  name,
  setName,
  pageReset,
}: Props) {
  const [isActive, setIsActive] = useState<boolean>(false);

  const [sDate, setSDate] = useState<Date>(new Date());
  const [eDate, setEDate] = useState<Date>(new Date());

  const [addedOn, setAddedOn] = useState<boolean>(false);
  const [lastModified, setLastModified] = useState<boolean>(false);

  const [status, setStatus] = useState<Array<tSelector>>();

  const [postCode, setPostCode] = useState<string>("");
  const [instrument, setInstrument] = useState<string>("");
  const [tutor, setTutor] = useState<string>("");
  const [child, setChild] = useState<string>("");

  const [minLesson, setMinLesson] = useState<number | null>(null);
  const [maxLesson, setMaxLesson] = useState<number | null>(null);
  const [lastLesson, setLastLesson] = useState<number | null>(null);
  const [upcomingLesson, setUpcomingLesson] = useState<number | null>(null);

  const [minCourse, setMinCourse] = useState<number | null>(null);
  const [maxCourse, setMaxCourse] = useState<number | null>(null);

  const [minBalance, setMinBalance] = useState<number | null>(null);
  const [maxBalance, setMaxBalance] = useState<number | null>(null);

  const [instumentSent, setInstumentSent] = useState<tSelector>(Selector[0]);
  const [usingApp, setUsingApp] = useState<tSelector>(Selector[0]);
  const [Unsubscribed, setUnsubscribed] = useState<tSelector>(Selector[0]);
  const [comment, setComment] = useState<string>("");
  const [country, setCountry] = useState<string>("");
  const [referral, setReferral] = useState<string>("");
  const [haveReferral, setHaveReferral] = useState<string>("");
  const [hasGoal, setHasGoal] = useState<string>("");

  // Handelar...
  const updateAddedDate = (e: Date | any) => {
    setSDate(e);
    if (e >= eDate) setEDate(e);
  };
  const updateLastModifiedDate = (e: Date | any) => {
    setEDate(e);
  };

  const handleReset = () => {
    setStatus([]);

    setMinBalance(null);
    setMaxBalance(null);

    setMinLesson(null);
    setMaxLesson(null);

    setLastLesson(null);
    setUpcomingLesson(null);

    setMinCourse(null);
    setMaxCourse(null);

    setSDate(new Date());
    setEDate(new Date());

    setAddedOn(false);
    setLastModified(false);

    setUsingApp(Selector[0]);
    setInstumentSent(Selector[0]);
    setUnsubscribed(Selector[0]);

    setPostCode("");
    setInstrument("");
    setTutor("");
    setChild("");
    setComment("");
    setCountry("");
    setReferral("");
    setHaveReferral("");
    setHasGoal("");

    handleQuery("");
    pageReset("");
  };

  const updateStatus = (e: any) => {
    setStatus(e.target.value);
  };

  const Update = {
    PostCode: (e: any) => {
      setPostCode(e.target.value);
    },
    Instrument: (e: any) => {
      setInstrument(e.target.value);
    },
    Tutor: (e: any) => {
      setTutor(e.target.value);
    },
    Child: (e: any) => {
      setChild(e.target.value);
    },
    Comment: (e: any) => {
      setComment(e.target.value);
    },

    Country: (e: any) => {
      setCountry(e);
    },

    HaveReferral: (e: any) => {
      setHaveReferral(e);
    },

    HasGoal: (e: any) => {
      setHasGoal(e);
    },

    Referral: (e: any) => {
      setReferral(e.target.value);
    },

    MinLesson: (e: any) => {
      setMinLesson(e.target.value);
    },
    MaxLesson: (e: any) => {
      setMaxLesson(e.target.value);
    },

    LastLesson: (e: any) => {
      setLastLesson(e.target.value);
    },

    UpcomingLesson: (e: any) => {
      setUpcomingLesson(e.target.value);
    },

    MinCourse: (e: any) => {
      setMinCourse(e.target.value);
    },
    MaxCourse: (e: any) => {
      setMaxCourse(e.target.value);
    },

    MinBalance: (e: any) => {
      setMinBalance(e.target.value);
    },
    MaxBalance: (e: any) => {
      setMaxBalance(e.target.value);
    },
  };

  // strin to bool
  const strToBool = (str: string | null) => {
    if (str === "true") return true;
    else if (str === "false") return false;
    else return "";
  };

  useEffect(() => {
    setIsActive(false);

    // url params to value
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.delete("p");

    if (urlParams.toString() !== "") {
      //set status
      const status = urlParams
        .get("status")
        ?.split(",")
        .map((x) => {
          return { value: x, label: x };
        });
      setStatus(status || []);

      //set min lesson
      setMinLesson(Number(urlParams.get("minLesson")));
      //set max lesson
      setMaxLesson(Number(urlParams.get("maxLesson")));
      //set last lesson
      setLastLesson(Number(urlParams.get("lastLesson")));
      //set upcoming lesson
      setUpcomingLesson(Number(urlParams.get("upcomingLesson")));
      //set min course
      setMinCourse(Number(urlParams.get("minCourse")));
      //set max course
      setMaxCourse(Number(urlParams.get("maxCourse")));
      //set min balance
      setMinBalance(Number(urlParams.get("minBalance")));
      //set max balance
      setMaxBalance(Number(urlParams.get("maxBalance")));
      //set post code
      setPostCode(urlParams.get("postCode") || "");
      //set instrument
      setInstrument(urlParams.get("instrument") || "");
      //set tutor
      setTutor(urlParams.get("tutor") || "");
      //set child
      setChild(urlParams.get("child") || "");
      //set comment
      setComment(urlParams.get("comment") || "");

      setReferral(urlParams.get("referral") || "");

      setCountry(urlParams.get("country") || "");

      setHaveReferral(urlParams.get("haveReferral") || "");

      setHasGoal(urlParams.get("hasGoal") || "");

      //set instrument sent selector
      setInstumentSent(
        Selector.find(
          (x) => x.value === strToBool(urlParams.get("instrumentSent")),
        ) || Selector[0],
      );
      //set is unsubscribed selector
      setUnsubscribed(
        Selector.find(
          (x) => x.value === strToBool(urlParams.get("isUnsubscribed")),
        ) || Selector[0],
      );
      //set is using app selector
      setUsingApp(
        Selector.find(
          (x) => x.value === strToBool(urlParams.get("isUsingApp")),
        ) || Selector[0],
      );
      //set added on
      setAddedOn(urlParams.get("addedOn") ? true : false);
      //set last modified
      setLastModified(urlParams.get("lastModified") ? true : false);
      //set addedOn
      setSDate(new Date(urlParams.get("addedOn") || new Date()));
      //set lastModified
      setEDate(new Date(urlParams.get("lastModified") || new Date()));

      //set name
      setName(urlParams.get("name") || "");

      handleQuery("&" + urlParams.toString());
    }

    setIsActive(true);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isActive) generateQuery();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name]);

  const generateQuery = () => {
    var newStat = status?.map((i) => i.value).toString();
    if (name.length > 2)
      handleQuery(
        GenerateQuery({
          status: newStat,
          minLesson,
          maxLesson,
          postCode,
          instrument,
          tutor,
          child,
          name,
          referral,
          country,
          haveReferral,
          hasGoal,
          minBalance,
          maxBalance,
          minCourse,
          maxCourse,
          instrumentSent: instumentSent.value,
          isUsingApp: usingApp.value,
          isUnsubscribed: Unsubscribed.value,
          lastLesson,
          upcomingLesson,
          comment,
          addedOn: addedOn ? moment(sDate).format("YYYY-MM-DD") : "",
          lastModified: lastModified ? moment(eDate).format("YYYY-MM-DD") : "",
        }),
      );
    else {
      handleQuery(
        GenerateQuery({
          status: newStat,
          minLesson,
          maxLesson,
          instrument,
          tutor,
          child,
          postCode,
          referral,
          country,
          haveReferral,
          hasGoal,
          minBalance,
          maxBalance,
          minCourse,
          maxCourse,
          instrumentSent: instumentSent.value,
          isUsingApp: usingApp.value,
          isUnsubscribed: Unsubscribed.value,
          lastLesson,
          upcomingLesson,
          comment,
          addedOn: addedOn ? moment(sDate).format("YYYY-MM-DD") : "",
          lastModified: lastModified ? moment(eDate).format("YYYY-MM-DD") : "",
        }),
      );
    }
    onClose();
  };

  const sts: Array<tSelector> = [
    { value: "Regular", label: "Regular" },
    { value: "New", label: "New" },
    { value: "Looking for tutor", label: "Looking for tutor" },
    { value: "Green", label: "Green" },
    { value: "Pending 1st lesson", label: "Pending 1st lesson" },
    { value: "Group course only", label: "Group course only" },
    { value: "Call much later", label: "Call much later" },
    { value: "Call later", label: "Call later" },
    { value: "Not active", label: "Not active" },
    { value: "Old new", label: "Old new" },
    { value: "Old green", label: "Old green" },
    { value: "Gave up", label: "Gave up" },
    { value: "Get to restart", label: "Get to restart" },
    { value: "Bus event", label: "Bus event" },
    { value: "Multiple Children", label: "Multiple Children" },
  ];

  return (
    <div className={styles.Main}>
      <div className={styles.StuContent}>
        <div className={styles.Title}>Filter Status</div>
        <div className={styles.inputContainer}>
          <Selects
            isMulti
            id="multistatus"
            name="Multi Status"
            options={sts}
            menuPosition="fixed"
            isSearchable
            placeholder="Select Status"
            onChange={(e: any) => setStatus(e)}
            value={status}
          />
        </div>

        <div className={styles.Title}>Postcode</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={postCode}
            onChange={Update.PostCode}
          />
        </div>

        <div className={styles.Title}>Referral</div>
        <div className=" my-1 mr-2 w-full">
          <Input
            className="mb-3 w-full rounded-sm border border-gray-400 bg-white"
            type="text"
            value={referral}
            onChange={Update.Referral}
          />
        </div>
        <div className={styles.Title}>Instrument</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={instrument}
            onChange={Update.Instrument}
          />
        </div>
        <div className={styles.Title}>Tutor</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={tutor}
            onChange={Update.Tutor}
          />
        </div>
        <div className={styles.Title}>Child</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={child}
            onChange={Update.Child}
          />
        </div>
        <div className={styles.Title}>Country</div>
        <div className="mt-1">
          <Select onValueChange={Update.Country} defaultValue={country}>
            <SelectTrigger className="mb-2 w-full rounded-sm border border-gray-400 bg-white">
              {/* <SelectValue placeholder="Select a type" /> */}
              <div className="">{country ? country : "Both"}</div>
            </SelectTrigger>
            <SelectContent className="text-xs font-normal text-gray-700">
              <SelectGroup>
                <SelectItem value={null as any}>Both</SelectItem>
                <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                <SelectItem value="United States">United States</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        <div className={styles.Title}>Have Referral</div>
        <div className="mt-1">
          <Select
            onValueChange={Update.HaveReferral}
            defaultValue={haveReferral}
          >
            <SelectTrigger className="mb-3 w-full rounded-sm border border-gray-400 bg-white">
              <div className=" capitalize">
                {haveReferral ? haveReferral : "All"}
              </div>
            </SelectTrigger>
            <SelectContent className="text-xs font-normal text-gray-700">
              <SelectGroup>
                <SelectItem value={null as any}>All</SelectItem>
                <SelectItem value="true">True</SelectItem>
                <SelectItem value="false">False</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {/* // Goal  */}

        <div className={styles.Title}>Has Goal</div>
        <div className="mt-1">
          <Select onValueChange={Update.HasGoal} defaultValue={hasGoal}>
            <SelectTrigger className="mb-3 w-full rounded-sm border border-gray-400 bg-white">
              <div className=" capitalize">{hasGoal ? hasGoal : "All"}</div>
            </SelectTrigger>
            <SelectContent className="text-xs font-normal text-gray-700">
              <SelectGroup>
                <SelectItem value={null as any}>All</SelectItem>
                <SelectItem value="true">True</SelectItem>
                <SelectItem value="false">False</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>

        {/* // Balances.......... */}
        <div className={styles.Flex}>
          <div className={styles.Left}>
            <div className={styles.Title}>Min Balance</div>

            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={minBalance ?? ""}
                onChange={Update.MinBalance}
              />
            </div>
          </div>
          <div>
            <div className={styles.Title}>Max Balance</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={maxBalance ?? ""}
                onChange={Update.MaxBalance}
              />
            </div>
          </div>
        </div>
        {/* // Lesson.......... */}
        <div className={styles.Flex}>
          <div className={styles.Left}>
            <div className={styles.Title}>Min Lesson</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={minLesson ?? ""}
                onChange={Update.MinLesson}
              />
            </div>
          </div>
          <div>
            <div className={styles.Title}>Max Lesson</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={maxLesson ?? ""}
                onChange={Update.MaxLesson}
              />
            </div>
          </div>
        </div>

        {/* // Last & upcoming Lessons */}
        <div className={styles.Flex}>
          <div className={styles.Left}>
            <div className={styles.Title}>Last</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={lastLesson ?? ""}
                onChange={Update.LastLesson}
              />
            </div>
          </div>
          <div>
            <div className={styles.Title}>Upcoming</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={upcomingLesson ?? ""}
                onChange={Update.UpcomingLesson}
              />
            </div>
          </div>
        </div>
        {/* // Courses.......... */}
        <div className={styles.Flex}>
          <div className={styles.Left}>
            <div className={styles.Title}>Min Course</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={minCourse ?? ""}
                onChange={Update.MinCourse}
              />
            </div>
          </div>
          <div>
            <div className={styles.Title}>Max Course</div>
            <div className={styles.inputContainer}>
              <input
                type="number"
                className={styles.Field}
                value={maxCourse ?? ""}
                onChange={Update.MaxCourse}
              />
            </div>
          </div>
        </div>

        <div className={styles.Title}>Comment</div>
        <div className={styles.inputContainer}>
          <input
            type="text"
            className={styles.Field}
            value={comment}
            onChange={Update.Comment}
          />
        </div>

        <div className={styles.Title}>Instrment Sent</div>
        <div className={styles.inputContainer}>
          <Selects
            id="instumentsent"
            name="instumentSent"
            options={Selector}
            menuPosition="fixed"
            isSearchable
            placeholder=""
            onChange={(e: any) => setInstumentSent(e)}
            value={instumentSent}
          />
        </div>

        <div className={styles.Title}>Using App</div>
        <div className={styles.inputContainer}>
          <Selects
            id="usingapp"
            name="Using App"
            options={Selector}
            menuPosition="fixed"
            isSearchable
            placeholder=""
            onChange={(e: any) => setUsingApp(e)}
            value={usingApp}
          />
        </div>

        <div className={styles.Title}>Unsubscribed</div>
        <div className={styles.inputContainer}>
          <Selects
            id="unsubscribe"
            name="Unsubscribe"
            options={Selector}
            menuPosition="fixed"
            isSearchable
            placeholder="Select Unsubscribed"
            onChange={(e: any) => setUnsubscribed(e)}
            value={Unsubscribed}
          />
        </div>

        <div className={styles.checkboxContainer}>
          <input
            type="checkbox"
            className={styles.Field}
            checked={addedOn}
            onChange={() => setAddedOn(!addedOn)}
          />
          Added On
        </div>

        <div
          style={
            addedOn
              ? { opacity: 1, padding: "2px 0px" }
              : { opacity: 0.3, padding: "2px 0px" }
          }
        >
          <div className={styles.Title}>Added Student</div>
          <div className={styles.inputContainer}>
            <DatePicker
              className={styles.Field}
              onChange={updateAddedDate}
              selected={sDate}
              dateFormat="dd/MM/yyyy"
            />
          </div>
        </div>

        <div className={styles.checkboxContainer}>
          <input
            type="checkbox"
            className={styles.Field}
            checked={lastModified}
            onChange={() => {
              setLastModified(!lastModified);
            }}
          />
          Last Modified
        </div>

        <div
          style={
            lastModified
              ? { opacity: 1, padding: "2px 0px" }
              : { opacity: 0.3, padding: "2px 0px" }
          }
        >
          <div className={styles.Title}>Last Modified Student</div>
          <div className={styles.inputContainer}>
            <DatePicker
              className={styles.Field}
              onChange={updateLastModifiedDate}
              selected={eDate}
              dateFormat="dd/MM/yyyy"
            />
          </div>
        </div>
      </div>
      <div className={styles.Footer}>
        <button className={styles.rButton} onClick={handleReset}>
          Reset
        </button>
        <button className={styles.sButton} onClick={generateQuery}>
          Submit
        </button>
      </div>
    </div>
  );
}
