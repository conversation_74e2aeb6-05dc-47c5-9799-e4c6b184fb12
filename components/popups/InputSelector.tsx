import { fetcher } from "fetchers/fetcher";
import React, { useEffect, useState } from "react";
import useS<PERSON> from "swr";
import { tUserS } from "types/tutor";
import { FcSearch } from "react-icons/fc";
import styles from "styles/FilterPopup.module.css";

type Props = {
  isOpen: Boolean;
  onChange: any;
  searchValue: string;
  apiRoute: string;
  nullFTest?: boolean;
};

export default function InputSelector({
  isOpen,
  onChange,
  searchValue,
  apiRoute,
  nullFTest,
}: Props) {
  const [isSearching, setIsSearching] = useState<Boolean>(false);
  const { data, error } = useSWR<Array<tUserS>>(
    `${apiRoute}?name=${searchValue}`,
    fetcher
  );
  useEffect(() => {
    if (!data && !error) setIsSearching(true);
    else setIsSearching(false);
  }, [data, error]);

  if (isOpen)
    return (
      <>
        <SearchLoading isSearching={isSearching} />
        <div className={styles.selectListContainer}>
          {!nullFTest && (
            <div
              className={styles.List}
              onClick={() =>
                onChange({ id: "", fullName: "All", fullname: "All" })
              }
            >
              All
            </div>
          )}

          {data?.map((user: any) => {
            return (
              <div
                key={user.id}
                className={styles.List}
                onClick={() => onChange(user)}
              >
                {user.fullName ? user.fullName : user.fullname}
              </div>
            );
          })}
        </div>
      </>
    );
  else
    return (
      <>
        <SearchLoading isSearching={isSearching} />
      </>
    );
}

function SearchLoading(props: any) {
  if (props.isSearching)
    return (
      <div className={styles.serchingIcon}>
        <FcSearch />
      </div>
    );
  else return null;
}
