"use client";

import React, { useEffect, useRef, useState } from "react";
import Avatar from "react-avatar";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import { ToggleGroup, ToggleGroupItem } from "@components/ui/toggle-group";

import { Input } from "@components/ui/input";
import { useForm } from "react-hook-form";
import { Dots } from "react-activity";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { Button } from "@components/ui/button";
import { HiPencil } from "react-icons/hi";
import InputSelectorForModal from "@components/modals/InputSelectorForModal";
import { tLftD, tSuggestedL } from "types/lft";
import { BooleanToString, StringToBoolean } from "core/Converters";
import Link from "next/link";
import { getSession } from "next-auth/react";
import { TbReplace } from "react-icons/tb";
import { tStudentL } from "types/student";
import useSWR from "swr";
import { tChildF } from "types/dashboard";
import { fetcher } from "@fetchers/fetcher";
import moment from "moment";

import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";
import Image from "next/image";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { tTutorL } from "types/tutor";
import { GeoCoding } from "core/Map/Map";
import { GenerateQuery } from "core/Query";
import { FaFemale, FaMale, FaPlus } from "react-icons/fa";

type Props = {
  lft: tLftD;
  mutate: () => void;
  mutateStatus: () => void;
};

export default function LftDetailsList({ lft, mutate, mutateStatus }: Props) {
  return (
    <div className="relative mt-6 grid h-fit w-full grid-cols-5 gap-x-4 rounded-md border border-slate-200 bg-white py-6 pl-4 pr-6">
      <div className="absolute right-1.5 top-1.5">
        <UpdateDetailsList
          lft={lft}
          mutate={mutate}
          mutateStatus={mutateStatus}
        />
      </div>

      <div className="space-y-1.5">
        <div className="text-xs font-semibold text-slate-600">Lesson Type</div>
        <div className="text-[10.5px] font-medium text-slate-500">
          {lft.lessonType ? lft.lessonType : "Unsure"}
        </div>
      </div>

      <div className="space-y-1.5">
        <div className="text-xs font-semibold text-slate-600">Last Spoken</div>
        <div className="text-[10.5px] font-medium text-slate-500">
          {lft.lastSpoken ? lft.lastSpoken : "Not Given"}
        </div>
      </div>

      <div className="space-y-1.5">
        <div className="text-xs font-semibold text-slate-600">
          Event Launch Date
        </div>
        <div className="text-[10.5px] font-medium text-slate-500">
          {lft.launchDate ? lft.launchDate : "Not Given"}
        </div>
      </div>

      <div className="space-y-1.5">
        <div className="text-xs font-semibold text-slate-600">Event Info</div>

        <div className="text-[10.5px] font-medium text-slate-500">
          {lft.sendEvents ? "Include" : "Do Not Include"}
        </div>
      </div>

      <div className="space-y-1.5">
        <div className=" text-xs font-semibold text-slate-600">Instrument</div>
        <div className="max-w-[96px] overflow-hidden truncate text-[10.5px] font-medium text-slate-500">
          {lft.instrument ? lft.instrument : "Not Given"}
        </div>
      </div>
    </div>
  );
}

export function LftStudent({ lft, mutate, mutateStatus }: Props) {
  return (
    <div className="relative flex h-56 w-56 flex-col items-center justify-center rounded-md border border-slate-200 bg-white">
      <div className="absolute right-8 top-1.5">
        <ReplaceStudent lft={lft} mutate={mutate} mutateStatus={mutateStatus} />
      </div>
      <div className="absolute right-1.5 top-1.5">
        <UpdateStudent lft={lft} mutate={mutate} mutateStatus={mutateStatus} />
      </div>

      <Avatar name={lft.student.fullName} round={true} size="80" />
      <div className=" flex flex-row items-center">
        <Link
          href={`/students/${lft.student.id}`}
          target="_blank"
          className="my-0.5 block max-w-[144px]  overflow-hidden truncate text-sm font-semibold text-slate-600 hover:cursor-pointer hover:underline "
        >
          {lft.student.fullName}
        </Link>

        {lft.student.country && (
          <Image
            src={lft.student.country === "United Kingdom" ? UKFlag : USFlag}
            alt={lft.student.country}
            className="ml-0.5 h-4 w-4"
          />
        )}
      </div>

      <div className="mb-0.5 text-xs font-normal text-slate-600">
        {lft.student.postCode}
      </div>

      <div className="mb-0.5 text-[10px] font-light text-slate-600">
        {lft.student.phoneNumber}
      </div>
      <div className="text-[9px] font-light text-slate-600">
        {lft.learnerType}
      </div>
    </div>
  );
}

export function LftChild({ lft, mutate, mutateStatus }: Props) {
  return (
    <div className="relative flex h-56 w-56 flex-col items-center justify-center rounded-md border border-slate-200 bg-white">
      <div className="absolute right-1.5 top-1.5">
        <UpdateChild lft={lft} mutate={mutate} mutateStatus={mutateStatus} />
      </div>

      <Avatar name={lft.student.fullName} round={true} size="80" />

      <div className="my-0.5 text-sm font-semibold text-slate-600 hover:cursor-pointer hover:underline ">
        {lft.child?.name}
      </div>

      <div className="mb-0.5 text-xs font-normal text-slate-600">
        {lft.child?.pronouns}
      </div>

      <div className="mb-0.5 text-[10px] font-light text-slate-600">
        {lft.child?.age}
      </div>
    </div>
  );
}

export function LftHomeTutor({ lft, mutate, mutateStatus }: Props) {
  return (
    <div className="relative flex h-56 w-56 flex-col items-center justify-center rounded-md border border-slate-200 bg-white">
      <div className="absolute right-2 top-1.5">
        <UpdateHomeTutor
          lft={lft}
          mutate={mutate}
          mutateStatus={mutateStatus}
        />
      </div>

      <div className="absolute right-8 top-1.5">
        <AddHomeTutor
          isReplaceTutor
          lft={lft}
          mutate={mutate}
          mutateStatus={mutateStatus}
        />
      </div>

      <Avatar name={lft.homeTutor?.fullname} round={true} size="80" />

      <Link
        href={`/tutors/${lft.homeTutor?.id}`}
        target="_blank"
        className="my-0.5 block max-w-[160px]  overflow-hidden truncate text-sm font-semibold text-slate-600 hover:cursor-pointer hover:underline "
      >
        {lft.homeTutor?.fullname}
      </Link>

      <div className="mb-0.5 text-xs font-medium text-slate-600">
        {lft.homeTutor?.country}
      </div>
      <div className="mb-1.5 text-[10px] font-light text-slate-600">
        {lft.homeTutor?.gender}
      </div>

      <div className="flex items-center space-x-2">
        {lft.homeTutor?.cv && (
          <Link
            href={lft.homeTutor?.cv}
            target="_blank"
            className={`flex h-5 cursor-pointer flex-col items-center justify-center rounded border px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200 ${
              lft.homeTutor.preferredCv === "Tutor"
                ? "border-green-400"
                : "border-slate-200"
            }`}
          >
            CV
          </Link>
        )}
        {lft.homeTutor?.dbs && (
          <Link
            href={lft.homeTutor?.dbs}
            target="_blank"
            className="flex h-5 cursor-pointer flex-col items-center justify-center rounded border border-slate-200 px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200"
          >
            DBS
          </Link>
        )}
        {lft.homeTutor?.adminCv && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href={lft.homeTutor.adminCv}
                  target="_blank"
                  className={`flex h-5 cursor-pointer flex-col items-center justify-center rounded border px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200 ${
                    lft.homeTutor.preferredCv === "Custom"
                      ? "border-green-400"
                      : "border-slate-200"
                  }`}
                >
                  Admin CV
                </Link>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm break-words rounded border  bg-slate-400 px-2 py-1 text-[10px]  font-medium text-slate-50">
                {moment(lft.homeTutor.lastCvUpload)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .fromNow()}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
}

export function LftOnlineTutor({ lft, mutate, mutateStatus }: Props) {
  return (
    <div className="relative flex h-56 w-56 flex-col items-center justify-center rounded-md border border-slate-200 bg-white">
      <div className="absolute right-2 top-1.5">
        <UpdateOnlineTutor
          lft={lft}
          mutate={mutate}
          mutateStatus={mutateStatus}
        />
      </div>

      <div className="absolute right-8 top-1.5">
        <AddOnlineTutor
          isReplaceTutor
          lft={lft}
          mutate={mutate}
          mutateStatus={mutateStatus}
        />
      </div>

      <Avatar name={lft.onlineTutor?.fullname} round={true} size="80" />

      <Link
        href={`/tutors/${lft.onlineTutor?.id}`}
        target="_blank"
        className="my-0.5 block max-w-[160px]  overflow-hidden truncate text-sm font-semibold text-slate-600 hover:cursor-pointer hover:underline "
      >
        {lft.onlineTutor?.fullname}
      </Link>

      <div className="mb-0.5 text-xs font-medium text-slate-600">
        {lft.onlineTutor?.country}
      </div>
      <div className="mb-1.5 text-[10px] font-light text-slate-600">
        {lft.onlineTutor?.gender}
      </div>

      <div className="flex items-center space-x-2">
        {lft.onlineTutor?.cv && (
          <Link
            href={lft.onlineTutor?.cv}
            target="_blank"
            className={`flex h-5 cursor-pointer flex-col items-center justify-center rounded border px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200 ${
              lft.onlineTutor.preferredCv === "Tutor"
                ? "border-green-400"
                : "border-slate-200"
            }`}
          >
            CV
          </Link>
        )}
        {lft.onlineTutor?.dbs && (
          <Link
            href={lft.onlineTutor.dbs}
            target="_blank"
            className="flex h-5 cursor-pointer flex-col items-center justify-center rounded border border-slate-200 px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200"
          >
            DBS
          </Link>
        )}

        {lft.onlineTutor?.adminCv && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href={lft.onlineTutor.adminCv}
                  target="_blank"
                  className={`flex h-5 cursor-pointer flex-col items-center justify-center rounded border px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200 ${
                    lft.onlineTutor.preferredCv === "Custom"
                      ? "border-green-400"
                      : "border-slate-200"
                  }`}
                >
                  Admin CV
                </Link>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm break-words rounded border  bg-slate-400 px-2 py-1 text-[10px]  font-medium text-slate-50">
                {moment(lft.onlineTutor.lastCvUpload)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .fromNow()}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
}

function UpdateDetailsList({ lft, mutate, mutateStatus }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    lessonType: yup.string().required("Lesson type is required!"),
    Instrument: yup.string().required("Instrument is required!"),
    sendEvents: yup.string().required("Send events type is required!"),
    lastSpoken: yup.string().required("last spoken is required!"),
  });

  const defaultValues = {
    lessonType: lft.lessonType,
    Instrument: lft.instrument,
    offerInstrument: BooleanToString(lft.offerInstrument),
    sendEvents: BooleanToString(lft.sendEvents),
    lastSpoken: lft.lastSpoken,

    launchDate: lft.launchDate,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    //  console.log(data);
    try {
      const Session = await getSession();
      const token = Session?.token;

      const { offerInstrument, sendEvents, sendCourses } = data;

      // const Test = {
      //   ...data,
      //   offerInstrument: StringToBoolean(offerInstrument),
      //   sendEvents: StringToBoolean(sendEvents),
      //   sendCourses: StringToBoolean(sendCourses),
      // };

      // console.log(Test);

      const response = await api.put(
        `/lft/${lft.id}/`,
        {
          ...data,
          offerInstrument: StringToBoolean(offerInstrument),
          sendEvents: StringToBoolean(sendEvents),
          sendCourses: StringToBoolean(sendCourses),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        mutateStatus();
        setIsLoading(false);
        setIsOpen(false);

        toast.success("LFT details updated successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error, "Error");
      toast.error("Failed to update LFT details!");
    }
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <HiPencil size={18} className="cursor-pointer text-slate-600" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Lft Details</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-3.5">
              <FormField
                control={form.control}
                name="lessonType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lesson Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value ? field.value : "Select Lesson Type"}
                          {/* : "Select Lesson Type"} */}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Unsure">Unsure</SelectItem>
                        <SelectItem value="Online">Online</SelectItem>
                        <SelectItem value="In-Person">In-Person</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastSpoken"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Spoken</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter last spoken"
                        {...field}
                        className="text-xs font-normal text-gray-800"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="launchDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Event Launch Date</FormLabel>
                    <FormDescription>Starting from _____</FormDescription>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter launch date"
                        {...field}
                        className="text-xs font-normal text-gray-800"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="Instrument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instrument</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value ? field.value : "Select An Instrument"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="hide-scrollbar max-h-52 overflow-y-scroll py-1">
                        <SelectGroup>
                          <SelectItem value="ABRSM Music Theory Grades">
                            ABRSM Music Theory Grades
                          </SelectItem>
                          <SelectItem value="Bass Guitar">
                            Bass Guitar
                          </SelectItem>
                          <SelectItem value="Cello">Cello</SelectItem>
                          <SelectItem value="Clarinet">Clarinet</SelectItem>
                          <SelectItem value="Double Bass">
                            Double Bass
                          </SelectItem>
                          <SelectItem value="Drums">Drums</SelectItem>
                          <SelectItem value="Flute">Flute</SelectItem>
                          <SelectItem value="French Horn">
                            French Horn
                          </SelectItem>
                          <SelectItem value="Guitar">Guitar</SelectItem>
                          <SelectItem value="Harp">Harp</SelectItem>
                          <SelectItem value="Jazz Piano">Jazz Piano</SelectItem>

                          <SelectItem value="Oboe">Oboe</SelectItem>
                          <SelectItem value="Piano">Piano</SelectItem>

                          <SelectItem value="Sax">Sax</SelectItem>
                          <SelectItem value="Singing">Singing</SelectItem>
                          <SelectItem value="Trombone">Trombone</SelectItem>
                          <SelectItem value="Trumpet">Trumpet</SelectItem>
                          <SelectItem value="Viola">Viola</SelectItem>
                          <SelectItem value="Violin">Violin</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="offerInstrument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Offer Instrument Purchase</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value === "true" ? "Yes" : "No"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sendEvents"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Send Events</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value === "true" ? "Yes" : "No"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UpdateStudent({ lft, mutate, mutateStatus }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    learnerType: yup.string().required("Learner type is required!"),
  });

  const defaultValues = {
    learnerType: lft.learnerType,
    phoneNumber: lft.student.phoneNumber,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/lft/${lft.id}/student`,
        {
          ...data,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        mutateStatus();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Student information updated successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update student information!");
    }
  };
  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <HiPencil size={18} className="cursor-pointer text-slate-600" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Student Info</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-3.5">
              <FormField
                control={form.control}
                name="learnerType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Learner Type </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("learnerType")
                            ? form.getValues("learnerType")
                            : "Select Learner Type"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Adult">Adult</SelectItem>
                        <SelectItem value="Single Child">
                          Single Child
                        </SelectItem>
                        <SelectItem value="Multiple Children">
                          Multiple Children
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormDescription>
                      Please enter the phone number without the country code
                      (e.g., omit '+44' for UK numbers).
                    </FormDescription>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter phone number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function ReplaceStudent({ lft, mutate, mutateStatus }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [student, setStudent] = useState<string>(lft.student.fullName);
  const [seletedStudentId, setSeletedStudentId] = useState<string>(
    lft.student.id,
  );

  const [studentNotFound, setStudentNotFound] = useState<boolean>(false);

  const {
    data: Children,
    mutate: mutateC,
    isLoading: isLoadingC,
  } = useSWR<Array<tChildF>>(
    seletedStudentId ? `/student/${seletedStudentId}/children` : null,
    fetcher,
  );

  var resolver = yup.object().shape({
    childId: yup.string().required("Child is required!"),
  });

  const defaultValues = {
    childId: lft.child?.id,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      setStudent(lft.student.fullName);
      setSeletedStudentId(lft.student.id);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
    makeAllValueDefault: () => {
      setIsLoading(false);
      setIsOpen(false);
      setStudent("");
      setSeletedStudentId("");
      setStudentNotFound(false);
    },
  };

  const StuSearchHandelar = {
    Search: (e: any) => {
      setStudent(e.target.value);
    },

    Select: (student: tStudentL) => {
      setStudent(student.fullname);
      setSeletedStudentId(student.id);
      setIsFocused(false);
      setStudentNotFound(false);
    },
  };

  const onSubmit = async (data: any) => {
    if (!seletedStudentId) {
      setStudentNotFound(true);
    } else {
      setIsLoading(true);
      console.log(data, seletedStudentId);
      try {
        const Session = await getSession();
        const token = Session?.token;
        const response = await api.put(
          `/lft/${lft.id}/replace-student`,
          {
            ...data,
            studentId: seletedStudentId,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        );

        if (response.status === 200) {
          mutate();
          mutateC();
          mutateStatus();

          toast.success(
            `${lft.student.fullName} has been successfully replaced with ${student}!`,
          );
          ModalControl.makeAllValueDefault();
        } else {
          toast.error("Unexpected response from the server!");
        }
      } catch (error: any) {
        setIsLoading(false);
        toast.error(`Failed to replace student. Please try again!`);
      }
    }
  };

  useEffect(() => {
    if (!student) {
      setSeletedStudentId("");
    }
  }, [student]);

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <TbReplace size={15} className="cursor-pointer text-slate-700" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Replace Student</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <div className="">
                <div
                  className={`pb-1 text-sm font-medium  ${
                    studentNotFound ? "text-destructive" : "text-gray-800"
                  }`}
                >
                  Find A Student
                </div>
                <Input
                  type="search"
                  value={student}
                  onChange={StuSearchHandelar.Search}
                  onFocus={() => setIsFocused(true)}
                  className="text-xs text-gray-800"
                />

                {student && (
                  <InputSelectorForModal
                    isOpen={isFocused}
                    searchValue={student}
                    onChange={StuSearchHandelar.Select}
                    apiRoute="/student/search"
                  />
                )}
                {studentNotFound && (
                  <div className="mt-1.5 text-xs font-medium text-destructive">
                    Student is required!
                  </div>
                )}
              </div>

              <FormField
                control={form.control}
                name="childId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select A Child</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full  bg-white">
                          <div className="text-xs font-normal text-gray-700">
                            {(Children &&
                              Children.find((c) => c.id === field.value)
                                ?.name) ||
                              "Select Child"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="text-xs font-medium text-gray-700">
                        <SelectGroup>
                          {Children && Children.length === 0 && (
                            <div className="p-1 text-[10px] font-medium text-blue-600">
                              No children found. Please add a child first!
                            </div>
                          )}
                          {Children &&
                            Children.map((child: any) => {
                              return (
                                <SelectItem key={child.id} value={child.id}>
                                  {child?.name}
                                </SelectItem>
                              );
                            })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* <FormField
                control={form.control}
                name="childId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select A Child</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full  bg-white">
                          <div className="text-xs font-normal text-gray-700">
                            {form.getValues("childId")
                              ? (Children &&
                                  Children.find(
                                    (c) => c.id === form.getValues("childId"),
                                  )?.name) ||
                                "Child not found"
                              : "Select Child"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="text-xs font-medium text-gray-700">
                        <SelectGroup>
                          {Children && Children.length === 0 && (
                            <div className="p-1 text-[10px] font-medium text-blue-600">
                              No children found. Please add a child first!
                            </div>
                          )}
                          {Children &&
                            Children.map((child: any) => {
                              return (
                                <SelectItem key={child.id} value={child.id}>
                                  {child?.name}
                                </SelectItem>
                              );
                            })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => {
                  form.reset(defaultValues), setStudent(lft.student.fullName);
                }}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Replace"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UpdateChild({ lft, mutate, mutateStatus }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const {
    data: Children,
    mutate: mutateC,
    isLoading: isLoadingC,
  } = useSWR<Array<tChildF>>(`/student/${lft.id}/children`, fetcher);

  var resolver = yup.object().shape({
    name: yup.string().required("Child name type is required!"),
  });

  const defaultValues = {
    name: lft.child?.name,
    pronouns: lft.child?.pronouns,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/lft/${lft.id}/child`,
        {
          ...data,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        mutateC();
        mutateStatus();

        setIsLoading(false);
        setIsOpen(false);
        toast.success("Child information updated successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update child information!");
    }
  };
  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <HiPencil size={18} className="cursor-pointer text-slate-600" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Child Info</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Child Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter child name"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pronouns"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pronouns </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value ? field.value : "Select pronouns"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="He">He</SelectItem>
                        <SelectItem value="She">She</SelectItem>
                        <SelectItem value="They">They</SelectItem>
                        <SelectItem value="You">You</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UpdateHomeTutor({ lft, mutate, mutateStatus }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    gender: yup.string().required("Gender is required!"),
    preferredCv: yup.string().required("Preferred cv required!"),
  });

  const defaultValues = {
    gender: lft.homeTutor?.gender,
    preferredCv: lft.homeTutor?.preferredCv,
    customCv: lft.homeTutor?.adminCv,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const inputFileRef = useRef<any>(undefined);

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    // console.log(data);

    const formData = new FormData();

    if (data.customCv) {
      formData.append("customCv", inputFileRef.current.files[0]);
    }

    formData.append("gender", data.gender);
    formData.append("preferredCv", data.preferredCv);

    // for (const value of formData.values()) {
    //   console.log(value);
    // }

    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(`/lft/${lft.id}/home-tutor`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        setIsLoading(false);
        mutate();
        mutateStatus();
        setIsOpen(false);
        toast.success("Home tutor details successfully updated in LFT!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update home tutor details in LFT!");
    }
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <HiPencil size={18} className="cursor-pointer text-slate-600" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-md ">
        <DialogHeader>
          <DialogTitle>Update Home Tutor</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className="capitalize">
                            {field.value ? field.value : "Select gender"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preferredCv"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred CV</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value
                            ? field.value === "Tutor"
                              ? "Tutor Version"
                              : field.value
                            : "Select Preferred CV "}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Tutor">Tutor Version</SelectItem>
                        <SelectItem value="Custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customCv"
                render={({ field: { onChange, onBlur, name, ref } }) => (
                  <FormItem>
                    <FormLabel>Upload Custom Cv</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        placeholder="Select a file to upload"
                        name={name}
                        onChange={onChange}
                        onBlur={onBlur}
                        ref={inputFileRef}
                        className="text-xs font-normal text-gray-800"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6 flex w-full flex-row items-center justify-between space-x-2">
              <DeleteTutor
                mutate={mutate}
                mutateStatus={mutateStatus}
                apiUrl={`/lft/${lft.id}/home-tutor`}
                whichTutor="home tutor"
                tutorName={lft.homeTutor?.fullname}
                successMsg={`Home tutor (${lft.homeTutor?.fullname}) has been successfully deleted!`}
                erroMsg={`Failed to delete home tutor (${lft.homeTutor?.fullname}). Please try again!`}
              />
              <div>
                <Button
                  type="reset"
                  onClick={() => {
                    form.reset(defaultValues);
                  }}
                  className="border border-black bg-white text-black hover:bg-black hover:text-white "
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                >
                  {isLoading ? <Dots /> : "Update"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UpdateOnlineTutor({ lft, mutate, mutateStatus }: Props) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    gender: yup.string().required("Gender is required!"),
    preferredCv: yup.string().required("Preferred cv required!"),
  });

  const defaultValues = {
    gender: lft.onlineTutor?.gender,
    preferredCv: lft.onlineTutor?.preferredCv,
    customCv: lft.onlineTutor?.adminCv,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const inputFileRef = useRef<any>(undefined);

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    const formData = new FormData();

    if (data.customCv) {
      formData.append("customCv", inputFileRef.current.files[0]);
    }

    formData.append("gender", data.gender);
    formData.append("preferredCv", data.preferredCv);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(`/lft/${lft.id}/online-tutor`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        setIsLoading(false);
        mutate();
        mutateStatus();
        setIsOpen(false);
        toast.success("Online tutor details successfully updated in LFT!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update online tutor details in LFT!");
    }
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <HiPencil size={18} className="cursor-pointer text-slate-600" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Online Tutor</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className=" space-y-2.5">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className="capitalize">
                            {field.value ? field.value : "Select gender"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preferredCv"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred CV</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value
                            ? field.value === "Tutor"
                              ? "Tutor Version"
                              : field.value
                            : "Select Preferred CV "}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Tutor">Tutor Version</SelectItem>
                        <SelectItem value="Custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customCv"
                render={({ field: { onChange, onBlur, name, ref } }) => (
                  <FormItem>
                    <FormLabel>Upload Custom Cv</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        placeholder="Select a file to upload"
                        name={name}
                        onChange={onChange}
                        onBlur={onBlur}
                        ref={inputFileRef}
                        className="text-xs font-normal text-gray-800"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6 flex w-full flex-row items-center justify-between space-x-2">
              <DeleteTutor
                mutate={mutate}
                mutateStatus={mutateStatus}
                apiUrl={`/lft/${lft.id}/online-tutor`}
                whichTutor="online tutor"
                tutorName={lft.onlineTutor?.fullname}
                successMsg={`Online tutor (${lft.onlineTutor?.fullname}) has been successfully deleted!`}
                erroMsg={`Failed to delete online tutor (${lft.onlineTutor?.fullname}). Please try again!`}
              />
              <div>
                <Button
                  type="reset"
                  onClick={() => {
                    form.reset(defaultValues);
                  }}
                  className="border border-black bg-white text-black hover:bg-black hover:text-white "
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                >
                  {isLoading ? <Dots /> : "Update"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

type DelProps = {
  // lft: tLftD;
  mutate: () => void;
  mutateStatus: () => void;
  apiUrl: string;
  successMsg: string;
  erroMsg: string;
  tutorName?: string;
  whichTutor?: string;
};

function DeleteTutor({
  // lft,
  mutate,
  mutateStatus,
  apiUrl,
  tutorName,
  whichTutor,
  successMsg,
  erroMsg,
}: DelProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDelete = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      let res = await api.delete(apiUrl, {
        headers: {
          Authorization: "Bearer " + token,
        },
      });

      if (res.status === 200) {
        mutate();
        mutateStatus();
        setIsLoading(false);
        toast.success(successMsg);
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(erroMsg);
    }
  };
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          type="button"
          onClick={() => {}}
          className="border border-destructive bg-destructive text-white hover:bg-white hover:text-destructive"
        >
          Remove Tutor
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            {`This action is irreversible and will remove ${tutorName} as the ${whichTutor} for this LFT.`}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

type AddTutorProps = {
  isReplaceTutor?: boolean;
  lft: tLftD;
  mutate: () => void;
  mutateStatus: () => void;
};

export function AddHomeTutor({
  isReplaceTutor,
  lft,
  mutate,
  mutateStatus,
}: AddTutorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [tutor, setTutor] = useState<string>("");
  const [seletedTutorId, setSeletedTutorId] = useState<string>("");
  const [tutorNotFound, setTutorNotFound] = useState<boolean>(false);
  const [isSuggested, setIsSuggested] = useState<boolean>(false);
  const [locNotFound, setLocNotFound] = useState<boolean>(false);

  //Filter Suggested Tutor List ...
  const [query, setQuery] = useState<string>("");
  const [gender, setGender] = useState<string>("All");
  const [students, setStudents] = useState<string>("15");
  const [radius, setRadius] = useState<string>("5");

  const HomeTutorsKey = lft.student.id
    ? `/lft/${lft.id}/home-tutors?p=1${query}`
    : null;

  const {
    data: HomeTutors,
    mutate: mutateHT,
    isLoading: isLoadingHT,
  } = useSWR<Array<tSuggestedL>>(lft ? HomeTutorsKey : null, fetcher);

  const ModalControl = {
    reset: (value: any) => {
      mutate();
      mutateHT();

      setIsOpen(value);

      setGender("All");
      setStudents("2");
      setRadius("5");
    },
    openModal: () => {
      setIsOpen(true);
    },
    makeAllValueDefault: () => {
      setIsLoading(false);
      setIsOpen(false);
      setTutor("");
      setSeletedTutorId("");
      setTutorNotFound(false);
      setIsSuggested(false);
    },
  };

  const TSearchHandelar = {
    Search: (e: any) => {
      setTutor(e.target.value);
    },

    Select: (tutor: tTutorL) => {
      setTutor(tutor.fullName);
      setSeletedTutorId(tutor.id);

      setIsFocused(false);
      setTutorNotFound(false);
      setIsSuggested(false);
    },
  };

  const FilterHandelar = {
    Gender: (e: any) => {
      setGender(e);
    },
    Students: (e: any) => {
      setStudents(e);
    },
    Radius: (e: any) => {
      setRadius(e);
    },
  };

  const SelectTutorFromSuggestion = (tutor: tSuggestedL) => {
    setTutor(tutor.fullName);
    setSeletedTutorId(tutor.id);
    setIsSuggested(true);

    setIsFocused(false);
    setTutorNotFound(false);
  };

  const Action = {
    UpdateQuery: (query: string) => {
      setQuery(query);
    },
    Submit: async () => {
      if (!seletedTutorId) {
        setTutorNotFound(true);
      } else {
        try {
          setIsLoading(true);

          const Session = await getSession();
          const token = Session?.token;
          const response = await api.put(
            `/lft/${lft.id}/replace-home-tutor`,
            {
              id: seletedTutorId,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            },
          );

          if (response.status === 200) {
            mutate();
            mutateStatus();
            ModalControl.makeAllValueDefault();
            toast.success(
              `Home Tutor ${
                isReplaceTutor ? "has been replaced" : "added"
              } successfully!`,
            );
          } else {
            toast.error("Unexpected response from the server!");
          }
        } catch (error: any) {
          setIsLoading(false);

          toast.error(
            `Failed to ${
              isReplaceTutor ? "replace" : "add"
            } home tutor. Please try again!`,
          );
        }
      }
    },
  };

  const generateQuery = async () => {
    var loc: { lat: number; lng: number } = await GeoCoding(
      lft.student.postCode,
    );

    if (!loc) {
      setLocNotFound(true);
      return;
    }

    setLocNotFound(false);

    Action.UpdateQuery(
      GenerateQuery({
        gender: gender === "All" ? "" : gender,
        students: Number(students),
        lat: loc.lat,
        lng: loc.lng,
        radius: Number(radius),
      }),
    );
  };

  useEffect(() => {
    if (!tutor) {
      setSeletedTutorId("");
    }
    if (isSuggested && seletedTutorId) {
      Action.Submit();
    }
  }, [tutor]);

  useEffect(() => {
    generateQuery();
  }, [gender, students, radius, lft.student.postCode]);

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        {isReplaceTutor ? (
          <TbReplace size={15} className="cursor-pointer text-slate-700" />
        ) : (
          <div className="flex h-24 w-24 cursor-pointer items-center justify-center rounded-full bg-slate-400">
            <FaPlus size={28} color="white" />
          </div>
        )}
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>
            {isReplaceTutor ? "Replace Home Tutor" : "Add Home Tutor"}
          </DialogTitle>

          <DialogDescription className="text-center">
            {lft.homeTutor?.fullname}
          </DialogDescription>
        </DialogHeader>

        <div>
          <div>
            <>
              <div
                className={`pb-1 text-sm font-medium  ${
                  tutorNotFound ? "text-destructive" : "text-gray-800"
                }`}
              >
                Find A Tutor
              </div>
              <Input
                type="search"
                value={tutor}
                onChange={TSearchHandelar.Search}
                onFocus={() => setIsFocused(true)}
                className="text-xs text-gray-800"
              />

              {tutor && (
                <InputSelectorForModal
                  isOpen={isFocused}
                  searchValue={tutor}
                  onChange={TSearchHandelar.Select}
                  apiRoute="/tutor/search"
                />
              )}
              {tutorNotFound && (
                <div className="mt-1.5 text-xs font-medium text-destructive">
                  Tutor is required!
                </div>
              )}
            </>

            <div className="mt-6">
              <div className={`text-base font-medium text-slate-600`}>
                Suggestions
              </div>
              {locNotFound && (
                <div className="mt-0.5 text-[10px] font-light text-destructive">
                  Failed to verify your postcode.
                </div>
              )}

              <div className="my-4 flex items-center justify-between">
                <div>
                  <div className="mb-1 text-xs font-semibold text-slate-600">
                    Radius
                  </div>

                  <ToggleGroup
                    type="single"
                    defaultValue={radius}
                    onValueChange={FilterHandelar.Radius}
                    className="h-fit rounded-md border border-slate-200 bg-white"
                  >
                    <ToggleGroupItem value="0">
                      <div className="text-xs font-normal text-slate-600">
                        All
                      </div>
                    </ToggleGroupItem>
                    <ToggleGroupItem value="5">
                      <div className="text-xs font-normal text-slate-600">
                        5km
                      </div>
                    </ToggleGroupItem>
                    <ToggleGroupItem value="10">
                      <div className="text-xs font-normal text-slate-600">
                        10km
                      </div>
                    </ToggleGroupItem>
                  </ToggleGroup>
                </div>

                <div className="flex items-center space-x-2.5">
                  <div>
                    <div className="mb-1 text-xs font-semibold text-slate-600">
                      Gender
                    </div>

                    <ToggleGroup
                      type="single"
                      defaultValue={gender}
                      onValueChange={FilterHandelar.Gender}
                      className="h-fit rounded-md border border-slate-200 bg-white"
                    >
                      <ToggleGroupItem value="All">
                        <div className="text-xs font-medium text-slate-500">
                          All
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="Male">
                        <FaMale size={15} />
                      </ToggleGroupItem>
                      <ToggleGroupItem value="Female">
                        <FaFemale size={15} color="#475569" />
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </div>

                  <div>
                    <div className="mb-1 text-xs font-semibold text-slate-600">
                      Max Student
                    </div>
                    <ToggleGroup
                      type="single"
                      defaultValue={students}
                      onValueChange={FilterHandelar.Students}
                      className="h-fit rounded-md border border-slate-200 bg-white"
                    >
                      <ToggleGroupItem value="0" aria-label="Toggle bold">
                        <div className="text-xs font-normal text-slate-600">
                          All
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="2" aria-label="Toggle italic">
                        <div className="text-xs font-normal text-slate-600">
                          2
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="3" aria-label="Toggle italic">
                        <div className="text-xs font-normal text-slate-600">
                          3
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="5" aria-label="Toggle italic">
                        <div className="text-xs font-normal text-slate-600">
                          5
                        </div>
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </div>
                </div>
              </div>

              {HomeTutors && HomeTutors?.length < 1 ? (
                <div className="pt-2.5 text-sm text-slate-600">
                  {` ${
                    lft.instrument
                      ? "Tutor suggestion not available."
                      : "Please update the instrument in order to get Tutor Suggestions."
                  }`}
                </div>
              ) : (
                <div className="mb-1.5 grid grid-cols-3 items-center gap-x-2.5">
                  <div className="col-span-2 space-y-0.5">
                    <div className="text-xs font-semibold text-slate-600">
                      Tutor Name
                    </div>

                    <div className="text-[10px] font-normal text-slate-500">
                      Tutor Email
                    </div>
                  </div>

                  <div className="space-y-0.5">
                    <div className="text-xs font-semibold text-slate-600">
                      Active Student
                    </div>

                    <div className="text-[10px] font-normal text-slate-500">
                      Rank
                    </div>
                  </div>
                </div>
              )}
              {isLoadingHT && <ListSkeltons count={3} height={50} />}

              {HomeTutors &&
                HomeTutors?.map((tutor: tSuggestedL) => (
                  <div
                    onClick={() => SelectTutorFromSuggestion(tutor)}
                    key={tutor.id}
                    className="mb-1.5 grid h-fit w-full cursor-pointer grid-cols-3 items-center gap-x-2.5 rounded-md border border-slate-200 bg-white py-2.5 hover:border-slate-300 hover:bg-slate-100"
                  >
                    <div className="col-span-2 flex items-center pl-1.5">
                      <Avatar name={tutor.fullName} round={true} size="42" />

                      <div className="space-y-0.5 pl-1.5">
                        <div className="max-w-[160px] overflow-hidden truncate text-xs font-semibold text-slate-700">
                          {tutor.fullName}
                        </div>
                        <div className="max-w-[160px] overflow-hidden truncate text-[10px] font-light text-slate-600">
                          {tutor.email}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-0.5">
                      <div className="max-w-[90px] overflow-hidden truncate text-xs font-medium text-slate-600">
                        {tutor.students}
                      </div>

                      <div className="max-w-[96px] overflow-hidden truncate text-[10px] font-light text-slate-600">
                        {tutor.rank}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          <div className="mt-4  flex w-full flex-row justify-end space-x-1">
            <Button
              type="submit"
              onClick={Action.Submit}
              className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
            >
              {isLoading ? <Dots /> : isReplaceTutor ? "Replace" : "Submit"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export function AddOnlineTutor({
  isReplaceTutor,
  lft,
  mutate,
  mutateStatus,
}: AddTutorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [tutor, setTutor] = useState<string>("");
  const [seletedTutorId, setSeletedTutorId] = useState<string>("");
  const [tutorNotFound, setTutorNotFound] = useState<boolean>(false);
  const [isSuggested, setIsSuggested] = useState<boolean>(false);

  //Filter Suggested Tutor List ...
  const [query, setQuery] = useState<string>("");
  const [gender, setGender] = useState<string>("All");
  const [students, setStudents] = useState<string>("15");

  const onlineTutorsKey = lft?.student.id
    ? `/lft/${lft.id}/online-tutors?p=1${query}`
    : null;

  const {
    data: OnlinTutors,
    mutate: mutateOT,
    isLoading: isLoadingOT,
  } = useSWR<Array<tSuggestedL>>(lft ? onlineTutorsKey : null, fetcher);

  const ModalControl = {
    reset: (value: any) => {
      mutate();
      mutateOT();

      setIsOpen(value);

      setGender("All");
      setStudents("15");
    },
    openModal: () => {
      setIsOpen(true);
    },
    makeAllValueDefault: () => {
      setIsLoading(false);
      setIsOpen(false);
      setTutor("");
      setSeletedTutorId("");
      setTutorNotFound(false);
      setIsSuggested(false);
    },
  };

  const TutorSearchHandelar = {
    Search: (e: any) => {
      setTutor(e.target.value);
    },

    Select: (tutor: tTutorL) => {
      setTutor(tutor.fullName);
      setSeletedTutorId(tutor.id);

      setIsFocused(false);
      setTutorNotFound(false);
      setIsSuggested(false);
    },
  };

  const FilterHandelar = {
    Gender: (e: any) => {
      setGender(e);
    },
    Students: (e: any) => {
      setStudents(e);
    },
  };

  const Action = {
    UpdateQuery: (query: string) => {
      setQuery(query);
    },
    onSubmit: async () => {
      if (!seletedTutorId) {
        setTutorNotFound(true);
      } else {
        setIsLoading(true);
        try {
          const Session = await getSession();
          const token = Session?.token;
          const response = await api.put(
            `/lft/${lft.id}/replace-online-tutor`,
            {
              id: seletedTutorId,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            },
          );

          if (response.status === 200) {
            mutate();
            mutateStatus();
            mutateOT();
            ModalControl.makeAllValueDefault();
            toast.success(
              `Online Tutor ${
                isReplaceTutor ? "has been replaced" : "added"
              } successfully!`,
            );
          } else {
            toast.error("Unexpected response from the server!");
          }
        } catch (error: any) {
          setIsLoading(false);

          toast.error(
            `Failed to ${
              isReplaceTutor ? "replace" : "add"
            } online tutor. Please try again!`,
          );
        }
      }
    },
  };

  const SelectTutorFromSuggestion = (tutor: tSuggestedL) => {
    setTutor(tutor.fullName);
    setSeletedTutorId(tutor.id);
    setIsSuggested(true);

    setIsFocused(false);
    setTutorNotFound(false);
  };

  const generateQuery = () => {
    // setQuery(
    //   GenerateQuery({
    //     gender: gender === "All" ? "" : gender,
    //     students: Number.isInteger(students),
    //   }),
    // );

    Action.UpdateQuery(
      GenerateQuery({
        gender: gender === "All" ? "" : gender,
        students: Number(students),
      }),
    );
  };

  useEffect(() => {
    if (!tutor) {
      setSeletedTutorId("");
    }
    if (isSuggested && seletedTutorId) {
      Action.onSubmit();
    }
  }, [tutor]);

  useEffect(() => {
    generateQuery();
  }, [gender, students]);

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        {isReplaceTutor ? (
          <TbReplace size={15} className="cursor-pointer text-slate-700" />
        ) : (
          <div className="flex h-24 w-24 cursor-pointer items-center justify-center rounded-full bg-slate-400">
            <FaPlus size={28} color="white" />
          </div>
        )}
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>
            {isReplaceTutor ? "Replace Online Tutor" : "Add Online Tutor"}
          </DialogTitle>
          <DialogDescription className=" text-center">
            {lft.onlineTutor?.fullname}
          </DialogDescription>
        </DialogHeader>

        <div>
          <>
            <>
              <div
                className={`pb-1 text-sm font-medium ${
                  tutorNotFound ? "text-destructive" : "text-gray-800"
                }`}
              >
                Find A Tutor
              </div>
              <Input
                type="search"
                value={tutor}
                onChange={TutorSearchHandelar.Search}
                onFocus={() => setIsFocused(true)}
                className="text-xs text-gray-800"
              />

              {tutor && (
                <InputSelectorForModal
                  isOpen={isFocused}
                  searchValue={tutor}
                  onChange={TutorSearchHandelar.Select}
                  apiRoute="/tutor/search"
                />
              )}

              {tutorNotFound && (
                <div className="mt-1.5 text-xs font-medium text-destructive">
                  Tutor is required!
                </div>
              )}
            </>

            <div className="mt-6">
              <div className="mb-4 flex items-start justify-between">
                <div className={`text-base font-medium text-gray-800`}>
                  Suggestions
                </div>

                <div className="flex items-center space-x-2.5">
                  <div>
                    <div className="mb-1 text-xs font-semibold text-slate-600">
                      Gender
                    </div>

                    <ToggleGroup
                      type="single"
                      defaultValue={gender}
                      onValueChange={FilterHandelar.Gender}
                      className="h-fit rounded-md border border-slate-200 bg-white"
                    >
                      <ToggleGroupItem value="All">
                        <div className="text-xs font-normal text-slate-600">
                          All
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="Male">
                        <FaMale size={15} color="#475569" />
                      </ToggleGroupItem>
                      <ToggleGroupItem value="Female">
                        <FaFemale size={15} color="#475569" />
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </div>

                  <div>
                    <div className="mb-1 text-xs font-semibold text-slate-600">
                      Max Student
                    </div>
                    <ToggleGroup
                      type="single"
                      defaultValue={students}
                      onValueChange={FilterHandelar.Students}
                      className="h-fit rounded-md border border-slate-200 bg-white"
                    >
                      <ToggleGroupItem value="0" aria-label="Toggle bold">
                        <div className="text-xs font-normal text-slate-600">
                          All
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="10" aria-label="Toggle italic">
                        <div className="text-xs font-normal text-slate-600">
                          10
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="15" aria-label="Toggle italic">
                        <div className="text-xs font-normal text-slate-600">
                          15
                        </div>
                      </ToggleGroupItem>
                      <ToggleGroupItem value="20" aria-label="Toggle italic">
                        <div className="text-xs font-normal text-slate-600">
                          20
                        </div>
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </div>
                </div>
              </div>

              {OnlinTutors && OnlinTutors?.length < 1 ? (
                <div className="pt-2.5 text-sm text-slate-600">
                  {` ${
                    lft.instrument
                      ? "Tutor suggestion not available."
                      : "Please update the instrument in order to get Tutor Suggestions."
                  }`}
                </div>
              ) : (
                <div className="mb-1.5 grid grid-cols-3 items-center gap-x-2.5">
                  <div className="col-span-2 space-y-0.5">
                    <div className="text-xs font-semibold text-slate-600">
                      Tutor Name
                    </div>

                    <div className="text-[10px] font-normal text-slate-500">
                      Tutor Email
                    </div>
                  </div>

                  <div className="space-y-0.5">
                    <div className="text-xs font-semibold text-slate-600">
                      Active Student
                    </div>

                    <div className="text-[10px] font-normal text-slate-500">
                      Rank
                    </div>
                  </div>
                </div>
              )}

              {isLoadingOT && <ListSkeltons count={3} height={50} />}

              {OnlinTutors &&
                OnlinTutors?.map((tutor: tSuggestedL) => (
                  <div
                    onClick={() => SelectTutorFromSuggestion(tutor)}
                    key={tutor.id}
                    className="mb-1.5 grid h-fit w-full cursor-pointer grid-cols-3 items-center gap-x-2.5 rounded-md border border-slate-200 bg-white py-2.5 hover:border-slate-300 hover:bg-slate-100"
                  >
                    <div className="col-span-2 flex items-center pl-1.5">
                      <Avatar name={tutor.fullName} round={true} size="42" />

                      <div className="space-y-0.5 pl-1.5">
                        <div className="max-w-[160px] overflow-hidden truncate text-xs font-semibold text-slate-700">
                          {tutor.fullName}
                        </div>
                        <div className="max-w-[160px] overflow-hidden truncate text-[10px] font-light text-slate-600">
                          {tutor.email}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-0.5">
                      <div className="max-w-[90px] overflow-hidden truncate text-xs font-medium text-slate-600">
                        {tutor.students}
                      </div>

                      <div className="max-w-[96px] overflow-hidden truncate text-[10px] font-light text-slate-600">
                        {tutor.rank}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </>

          <div className="mt-4 flex w-full flex-row justify-end space-x-1">
            <Button
              type="submit"
              onClick={Action.onSubmit}
              className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
            >
              {isLoading ? <Dots /> : isReplaceTutor ? "Replace" : "Submit"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
