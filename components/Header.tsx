import Link from "next/link";
import React from "react";
import styles from "styles/Header.module.css";
import {
  RiUserSmileLine,
  RiNotificationLine,
  RiMessage2Line,
  Ri<PERSON>lertLine,
} from "react-icons/ri";
import { BiLogOutCircle } from "react-icons/bi";

import { signOut } from "next-auth/react";

export default function Header(props: any) {
  return (
    <div className={styles.Main}>
      <div className={styles.Left} />
      <div className={styles.Right}>
{/*         <div className={styles.Item}>
          <Link href="/">
            <RiAlertLine size={15} />
          </Link>
        </div> */}
        <div className={styles.Item}>
          <Link href="/">
            <RiMessage2Line size={15} />
          </Link>
        </div>
        <div className={styles.Item}>
          <Link href="/">
            <RiNotificationLine size={15} />
          </Link>
        </div>
        <div className={styles.Item}>
          <Link href="/">
            <RiUserSmileLine size={15} />
          </Link>
        </div>
        <div className={styles.Item} onClick={() => signOut()}>
          <BiLogOutCircle size={16} />
        </div>
      </div>
    </div>
  );
}
