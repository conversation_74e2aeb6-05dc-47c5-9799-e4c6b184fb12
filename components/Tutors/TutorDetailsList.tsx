"use client";

import { tAssignedStudent, tTutorD, tTutorStats } from "types/tutor";
import React, { useEffect, useRef, useState } from "react";
import Avatar from "react-avatar";
import moment from "moment";
import Link from "next/link";
import { RiDeleteBin6Line } from "react-icons/ri";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Calendar } from "components/ui/calendar";

import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { SlCalender } from "react-icons/sl";

import { Checkbox } from "@components/ui/checkbox";

import { Tabs, TabsContent } from "components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "components/ui/card";

import { Button } from "components/ui/button";

import {
  MdEditLocationAlt,
  MdOutlineFreeCancellation,
  MdOutlinePlayLesson,
  MdPlayLesson,
} from "react-icons/md";

import { Textarea } from "@components/ui/textarea";

import { Dots } from "react-activity";
import { RxDotFilled } from "react-icons/rx";
import { GiMoneyStack, GiPayMoney } from "react-icons/gi";
import { IoIosSettings, IoMdNotifications } from "react-icons/io";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";

import { FaUserEdit } from "react-icons/fa";
import { TbHomeEdit } from "react-icons/tb";
import { Input } from "@components/ui/input";
import Selects from "react-select";
import { checkPostCode } from "core/Checker";
import { getSession } from "next-auth/react";
import { GeoCoding } from "core/Map/Map";
import UpdateEmail from "@components/global/UpdateEmail";
import { MdEditDocument } from "react-icons/md";

type tTutorInfoProps = {
  tutor: tTutorD;
  mutate: () => void;
};
type tAssignedStuInfo = {
  student: tAssignedStudent;
  mutate: any;
};

type tAssignedStuProps = {
  student: tAssignedStudent;
  mutate: () => void;
};

type tTutorStatsProps = {
  tutorStats: tTutorStats;
};

type tSelector = {
  value: any;
  label: string;
};

export default function Tutor() {
  return <div>Tutor</div>;
}

export function TutorInfo({ tutor, mutate }: tTutorInfoProps) {
  return (
    <div className="h-auto w-64 rounded  border border-slate-200 bg-white pb-5 pt-3">
      {/* //Setting Section... */}

      <div className="flex w-full flex-row  items-center justify-between px-2">
        <NotifySingleTutor tutor={tutor} mutate={mutate} />

        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div>
              <IoIosSettings
                size={22}
                className="cursor-pointer text-slate-700"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-1.5 p-1">
                <UpdateProfile tutor={tutor} mutate={mutate} />
                <UpdateEmail
                  email={tutor.email}
                  operationName="Tutor Email"
                  mutate={mutate}
                />
                <UpdateLocation tutor={tutor} mutate={mutate} />
                <UpdateTutorCv tutor={tutor} mutate={mutate} />
                <ChangeCountry tutor={tutor} mutate={mutate} />
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex w-full flex-col  items-center justify-center pb-2.5">
        <Avatar name={tutor.fullName} size="90" round={true} />
        <div className="my-1 text-sm font-semibold text-slate-500 ">
          {tutor.fullName}
        </div>
        {tutor.category && (
          <div className="mb-1 text-xs font-medium text-slate-500">
            {tutor.category}
          </div>
        )}
        <div className="text-xs font-light text-slate-600">{tutor.email}</div>

        <div className="my-2.5 flex items-center space-x-2.5">
          {tutor.cv && (
            <Link
              href={tutor.cv}
              target="_blank"
              className={`flex h-5 cursor-pointer flex-col items-center justify-center rounded border bg-white px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200 ${
                tutor.preferred === "Tutor"
                  ? "border-green-400"
                  : "border-slate-200"
              }`}
            >
              CV
            </Link>
          )}
          {tutor.dbs && (
            <Link
              href={tutor.dbs}
              target="_blank"
              className="flex h-5 cursor-pointer flex-col items-center justify-center rounded border border-slate-200 bg-white px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200"
            >
              DBS
            </Link>
          )}
          {tutor.adminCv && (
            <Link
              href={tutor.adminCv}
              target="_blank"
              className={`flex h-5 cursor-pointer flex-col items-center justify-center rounded border bg-white px-2 text-[9px] font-medium text-slate-600 hover:bg-slate-200 ${
                tutor.preferred === "Custom"
                  ? "border-green-400"
                  : "border-slate-200"
              }`}
            >
              Admin CV
            </Link>
          )}
        </div>
      </div>

      <div>
        {/* //General Information... */}
        <div className="flex h-7 w-full items-center bg-slate-200 pl-2 text-sm font-semibold text-slate-700">
          General
        </div>

        <div className="my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Gender</p>
          <p
            className={` text-[11px] font-light  ${
              tutor.gender ? "text-slate-800" : "text-destructive"
            }`}
          >
            {tutor.gender ? tutor.gender : "Not Selected"}
          </p>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Phone</p>
          <p className=" text-[11px] font-light text-slate-800">
            {tutor.phoneNumber}
          </p>
        </div>

        <div className="my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Address</p>
          <p className="text-[11px]  font-light text-slate-800">
            {tutor.address ? tutor.address : "-------"}
          </p>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Country</p>
          <p className="text-[11px]  font-light text-slate-800">
            {tutor.country ? tutor.country : "-------"}
          </p>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">
            {tutor.country === "United Kingdom" ? "Postcode" : "Zipcode"}
          </p>
          <p className="text-[11px]  font-light text-slate-800">
            {tutor.postCode ? tutor.postCode : "-------"}
          </p>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Work Permit</p>
          <p className="text-[11px]  font-light text-slate-800">
            {tutor.workPermit ? tutor.workPermit : "-------"}
          </p>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Birthdate</p>
          <p className="text-[11px]  font-light text-slate-800">
            {tutor.dob
              ? moment(tutor.dob)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .format("DD MMM YYYY")
              : "-------"}
          </p>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">
            Can Teach At Home?
          </p>
          <p className="text-[11px]  font-light text-slate-800">
            {tutor.canTeachAtHome ? "Yes" : "No"}
          </p>
        </div>

        <div className="my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Joined</p>
          <p className="text-[11px] font-light text-slate-800">
            {tutor.joined
              ? moment(tutor.joined)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .format("DD MMM YYYY")
              : "Unknown"}
          </p>
        </div>

        {/* //Qualification... */}
        <div className="flex h-7 w-full items-center bg-slate-200 pl-2 text-sm font-semibold text-slate-700">
          Qualification
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">University</p>
          <p className="text-[11px] font-light text-slate-800">
            {tutor.university ? tutor.university : "Unknown"}
          </p>
        </div>
        <div className="my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Instruments</p>
          <div className="mt-1 flex  flex-wrap">
            {tutor.instruments.map((instrument: string, index) => (
              <div
                key={index}
                className="mb-1 mr-1 flex h-5 w-fit  flex-col items-center justify-center rounded border px-2 text-[9px] font-light text-slate-800 "
              >
                {instrument}
              </div>
            ))}
          </div>
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Rank</p>
          <p className="  text-[11px] font-light text-slate-800">
            {tutor.rank ? tutor.rank : "Unknown"}
          </p>
        </div>

        {/* //Bank Info... */}
        <div className="flex h-7 w-full items-center bg-slate-200 pl-2 text-sm font-semibold text-slate-700">
          Bank Info
        </div>

        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Account Number</p>
          <p className="  text-[11px] font-light text-slate-800">
            {tutor.accNo ? tutor.accNo : "Unknown"}
          </p>
        </div>
        <div className=" my-2.5 pl-2">
          <p className=" text-xs font-medium text-slate-800">Sort Code</p>
          <p className="  text-[11px] font-light text-slate-800">
            {tutor.sc ? tutor.sc : "Unknown"}
          </p>
        </div>
      </div>
    </div>
  );
}

export function AssignStudents({ student, mutate }: tAssignedStuInfo) {
  return (
    <div className="mb-2 grid grid-cols-7 items-center gap-x-6 rounded-md border border-slate-200 bg-white px-2  py-2">
      <div className=" col-span-2 flex items-center">
        <Avatar name={student.studentName} round={true} size="40" />
        <div className=" ml-2">
          <div className="flex items-center">
            {student.isActive && (
              <RxDotFilled
                size={22}
                color="#009900"
                className=" ml-[-10px] mr-[-4px]"
              />
            )}
            <Link
              href={`/students/${student.studentId}`}
              target="_blank"
              className="w-24 overflow-hidden truncate whitespace-nowrap text-xs font-semibold text-slate-700 hover:cursor-pointer hover:underline "
            >
              {student.studentName}
            </Link>
          </div>

          <div className="w-20 overflow-hidden truncate whitespace-nowrap text-[10px] font-light text-slate-600">
            {student.childName}
          </div>
        </div>
      </div>

      <div>
        <div className=" text-xs font-light text-slate-600">
          {student.lessons}
        </div>
        <div className=" text-[10px] font-light text-slate-600">
          {student.type}
        </div>
      </div>

      <div className="">
        <div className="text-xs font-medium text-slate-600">
          {student.payments}
        </div>
        <div className="text-[10px] font-light text-slate-600">
          {student.commissions}
        </div>
      </div>

      <div className="text-xs font-light text-slate-600">
        {student.instrument}
      </div>

      <div>
        <div className=" text-xs font-medium text-slate-600">
          {moment(student.started)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
        {!student.isActive && (
          <div className="text-[10px] font-light text-slate-600">
            {moment(student.ended)
              .add(new Date().getTimezoneOffset() * -1, "minute")
              .format("DD MMM YYYY")}
          </div>
        )}
      </div>

      <div className="flex cursor-pointer  items-center justify-center text-xs font-light text-slate-600">
        {student.isActive ? (
          <DeleteAssignedStu student={student} mutate={mutate} />
        ) : (
          <RiDeleteBin6Line size={16} color="#333333" />
        )}
      </div>
    </div>
  );
}

export function AssignStudentsHeader() {
  return (
    <div className="grid grid-cols-7 gap-x-4 px-2 py-1 ">
      <div className="col-span-2">
        <div className=" text-xs font-medium text-slate-600">Student</div>
        <div className=" text-[10px] font-light text-slate-500">Child</div>
      </div>
      <div>
        <div className=" text-xs font-medium text-slate-600">Lessons</div>
        <div className=" text-[10px] font-light text-slate-500">Type</div>
      </div>

      <div className=" ">
        <div className="text-xs font-medium text-slate-600">Payments</div>
        <div className="text-[10px] font-light text-slate-500">Commissions</div>
      </div>
      <div className="text-xs font-medium text-slate-600 ">Instrument</div>
      <div className="">
        <div className=" text-xs font-medium text-slate-600">Started</div>
        <div className="text-[10px] font-light text-slate-500">Ended</div>
      </div>
      <div className="text-center text-xs font-medium text-slate-600">
        Action
      </div>
    </div>
  );
}

function DeleteAssignedStu({ student, mutate }: tAssignedStuProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDelete = async () => {};

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <RiDeleteBin6Line size={16} color="#ff0000" />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action is irreversible and will permanently delete the assigned
            student details from our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export function TutorStatsCard({ tutorStats }: tTutorStatsProps) {
  return (
    <div>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsContent value="overview" className="space-y-4">
          <div className="grid  grid-cols-4 gap-2">
            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  Lessons
                </CardTitle>
                <MdPlayLesson
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.lessons.toFixed(0)}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  To date
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  8+ Lessons
                </CardTitle>
                <MdOutlineFreeCancellation
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.eightInTime}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  Within 90 days
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  20+ Lessons
                </CardTitle>
                <MdOutlineFreeCancellation
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.moreThanTwenty}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  {`Out of ${tutorStats?.students} students`}
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  Average
                </CardTitle>
                <MdOutlinePlayLesson
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.average.toFixed(2)}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  Lesson per month
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  Earning
                </CardTitle>
                <GiMoneyStack
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.currency}
                  {tutorStats?.earning.toFixed(2)}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  To date
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  Commission
                </CardTitle>
                <GiPayMoney
                  size={22}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.currency}
                  {tutorStats?.commission.toFixed(2)}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  To date
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  Commission
                </CardTitle>
                <GiPayMoney
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.currency}
                  {tutorStats?.commissionLastMonth.toFixed(2)}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  Last 30 days
                </p>
              </CardContent>
            </Card>

            <Card className="h-24">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 py-4 pb-2">
                <CardTitle className="text-sm font-medium  text-slate-600">
                  Commission
                </CardTitle>
                <GiPayMoney
                  size={20}
                  className="h-4 w-4 text-muted-foreground"
                />
              </CardHeader>
              <CardContent>
                <div className="text-base font-bold text-slate-700">
                  {tutorStats?.currency}
                  {tutorStats?.commissionLastYear.toFixed(2)}
                </div>
                <p className="text-[9px] font-extralight  text-slate-500">
                  Last 365 days
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function UpdateProfile({ tutor, mutate }: tTutorInfoProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [birthDate, setBirthDate] = useState<Date | null>(
    tutor.dob ? new Date(tutor.dob) : null,
  );

  var resolver = yup.object().shape({
    fullName: yup.string().required("fullName is required!"),
    phoneNumber: yup.string().required("Phone number is required!"),
    // instruments: yup.string().required("Instruments is required!"),
    rank: yup.string().required("Rank is required!"),
    instruments: yup
      .array()
      .of(
        yup.object().shape({
          value: yup.string().required(),
          label: yup.string().required(),
        }),
      )
      .required("Instruments is required!"),
    // category: yup.string().required("Category is required!"),
    gender: yup.string().required("Gender is required!"),

    // workPermit: yup.string().required("Work Permit is required!"),
  });

  const defaultValues = {
    fullName: tutor.fullName || "",
    phoneNumber: tutor.phoneNumber || "",
    university: tutor.university || "",
    rank: tutor.rank.toString(),
    instruments: tutor.instruments.map((x) => {
      return { value: x, label: x };
    }),
    accNo: tutor.accNo || "", // Handle null
    sc: tutor.sc || "", // Handle null
    category: tutor.category || "", // Handle null
    gender: tutor.gender || "",
    canTeachAtHome: tutor.canTeachAtHome || false,
    workPermit: tutor.workPermit || "",
  };
  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    // console.log(data, "Update Tutor");

    var newInstrument = form
      .getValues("instruments")
      ?.map((x: any) => x.value)
      .toString();
    console.log(data);
    // console.log(newInstrument, "New...");

    const { rank } = data;

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/tutor/${tutor.id}/info`,
        {
          ...data,
          instruments: newInstrument,
          rank: Number(rank),
          dob: birthDate ? moment(birthDate).format("YYYY-MM-DD") : null,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Tutor Profile successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update tutor profile!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
    setBirthDate(tutor.dob ? new Date(tutor.dob) : null);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const InstrumentList: Array<tSelector> = [
    { value: "Bass Guitar", label: "Bass Guitar" },
    { value: "Cello", label: "Cello" },
    { value: "Clarinet", label: "Clarinet" },
    { value: "Double Bass", label: "Double Bass" },
    { value: "Drums", label: "Drums" },
    { value: "Flute", label: "Flute" },
    { value: "Guitar", label: "Guitar" },
    { value: "Harp", label: "Harp" },
    { value: "Jazz Piano", label: "Jazz Piano" },
    { value: "French Horn", label: "French Horn" },
    { value: "Viola", label: "Viola" },
    { value: "Violin", label: "Violin" },
    { value: "Oboe", label: "Oboe" },
    { value: "Piano - grades 1 to 5", label: "Piano - grades 1 to 5" },
    {
      value: "Piano - grades 5 and above",
      label: "Piano - grades 5 and above",
    },
    { value: "Sax", label: "Sax" },
    { value: "Singing", label: "Singing" },
    { value: "Trombone", label: "Trombone" },
    { value: "Trumpet", label: "Trumpet" },
    { value: "ABRSM Music Theory Grades", label: "ABRSM Music Theory Grades" },
  ];

  // Handler to properly convert the Calendar's selection
  const handleDateSelect = (date: Date | undefined) => {
    setBirthDate(date || null); // Convert undefined to null
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <FaUserEdit size={15} color="#64748B" />

          <span className="ml-1 text-xs font-medium text-slate-600">
            Update Profile
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full overflow-y-scroll sm:w-full sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Tutor Profile</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter full name"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter phone number"
                        {...field}
                        className="text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="instruments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instruments</FormLabel>
                    <div className="hide-scrollbar relative  w-full">
                      <Selects
                        isMulti
                        id="instruments"
                        // name="instruments"
                        options={InstrumentList}
                        placeholder="select instruments"
                        {...field}
                        className="border-slate-300 text-xs font-normal text-gray-700 "
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex w-full flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem className="w-1/2">
                      <FormLabel>Gender </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            {form.getValues("gender")
                              ? form.getValues("gender")
                              : "Select gender"}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Male">Male</SelectItem>
                          <SelectItem value="Female">Female</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rank"
                  render={({ field }) => (
                    <FormItem className="w-1/2">
                      <FormLabel>Rank</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            {form.getValues("rank")
                              ? form.getValues("rank")
                              : "Select rank"}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">1</SelectItem>
                          <SelectItem value="2">2</SelectItem>
                          <SelectItem value="3">3</SelectItem>
                          <SelectItem value="4">4</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("category")
                            ? form.getValues("category")
                            : "Select category"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={null as any}>Empty</SelectItem>
                        <SelectItem value="Regular">Regular</SelectItem>
                        <SelectItem value="Tier 4">Tier 4</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="workPermit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work permit</FormLabel>
                    <FormDescription>
                      Proof required before any teaching commences
                    </FormDescription>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className="max-w-80 truncate">
                            {field.value
                              ? field.value === "Self employed"
                                ? "I have the full right to work as a self employed person"
                                : field.value === "Student"
                                  ? "I am on a student visa"
                                  : field.value
                              : "Select a permit"}
                          </div>
                        </SelectTrigger>
                      </FormControl>

                      <SelectContent>
                        <SelectItem value="Self employed">
                          I have the full right to work as a self employed
                          person
                        </SelectItem>
                        <SelectItem value="Student">
                          I am on a student visa
                        </SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="w-full ">
                <FormLabel>Birthdate</FormLabel>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="birthDate"
                      variant={"outline"}
                      className="mt-1 flex w-full items-center justify-start bg-white px-2.5 font-normal"
                    >
                      <SlCalender size={16} className="mr-2" />

                      {birthDate ? (
                        moment(birthDate).format("MMM D, YYYY")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="single"
                      defaultMonth={birthDate ?? new Date()}
                      selected={birthDate ?? undefined} // Convert null to undefined
                      onSelect={handleDateSelect} // Use the handler instead of setBirthDate directly
                      numberOfMonths={1}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <FormField
                control={form.control}
                name="university"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>University</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="university"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="accNo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account Number</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter accNo"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sc"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sort Code</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter sort code"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="canTeachAtHome"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-2 pt-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel>Can Teach At Home?</FormLabel>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* //Button Section... */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UpdateLocation({ tutor, mutate }: tTutorInfoProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    address: yup.string().required("Address is required!"),
    postCode: yup
      .string()
      .required("Postcode is required!")
      .test("", "Can not locate your Postcode!", async (value, values) =>
        checkPostCode(value),
      ),
  });

  const defaultValues = {
    address: tutor.address,
    postCode: tutor.postCode,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    const { postCode } = data;

    setIsLoading(true);
    // console.log(data);

    var loc: { lat: number; lng: number } = await GeoCoding(postCode);
    // console.log(loc);

    if (!loc) {
      toast.error(
        "Failed to verify your postCode!. \n\nAn unknown error occurred while verifying your location!",
      );
    }

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/tutor/${tutor.id}/loc`,
        {
          ...data,
          lat: loc.lat,
          lng: loc.lng,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Tutor location successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update tutor location!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={reset}>
      <DialogTrigger onClick={onOpen}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <MdEditLocationAlt size={16} color="#64748B" />
          {/* <MdOutlineEditLocationAlt size={14} color="#64748B" /> */}
          <span className="ml-1 text-xs font-medium text-slate-600">
            Update Location
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Location</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter address"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="postCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postcode</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter postCode"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {/* //Button Section... */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function ChangeCountry({ tutor, mutate }: tTutorInfoProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    country: yup.string().required("Country is required!"),
  });

  const defaultValues = {
    country: tutor.country || "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    // console.log(data);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/tutor/${tutor.id}/country`,
        {
          ...data,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Tutor country successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update tutor country!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={reset}>
      <DialogTrigger onClick={onOpen}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <TbHomeEdit size={16} color="#64748B" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Change Country
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Change Country</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem className="py-2.5">
                  <FormLabel>Country </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="capitalize">
                          {form.getValues("country")
                            ? form.getValues("country")
                            : "Select country"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                      <SelectItem value="Ukraine">Ukraine</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UpdateTutorCv({ tutor, mutate }: tTutorInfoProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    preferred: yup.string().required("Preferred CV required!"),
  });

  const defaultValues = {
    preferred: tutor.preferred,
    adminCv: tutor.adminCv,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const inputFileRef = useRef<any>(undefined);

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    const formData = new FormData();

    if (data.adminCv) {
      formData.append("adminCv", inputFileRef.current.files[0]);
    }

    formData.append("preferred", data.preferred);

    // for (const value of formData.values()) {
    //   console.log(value);
    // }

    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(`/tutor/${tutor.id}/cv`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        setIsLoading(false);
        mutate();

        setIsOpen(false);
        toast.success(
          `Admin CV of ${tutor.fullName} has been successfully updated!`,
        );
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update admin CV!");
    }
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <MdEditDocument size={15} color="#64748B" />

          <span className="ml-1 text-xs font-medium text-slate-600">
            Update CV
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-md ">
        <DialogHeader>
          <DialogTitle>Update Tutor CV</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-3">
              <FormField
                control={form.control}
                name="preferred"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred CV</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {field.value
                            ? field.value === "Tutor"
                              ? "Tutor Version"
                              : field.value
                            : "Select Preferred CV "}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Tutor">Tutor Version</SelectItem>
                        <SelectItem value="Custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="adminCv"
                render={({ field: { onChange, onBlur, name, ref } }) => (
                  <FormItem>
                    <FormLabel>Upload Admin CV</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        placeholder="Select a file to upload"
                        name={name}
                        onChange={onChange}
                        onBlur={onBlur}
                        ref={inputFileRef}
                        className="text-xs font-normal text-gray-800"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6 flex w-full flex-row items-center justify-end space-x-2.5">
              <Button
                type="reset"
                onClick={() => {
                  form.reset(defaultValues);
                }}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function NotifySingleTutor({ tutor, mutate }: tTutorInfoProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    title: yup.string().required("Title is required!"),
    body: yup.string().required("Body is required!"),
  });

  const defaultValues = { title: "", body: "" };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `/tutor/${tutor.id}/notify`,
        { data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Notification successfully sent!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to send notification!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };
  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="">
          <IoMdNotifications
            size={20}
            className="cursor-pointer text-slate-700"
          />
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Notify Tutor</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className=" space-y-2.5">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter Title"
                        className=" text-xs font-normal text-slate-700"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="body"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter description"
                        className=" text-xs font-normal text-slate-700"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-6 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
