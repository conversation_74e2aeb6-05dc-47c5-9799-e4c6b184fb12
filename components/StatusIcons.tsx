import React from 'react'
import { BsArrowRepeat, BsCalendar2Check, BsCheck2Square } from "react-icons/bs"
import { MdCancelPresentation } from "react-icons/md"

export default function StatusIcons(props: any) {
    if (props.status === "Confirmed")
        return (
            <BsCheck2Square color='green' />
        )
    else if (props.status === "Late Cancelled By Student")
        return (
            <BsCalendar2Check color="green" />
        )
    else if (props.status === "Pending")
        return (
            <BsArrowRepeat color="blue" />
        )
    else {
        return <MdCancelPresentation color="red" />
    }
}