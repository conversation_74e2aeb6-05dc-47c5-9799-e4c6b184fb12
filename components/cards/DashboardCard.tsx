import nFormatter from "core/nFormatter";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";
import { Dots } from "react-activity";
import { BsArrowDownLeft, BsArrowUpRight } from "react-icons/bs";

type Props = {
  title?: string;
  data: number | any | undefined;
  icon: any;
  range?: number | undefined;
  rangeTitle?: string;
  statistic?: number | undefined;
  isLoading: boolean;
  isOnClick?: boolean;
  routeLink?: any;
  notSendQuery?: boolean;
};

export default function DashboardCard({
  title,
  data,
  icon,
  range,
  statistic,
  isLoading,
  rangeTitle,
  isOnClick,
  routeLink,
  notSendQuery,
}: Props) {
  const router = useRouter();

  const titleGenerate = (title: string) => {
    if (range == 1) {
      return title + " Today";
    } else if (range == 7) return title + " This Week";
    else if (range == 30) return title + " This Month";
  };

  // OLD Code for router ....
  // const handleClick = () => {
  //   if (isOnClick && routeLink) {
  //     router.push({ pathname: routeLink, query: { Compare: range } });
  //   }
  // };

  return (
    <>
      {isOnClick && routeLink ? (
        <Link
          href={
            notSendQuery
              ? { pathname: routeLink }
              : { pathname: routeLink, query: { Compare: range } }
          }
          target="_blank"
          rel="noopener noreferrer"
        >
          <div className="group flex aspect-square cursor-pointer flex-col items-center justify-center overflow-hidden rounded-lg border border-slate-200 bg-white transition-all hover:bg-slate-800">
            <div className="flex flex-row items-center justify-center rounded-full bg-slate-100 p-4 group-hover:bg-white">
              {icon}
            </div>

            <div className="mb-1.5 mt-2.5 text-2xl font-bold text-slate-800 group-hover:text-white">
              {isLoading ? <Dots /> : nFormatter(data)}
            </div>

            {range ? (
              <div className="text-sm font-normal text-slate-500 group-hover:text-white">
                {rangeTitle && titleGenerate(rangeTitle)}
              </div>
            ) : (
              <div className="text-sm font-normal text-slate-500 group-hover:text-white">
                {title}
              </div>
            )}

            {statistic !== undefined && (
              <>
                {statistic < 0 ? (
                  isLoading ? (
                    <Dots />
                  ) : (
                    <div className="mt-1.5 flex items-center space-x-1">
                      <BsArrowDownLeft className="text-red-600" size={10} />
                      <span className="text-[10px] font-medium text-red-600">
                        Down {statistic}%
                      </span>
                    </div>
                  )
                ) : isLoading ? (
                  <Dots />
                ) : (
                  <div className="mt-1.5 flex items-center space-x-1">
                    <BsArrowUpRight className="text-green-600" size={10} />
                    <span className="text-[10px] font-medium text-green-600">
                      Up {statistic}%
                    </span>
                  </div>
                )}
              </>
            )}
          </div>
        </Link>
      ) : (
        <div className="group flex aspect-square flex-col items-center  justify-center overflow-hidden rounded-lg border border-slate-200 bg-white transition-all hover:bg-slate-800">
          <div className="flex flex-row items-center justify-center rounded-full bg-slate-100 p-4 group-hover:bg-white">
            {icon}
          </div>

          <div className="mb-1.5 mt-2.5 text-2xl font-bold text-slate-800 group-hover:text-white">
            {isLoading ? <Dots /> : nFormatter(data)}
          </div>

          {range ? (
            <div className="text-sm font-normal text-slate-500 group-hover:text-white">
              {rangeTitle && titleGenerate(rangeTitle)}
            </div>
          ) : (
            <div className="text-sm font-normal text-slate-500 group-hover:text-white">
              {title}
            </div>
          )}

          {statistic !== undefined && (
            <>
              {statistic < 0 ? (
                isLoading ? (
                  <Dots />
                ) : (
                  <div className="mt-1.5 flex items-center space-x-1">
                    <BsArrowDownLeft className="text-red-600" size={10} />
                    <span className="text-[10px] font-medium text-red-600">
                      Down {statistic}%
                    </span>
                  </div>
                )
              ) : isLoading ? (
                <Dots />
              ) : (
                <div className="mt-1.5 flex items-center space-x-1">
                  <BsArrowUpRight className="text-green-600" size={10} />
                  <span className="text-[10px] font-medium text-green-600">
                    Up {statistic}%
                  </span>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </>
  );
}
