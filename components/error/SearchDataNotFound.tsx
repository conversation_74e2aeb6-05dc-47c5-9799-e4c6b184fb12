"use client";

import { Button } from "@components/ui/button";
import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";

const LottieAnimation = dynamic(
  () => import("@components/lottie/LottieAnimation"),
  { ssr: false },
);

import NotSearchFound from "assets/lottie/not-found.json";
import { FaRotate } from "react-icons/fa6";

type Props = {
  title: string;
  des?: string;
  trigger: () => void;
};

export default function SearchDataNotFound({ title, des, trigger }: Props) {
  const router = useRouter();

  return (
    <div className="flex h-full w-full flex-col items-center justify-center  px-4 md:px-8 lg:px-12">
      <div>
        <LottieAnimation height={250} width={250} icon={NotSearchFound} />
      </div>

      <div className="mb-5 mt-5 text-sm font-semibold text-slate-800 md:mt-10 md:text-base">
        {title}
      </div>

      {des && (
        <p className="mb-10 text-center text-xs font-medium text-slate-500 md:text-sm lg:text-base">
          {des}
        </p>
      )}

      <Button
        onClick={() => trigger()}
        className="h-10 w-32 rounded-2xl bg-cyan-600 text-xs font-semibold text-white hover:bg-cyan-800 md:text-sm"
      >
        <FaRotate size={16} className="mr-2" />
        Reset
      </Button>
    </div>
  );
}
