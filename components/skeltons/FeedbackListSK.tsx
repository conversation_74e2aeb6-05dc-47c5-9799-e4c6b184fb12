import { Skeleton } from "components/ui/skeleton";
import React from "react";
import ListSkeltons from "./ListSkeltons";

export default function FeedbackListSK() {
  return (
    <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
      <div className="mb-10  grid grid-cols-4 gap-2">
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
        <Skeleton className="h-24 rounded-xl border  text-card-foreground shadow" />
      </div>

      <ListSkeltons height={42} count={30} />
    </div>
  );
}
