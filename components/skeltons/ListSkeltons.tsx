import React from 'react'
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'

type Props = {
    count: number
    height: number
}

export default function ListSkeltons({ count, height }: Props) {
    return (
        <div style={{
            display: 'block',
            width: '100%',
            marginBottom: 5
        }}>
            <Skeleton height={height} count={count} />
        </div>
    )
}
