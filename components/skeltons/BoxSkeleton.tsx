import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

type Props = {
  count: number;
  height: number;
  width: number;
};

export default function BoxSkeleton({ count, height, width }: Props) {
  return (
    <div
      style={{
        display: "block",
        width: width,
        marginBottom: 5,
      }}
    >
      <Skeleton height={height} count={count} width={width} />
    </div>
  );
}
