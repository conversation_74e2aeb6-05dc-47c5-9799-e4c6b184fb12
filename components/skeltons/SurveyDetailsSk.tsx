import { Skeleton } from "components/ui/skeleton";
import React from "react";

export default function SurveyDetailsSk() {
  return (
    <div className="hide-scrollbar h-full w-full overflow-y-scroll bg-gradient-to-br from-slate-50 to-cyan-50 px-3.5 py-4 sm:px-4 md:px-6 lg:px-8">
      <div className="mx-auto max-w-6xl space-y-4 md:space-y-6">
        {/* Header Section Skeleton */}
        <div className="relative rounded-xl bg-gradient-to-r from-gray-200 to-gray-300 p-3.5 shadow-xl sm:rounded-2xl sm:p-6 md:p-8">
          {/* Mobile Layout Skeleton */}
          <div className="block sm:hidden">
            <div className="mb-3 flex items-start justify-between">
              <Skeleton className="h-6 w-32 flex-1" />
              <Skeleton className="h-6 w-20 rounded-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-28 rounded-full" />
              <Skeleton className="h-5 w-24 rounded-full" />
            </div>
          </div>

          {/* Desktop/Tablet Layout Skeleton */}
          <div className="hidden sm:block">
            <div className="absolute right-4 top-4 md:right-6 md:top-6">
              <Skeleton className="h-10 w-24 rounded-full md:h-12 md:w-28" />
            </div>
            <div className="pr-24 md:pr-32">
              <Skeleton className="mb-4 h-8 w-48 md:h-10 md:w-64 lg:h-12 lg:w-80" />
              <div className="mb-6 flex flex-col items-center justify-center space-y-2 sm:flex-row sm:space-x-4 sm:space-y-0 lg:justify-start">
                <Skeleton className="h-8 w-36 rounded-full" />
                <Skeleton className="h-8 w-24 rounded-full" />
              </div>
            </div>
          </div>
        </div>

        {/* Survey Questions Section Skeleton */}
        <div className="rounded-lg border-0 bg-white shadow-lg">
          {/* Header */}
          <div className="border-b bg-gradient-to-r from-gray-50 to-cyan-50 py-4 sm:py-4 md:py-6">
            <div className="flex flex-row items-center justify-center space-x-3">
              <Skeleton className="h-5 w-5 rounded sm:h-6 sm:w-6 md:h-8 md:w-8" />
              <Skeleton className="h-6 w-40 sm:h-7 sm:w-48 md:h-8 md:w-56" />
            </div>
          </div>

          {/* Questions */}
          <div className="p-4 sm:p-4 md:p-6">
            <div className="space-y-4 sm:space-y-4 md:space-y-6">
              {[...Array(5)].map((_, index) => (
                <div
                  key={index}
                  className="rounded-lg border-l-4 border-l-gray-300 bg-gradient-to-r from-white to-gray-50 p-4 shadow-md sm:p-4 md:p-6"
                >
                  <Skeleton className="mb-3 h-5 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="mt-2 h-4 w-2/3" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Referrals Section Skeleton */}
        <div className="rounded-lg border-0 bg-white shadow-lg">
          {/* Header */}
          <div className="border-b bg-gradient-to-r from-gray-50 to-cyan-50 py-4 sm:py-4 md:py-6">
            <div className="flex flex-row items-center justify-center space-x-3">
              <Skeleton className="h-5 w-5 rounded sm:h-6 sm:w-6 md:h-8 md:w-8" />
              <Skeleton className="h-6 w-32 sm:h-7 sm:w-40 md:h-8 md:w-48" />
              <Skeleton className="h-6 w-8 rounded-full" />
            </div>
          </div>

          {/* Referral Cards */}
          <div className="p-4 sm:p-4 md:p-6">
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-3 md:gap-4 lg:grid-cols-3">
              {[...Array(3)].map((_, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-3 rounded-lg bg-gradient-to-r from-green-50 to-cyan-50 p-3 shadow-sm"
                >
                  <Skeleton className="h-8 w-8 flex-shrink-0 rounded-full" />
                  <div className="min-w-0 flex-1">
                    <Skeleton className="h-4 w-32" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Professional Footer */}
        <div className="rounded-xl bg-gradient-to-r from-gray-800 to-gray-900 p-4 sm:rounded-2xl sm:p-4 md:p-6">
          <div className="flex flex-col items-center space-y-3 sm:flex-row sm:justify-between sm:space-y-0">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full sm:h-10 sm:w-10 md:h-12 md:w-12" />
              <div className="text-center sm:text-left">
                <Skeleton className="mb-1 h-4 w-16 sm:h-5 sm:w-20 md:h-6 md:w-24" />
                <Skeleton className="h-3 w-32 sm:h-4 sm:w-40" />
              </div>
            </div>
            <div className="text-center sm:text-right">
              <Skeleton className="mb-1 h-4 w-24 sm:h-5 sm:w-28 md:h-6 md:w-32" />
              <Skeleton className="h-3 w-36 sm:h-4 sm:w-40" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
