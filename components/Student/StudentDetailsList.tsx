"use client";

import React, { useState } from "react";
import Link from "next/link";

import Avatar from "react-avatar";
import { tStudentD } from "types/student";
import { useForm } from "react-hook-form";
import { RiNotificationOffLine } from "react-icons/ri";
import { FcPhoneAndroid } from "react-icons/fc";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import { Button } from "@components/ui/button";
import { Dots } from "react-activity";
import { Textarea } from "@components/ui/textarea";
import { getSession } from "next-auth/react";
import { IoIosSettings } from "react-icons/io";
import UpdateStatus from "@components/global/UpdateStatus";
import moment from "moment";
import { FaUserEdit } from "react-icons/fa";
import { Input } from "@components/ui/input";
import UpdateEmail from "@components/global/UpdateEmail";

type Props = {
  student: tStudentD | undefined;
  mutate: () => void;
};

export function StudentInfo({ student, mutate }: Props) {
  const GetStatusColor = (status: string) => {
    switch (status) {
      case "Regular":
        return "bg-lime-600 border-lime-600 text-lime-600";
      case "Gave up":
        return "bg-red-600 border-red-600 text-red-600";
      case "Looking for tutor":
        return "bg-blue-600 border-blue-600 text-blue-600";
      case "Green":
        return "bg-green-600 border-green-600 text-green-600";
      case "New":
        return "bg-amber-600 border-amber-600 text-amber-600";
      case "Pending 1st lesson":
        return "bg-violet-600 border-violet-600 text-violet-600";
      case "Multiple Children":
        return "bg-pink-600 border-pink-600";
      case "Call much later":
        return "bg-cyan-600 border-cyan-600 text-cyan-600";
      default:
        return "bg-teal-600 border-teal-600 text-teal-600";
    }
  };

  const GetStepColor = (step: string) => {
    const colorMap: Record<string, string> = {
      Step1: "yellow",
      Step2: "blue",
      Step3: "purple",
      Step4: "green",
    };

    const color = colorMap[step] || "gray";
    return `bg-${color}-600 border-${color}-600 text-${color}-600`;
  };

  return (
    <>
      {student && (
        <div className="h-auto w-64 rounded  border border-slate-200 bg-white pb-5 pt-2">
          {/* //Top */}

          {/* //DropdownMenu // */}
          <div className="flex flex-row items-center justify-end px-2">
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="">
                  <IoIosSettings
                    size={22}
                    className="cursor-pointer text-slate-700"
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuGroup>
                  <UpdateProfile student={student} mutate={mutate} />

                  <div className="mb-2 w-44 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                    <UpdateStatus
                      student={student}
                      mutate={mutate}
                      icon
                      title="Update Status"
                    />
                  </div>

                  <UpdateEmail
                    email={student.email}
                    operationName="Student Email"
                    mutate={mutate}
                  />
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className=" flex w-full flex-col  items-center justify-center pt-2">
            <Avatar name={student.fullName} round={true} size="90" />
            <div className="flex flex-row items-center">
              <div className="my-1 text-sm font-semibold text-slate-500 ">
                {student.fullName}
              </div>
              {student.isUsingApp && (
                <FcPhoneAndroid size={12} style={{ marginLeft: "5px" }} />
              )}
            </div>

            <div className="flex flex-row items-center">
              <div className="mb-1 text-xs font-medium text-slate-500">
                <Link href={`mailto:${student.email}`}>{student.email}</Link>
              </div>
              {student.isUnsubscribed && (
                <div>
                  <RiNotificationOffLine
                    size={12}
                    style={{ marginLeft: "5px", color: "red" }}
                  />
                </div>
              )}
            </div>

            <div className="text-xs font-light text-slate-500">
              <Link href={`tel:${student.phone}`}>{student.phone}</Link>
            </div>
            <div
              className={`mt-2 w-fit rounded border ${GetStatusColor(
                student.status,
              )} cursor-pointer bg-opacity-10 px-2 py-0.5 text-xs font-medium`}
            >
              <UpdateStatus
                student={student}
                mutate={mutate}
                title={student.status}
              />
            </div>
          </div>

          {/* //General Information... */}
          <div className="mt-5">
            <div className="flex h-7 w-full items-center bg-slate-200 pl-2 text-sm font-semibold text-slate-700">
              General
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Admin</p>
              <p className="text-[11px] font-light text-slate-800">
                {student.admin ? student.admin : "----"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Company</p>
              <p className="text-[11px] font-light text-slate-800">
                {student.type}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Country</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.country}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Referral</p>
              <p className="  text-[11px]  font-light text-slate-800">
                {student.referral ? student.referral : "No Referral"}
              </p>
            </div>

            <div className="flex h-7 w-full items-center bg-slate-200 pl-2 text-sm font-semibold text-slate-700">
              Profile Info
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Username</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.userName}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Age</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.age}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">
                Age category
              </p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.isChild ? "Child" : "Adult"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Gender</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.gender ? student.gender : "----"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Postcode</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.postCode}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Address</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.address}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Instrument</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.instrument}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Balance</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.balance}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Added on</p>
              <p className="text-[11px]  font-light text-slate-800">
                {moment(student.addedOn).format("DD MMM YYYY")}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">
                Last modified
              </p>
              <p className="  text-[11px]  font-light text-slate-800">
                {moment(student.lastModified).format("DD MMM YYYY")}
              </p>
            </div>

            <div className="flex h-7 w-full items-center bg-slate-200 pl-2 text-sm font-semibold text-slate-700">
              Others
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">
                Enquiry status
              </p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.enquiryStatus ? student.enquiryStatus : "-----"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">
                Submitted Step
              </p>
              {student.formSubmitStep ? (
                <p
                  className={`${GetStepColor(student.formSubmitStep)} w-fit rounded-md border bg-opacity-10 px-2 py-0.5 text-xs font-medium`}
                >
                  {student.formSubmitStep}
                </p>
              ) : (
                <p className="text-xs font-light text-slate-800">------</p>
              )}
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">
                Preferred lesson date
              </p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.firstLessonAvailabilityDate
                  ? moment(student.firstLessonAvailabilityDate).format(
                      "ddd, D MMM YY, h:mm A",
                    )
                  : "------"}
              </p>
            </div>

            <div className="my-2.5 pl-2">
              <p className="text-xs font-medium text-slate-800">
                Schedule Preference
              </p>
              <p className="text-xs font-medium text-slate-800">
                {!student.firstLessonAvailabilityDate &&
                student.isFlexibleFirstLesson
                  ? "Flexible"
                  : student.firstLessonAvailabilityDate &&
                      student.isFlexibleFirstLesson
                    ? "Flexible & preferred date"
                    : student.firstLessonAvailabilityDate &&
                        !student.isFlexibleFirstLesson
                      ? "Preferred date"
                      : "------"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Page title</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.pageTitle ? student.pageTitle : "-----"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Message</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.message ? student.message : "-----"}
              </p>
            </div>

            <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Note</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.note ? student.note : "-----"}
              </p>
            </div>

            {/* <div className=" my-2.5 pl-2">
              <p className=" text-xs font-medium text-slate-800">Frozen</p>
              <p className="text-[11px]  font-light text-slate-800">
                {student.isFrozen ? "Yes" : "No"}
              </p>
            </div> */}
          </div>
        </div>
      )}
    </>
  );
}

type tActionProps = {
  student: tStudentD;
  mutate: () => void;
};

function UpdateProfile({ student, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    fullName: yup.string().required("fullName is required!"),
    address: yup.string().required("Address is required!"),
    phone: yup.string().required("Phone number is required!"),
    age: yup.string().required("Age number is required!"),
    postCode: yup.string().required("postcode number is required!"),
    instrument: yup.string().required("Instrument number is required!"),
    type: yup.string().required("Type number is required!"),
    gender: yup.string().required("Gender is required!"),
  });

  const defaultValues = {
    fullName: student.fullName || "",
    address: student.address || "",
    phone: student.phone || "",
    age: student.age || "",
    postCode: student.postCode || "",
    instrument: student.instrument || "",
    type: student.type || "",
    message: student.message || "",
    note: student.note || "",
    gender: student.gender || "",
    enquiryStatus: student.enquiryStatus || "",
  };
  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(`/student/${student.id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success(`${student.fullName}'s profile updated successfully!`);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update student profile. Please try again!");
    }
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <FaUserEdit size={16} color="gray" className=" text-slate-600" />

          <span className="ml-1 text-xs font-medium text-slate-600">
            Update Profile
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[520px]">
        <DialogHeader>
          <DialogTitle>Update Student Profile</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter full name"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter address"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter phone"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="instrument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instrument Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter instrument name"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="postCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postcode</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter post code"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex w-full flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="age"
                  render={({ field }) => (
                    <FormItem className="w-1/2">
                      <FormLabel>Age</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter age"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem className="w-1/2">
                      <FormLabel>Company </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className="capitalize">
                              {form.getValues("type")
                                ? form.getValues("type")
                                : "Select country"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="US">US</SelectItem>
                          <SelectItem value="UK">UK</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex w-full flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Gender</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            {form.getValues("gender")
                              ? form.getValues("gender")
                              : "Select gender"}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Male">Male</SelectItem>
                          <SelectItem value="Female">Female</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="enquiryStatus"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Enquiry Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            {form.getValues("enquiryStatus")
                              ? form.getValues("enquiryStatus")
                              : "Select enquiry"}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Good">Good</SelectItem>
                          <SelectItem value="Normal">Normal</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Write a Message..."
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Note</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Write a note..."
                        className="text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* //Button Section... */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
