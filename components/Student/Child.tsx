"use client";

import { tStuChilds } from "types/student";
import React, { useState } from "react";
import Avatar from "react-avatar";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Button } from "@components/ui/button";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Input } from "@components/ui/input";
import { Dots } from "react-activity";
import { FiEdit } from "react-icons/fi";
import api from "@fetchers/BaseUrl";
import toast from "react-hot-toast";
import { getSession } from "next-auth/react";
import Delete from "@components/global/Delete";
import { RiDeleteBin6Line } from "react-icons/ri";

import { Calendar } from "components/ui/calendar";

import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { SlCalender } from "react-icons/sl";
import moment from "moment";
//  <SlCalender size={16} className="mr-2" />

type Props = {
  child: tStuChilds;
  mutate: () => void;
  childMutate: () => void;
};

export default function Child({ child, mutate, childMutate }: Props) {
  const GetStatusColor = (status: string) => {
    switch (status) {
      case "Regular":
        return "bg-lime-600 border-lime-600 text-lime-600";
      case "Gave up":
        return "bg-red-600 border-red-600 text-red-600";
      case "Looking for tutor":
        return "bg-blue-600 border-blue-600 text-blue-600";
      case "Green":
        return "bg-green-600 border-green-600 text-green-600";
      case "New":
        return "bg-amber-600 border-amber-600 text-amber-600";
      case "Pending 1st lesson":
        return "bg-violet-600 border-violet-600 text-violet-600";
      case "Multiple Children":
        return "bg-pink-600 border-pink-600";
      case "Call much later":
        return "bg-cyan-600 border-cyan-600 text-cyan-600";
      default:
        return "bg-teal-600 border-teal-600 text-teal-600";
    }
  };
  return (
    <div className="mb-2 grid grid-cols-8 items-center gap-x-2 rounded-md border border-slate-200 bg-white py-2 ">
      <div className="col-span-2 flex items-center pl-1">
        <Avatar name={child.name} round={true} size="40" />

        <div className="ml-1 space-y-0.5">
          <div className="max-w-20 overflow-hidden truncate text-xs font-semibold text-slate-700 xl:max-w-28">
            {child.name}
          </div>
          <div className="max-w-20 truncate text-[10px] font-light text-slate-600">
            {child.postCode}
          </div>

          <div className="max-w-20 truncate text-[9px] font-extralight text-slate-500">
            {child.gender || "N/A"}
          </div>
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="w-20 truncate text-xs font-medium text-slate-600">
          {child.lessons}
        </div>
        <div className="w-20 truncate text-[10px] font-light text-slate-600">
          {child.courses}
        </div>

        <div className="w-20 truncate text-[9px] font-extralight text-slate-600">
          {child.exams}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="w-20 truncate text-xs font-medium text-slate-600">
          {child.age}
        </div>

        <div className="w-20 truncate text-[10px] font-light text-slate-600">
          {child.dob
            ? moment(child.dob)
                .add(new Date().getTimezoneOffset() * -1, "minute")
                .format("DD MMM YY")
            : "N/A"}
        </div>
      </div>

      <div
        className={`mt-2 w-fit rounded border ${GetStatusColor(
          child.status,
        )} cursor-pointer bg-opacity-10 px-2 py-0.5 text-xs font-medium`}
      >
        {child.status ? child.status : "None"}
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[90px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {child.instrument ? child.instrument : "N"}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[96px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {child.expense}
        </div>
      </div>

      <div className="flex items-center justify-center text-xs font-medium text-slate-600">
        <div className="flex flex-row items-center space-x-1.5">
          <EditChild child={child} mutate={mutate} childMutate={childMutate} />
          <Delete
            apiUrl={`/student/removechild/${child.id}`}
            mutate={mutate}
            successMsg={`${child.name} has been successfully deleted!`}
            erroMsg="Faild to delete child!"
            icon={<RiDeleteBin6Line size={16} color="#ff0000" />}
            des="children's details"
          />
        </div>
      </div>
    </div>
  );
}

export function ChildHeader() {
  return (
    <div className="grid grid-cols-8 gap-x-2 pb-1 pt-2">
      <div className="col-span-2 space-y-0.5">
        <div className=" text-xs font-semibold text-slate-500">Name</div>
        <div className=" text-[10px] font-normal text-slate-500">Postcode</div>
        <div className="text-[9px] font-light text-slate-500">Gender</div>
      </div>

      <div className="space-y-0.5">
        <div className="text-xs font-semibold text-slate-500">Lessons</div>
        <div className="text-[10px]  font-normal text-slate-500">Courses</div>
        <div className="text-[9px] font-light text-slate-500">Exams</div>
      </div>

      <div className="space-y-0.5">
        <div className="text-xs font-semibold text-slate-500"> Age</div>
        <div className="text-[10px]  font-normal text-slate-500">Birthdate</div>
      </div>

      <div className="text-xs font-semibold text-slate-500">Status</div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Instrument
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Expense
      </div>
      <div className=" flex  justify-center text-xs font-semibold text-slate-500">
        Actions
      </div>
    </div>
  );
}

type tActionProps = {
  child: tStuChilds;
  mutate: () => void;
  childMutate: () => void;
};

function EditChild({ child, mutate, childMutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [birthDate, setBirthDate] = useState<Date | null>(
    child.dob ? new Date(child.dob) : null,
  );

  var resolver = yup.object().shape({
    name: yup.string().required("Child name is required!"),

    age: yup
      .string()
      .required("Age is required!")
      .matches(
        /^[a-zA-Z0-9]+$/,
        "Invalid age format. Use only letters and numbers, no special characters.",
      ),

    postCode: yup.string().required("Postcode is required!"),

    status: yup.string().required("Status is required!"),

    gender: yup.string().required("Gender is required!"),
  });

  const defaultValues = {
    name: child.name,
    age: child.age,
    postCode: child.postCode,
    status: child.status,
    instrument: child.instrument,
    gender: child.gender,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
      setBirthDate(child.dob ? new Date(child.dob) : null);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  // Handler to properly convert the Calendar's selection
  const handleDateSelect = (date: Date | undefined) => {
    setBirthDate(date || null); // Convert undefined to null
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const session = await getSession();
      const token = session?.token;

      const response = await api.put(
        `student/child/${child.id}`,
        {
          ...data,
          dob: birthDate ? moment(birthDate).format("YYYY-MM-DD") : null,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        childMutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Child informartion successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      if (error.response.data === "sae")
        toast.error("Student already exist! Please check the Email address!");
      setIsLoading(false);
      toast.error("Faild to update a child information!");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={ModalControl.reset}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <FiEdit size={14} color="#0000b3" />
      </DialogTrigger>
      <DialogContent className=" hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[440px]">
        <DialogHeader>
          <DialogTitle>Update Child Information</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Child Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter name"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex w-full flex-row items-center space-x-2.5 py-2">
              <FormField
                control={form.control}
                name="age"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Age</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter age"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="postCode"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Postcode</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter postCode"
                        {...field}
                        className=" text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex w-full flex-row items-center space-x-2.5 py-2">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Gender</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("gender")
                            ? form.getValues("gender")
                            : "Select gender"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="w-1/2">
                <FormLabel>Birthdate</FormLabel>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="birthDate"
                      variant={"outline"}
                      className="mt-1 flex w-full items-center justify-start bg-white px-2.5 font-normal"
                    >
                      <SlCalender size={16} className="mr-2" />

                      {birthDate ? (
                        moment(birthDate).format("MMM D, YYYY")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="single"
                      defaultMonth={birthDate ?? new Date()}
                      selected={birthDate ?? undefined} // Convert null to undefined
                      onSelect={handleDateSelect} // Use the handler instead of setBirthDate directly
                      numberOfMonths={1}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Status </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="capitalize">
                          {form.getValues("status")
                            ? form.getValues("status")
                            : "Select status"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value="Regular">Regular</SelectItem>
                        <SelectItem value="New">New</SelectItem>
                        <SelectItem value="Looking for tutor">
                          Looking for tutor
                        </SelectItem>
                        <SelectItem value="Green">Green</SelectItem>
                        <SelectItem value="Pending 1st lesson">
                          Pending 1st lesson
                        </SelectItem>
                        <SelectItem value="Group course only">
                          Group course only
                        </SelectItem>

                        <SelectItem value="Call much later">
                          Call much later
                        </SelectItem>
                        <SelectItem value="Call later">Call later</SelectItem>
                        <SelectItem value="Not active">Not active</SelectItem>
                        <SelectItem value="Old new">Old new</SelectItem>
                        <SelectItem value="Old green">Old green</SelectItem>
                        <SelectItem value="Gave up">Give Up</SelectItem>
                        <SelectItem value="Get to restart">
                          Get to restart
                        </SelectItem>
                        <SelectItem value="Bus event">Bus event</SelectItem>
                        <SelectItem value="Multiple Children">
                          Multiple Children
                        </SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="instrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Sent Instrument</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="capitalize">
                          {form.getValues("instrument") === "Y" ? "Yes" : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value={null as any}>No</SelectItem>
                        <SelectItem value="Y">Yes</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
