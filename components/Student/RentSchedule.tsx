"use client";

import { tRentScheduleL } from "types/student";
import React, { useEffect, useState } from "react";
import Avatar from "react-avatar";
import moment from "moment";
import { RiDeleteBin6Line } from "react-icons/ri";
import Delete from "@components/global/Delete";
import api from "@fetchers/BaseUrl";
import toast from "react-hot-toast";
import { getSession } from "next-auth/react";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Button } from "@components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Input } from "@components/ui/input";
import { Dots } from "react-activity";
import { BooleanToString, StringToBoolean } from "core/Converters";
import { FiEdit } from "react-icons/fi";

type Props = {
  rSchedule: tRentScheduleL;
  mutate: any;
};
export default function RentSchedule({ rSchedule, mutate }: Props) {
  return (
    <div className="mb-2 grid grid-cols-8 items-center gap-x-2 rounded-md border border-slate-200 bg-white py-2">
      <div className="flex items-center pl-1">
        <Avatar name={rSchedule.child} round={true} size="40" />
        <div className="ml-1">
          <div className="max-w-[45px] overflow-hidden truncate text-xs font-semibold text-slate-700">
            {rSchedule.child}
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center pl-1.5">
        <div className="max-w-[80px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {rSchedule.instrument}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[80px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {rSchedule.amount}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className=" text-xs font-medium text-slate-600">
          {moment(rSchedule?.startFrom)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YY")}
        </div>
      </div>

      <div className="flex items-center justify-center">
        {rSchedule?.isActive === true ? (
          <div className="max-w-[80px] overflow-hidden truncate text-xs font-medium text-slate-600">
            {moment(rSchedule?.nextPay)
              .add(new Date().getTimezoneOffset() * -1, "minute")
              .format("DD MMM YY")}
          </div>
        ) : (
          <div className="text-xs font-medium text-slate-600">Inactive</div>
        )}
      </div>

      <div className="flex items-center justify-center">
        {rSchedule?.isActive === true ? (
          <div className="w-fit rounded  border border-green-600 bg-green-600 bg-opacity-10 px-2.5 py-0.5 text-xs font-medium text-green-600">
            Active
          </div>
        ) : (
          <div className="max-w-[96px] overflow-hidden truncate text-xs font-medium text-slate-600">
            {moment(rSchedule?.endedAt)
              .add(new Date().getTimezoneOffset() * -1, "minute")
              .format("DD MMM YY")}
          </div>
        )}
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[80px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {rSchedule.rents}
        </div>
      </div>

      <div className="flex items-center justify-center text-xs font-medium text-slate-600">
        <div className="flex flex-row items-center space-x-1.5">
          <EditRentSchedule rSchedule={rSchedule} mutate={mutate} />
          <Delete
            apiUrl={`/instrument/rent-schedule/${rSchedule?.id}`}
            mutate={mutate}
            successMsg={`${rSchedule.child}'s rent schedule has been deleted successfully!`}
            erroMsg="Faild to remove Rent schedule!"
            icon={<RiDeleteBin6Line size={16} color="#ff0000" />}
            des={`"${rSchedule.child}" rental details`}
          />
        </div>
      </div>
    </div>
  );
}

export function RentScheduleHeader() {
  return (
    <div className="grid grid-cols-8 gap-x-2 pb-1 pt-2">
      <div className="text-xs font-semibold text-slate-500">Child Name</div>

      <div className="flex justify-center  pl-1.5 text-xs font-semibold text-slate-500">
        Instrument
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Amount
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Start Date
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Next Pay
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        End Date
      </div>
      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Rents
      </div>
      <div className=" flex  justify-center text-xs font-semibold text-slate-500">
        Actions
      </div>
    </div>
  );
}

function EditRentSchedule({ rSchedule, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    startFrom: yup.date().required("Start date is required!"),
    nextPay: yup.date().required("Next pay date is required!"),
    instrument: yup.string().required("Instrument name is required!"),
    amount: yup
      .number()
      .typeError("Enter a valid number for the amount. This field is required.")
      .required("Amount is required!"),
  });

  const defaultValues = {
    startFrom: rSchedule.startFrom ? new Date(rSchedule.startFrom) : null,
    nextPay: rSchedule.nextPay ? new Date(rSchedule.nextPay) : null,
    instrument: rSchedule.instrument,
    amount: rSchedule.amount,
    isActive: BooleanToString(rSchedule.isActive),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    const { startFrom, nextPay, isActive } = data;

    setIsLoading(true);
    // console.log(data);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/instrument/rent-schedule/${rSchedule.id}`,
        {
          ...data,
          startFrom: moment(startFrom).format("YYYY-MM-DD"),
          nextPay: moment(nextPay).format("YYYY-MM-DD"),
          isActive: StringToBoolean(isActive),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();

        setIsLoading(false);
        setIsOpen(false);
        toast.success("Rent schedule information updated successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update the rent schedule information!");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={ModalControl.reset}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="ml-1 cursor-pointer text-slate-600">
          <FiEdit size={14} color="#0000b3" />
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Rent Schedule</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="instrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Instrument Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Ex. Piano"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="startFrom"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="nextPay"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Next Pay</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Is Active</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="capitalize">
                          {field.value === "true" ? "Yes" : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
