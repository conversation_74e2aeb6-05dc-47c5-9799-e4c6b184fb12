"use client";

import { tAssignedTutor } from "types/student";
import React from "react";
import Avatar from "react-avatar";
import Delete from "@components/global/Delete";
import { RiDeleteBin6Line } from "react-icons/ri";
import Link from "next/link";

type Props = {
  aTutor: tAssignedTutor;
  mutate: () => void;
};

export default function AssignedTutor({ aTutor, mutate }: Props) {
  return (
    <div className="mb-2 grid grid-cols-9 items-center gap-x-4 rounded-md border border-slate-200 bg-white py-2 ">
      <div className="col-span-2 flex items-center pl-1">
        <Avatar name={aTutor.tutorName} round={true} size="40" />
        <div className="ml-1">
          <Link
            href={`/tutors/${aTutor.tutorId}`}
            target="_blank"
            className="block max-w-[80px] overflow-hidden truncate text-xs font-semibold text-slate-700 hover:cursor-pointer hover:underline"
          >
            {aTutor.tutorName}
          </Link>

          <div className="max-w-[80px] overflow-hidden truncate text-[10px] font-light text-slate-600">
            {aTutor.childName}
          </div>
        </div>
      </div>

      <div className="col-span-2">
        <div className="max-w-[144px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {aTutor.prices ? aTutor.prices : "None"}
        </div>
        <div className="max-w-[144px] overflow-hidden truncate text-[10px] font-light text-slate-600">
          {aTutor.commissions ? aTutor.commissions : "None"}
        </div>
      </div>

      <div className="col-span-2">
        <div className="max-w-[144px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {aTutor.onlinePrices ? aTutor.onlinePrices : "None"}
        </div>
        <div className="max-w-[144px]overflow-hidden truncate text-[10px] font-light text-slate-600">
          {aTutor.onlineCommissions ? aTutor.onlineCommissions : "None"}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[80px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {aTutor.type}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[80px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {aTutor.instrument}
        </div>
      </div>

      <div className="flex items-center justify-center text-xs font-medium text-slate-600">
        <div className="flex flex-row items-center">
          <Delete
            apiUrl={`/assign/${aTutor.id}`}
            mutate={mutate}
            successMsg={"Assigned Tutor successfully Deleted!"}
            erroMsg="Faild to remove Rent schedule!"
            icon={<RiDeleteBin6Line size={16} color="#ff0000" />}
            des={`"${aTutor.tutorName}" details`}
          />
        </div>
      </div>
    </div>
  );
}

export function AssignTutorHeader() {
  return (
    <div className="grid grid-cols-9 gap-x-4 pb-1 pt-2">
      <div className="col-span-2">
        <div className="text-xs font-semibold text-slate-500">Tutor</div>
        <div className="text-[10px] font-normal text-slate-500">Child</div>
      </div>

      <div className="col-span-2">
        <div className="text-xs font-semibold text-slate-500">Prices</div>
        <div className="text-[10px] font-normal text-slate-500">
          Commissions
        </div>
      </div>

      <div className="col-span-2">
        <div className="text-xs font-semibold text-slate-500">
          online Prices
        </div>
        <div className="text-[10px] font-normal text-slate-500">
          Online Commissions
        </div>
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Type
      </div>

      <div className="flex  justify-center text-xs font-semibold text-slate-500">
        Instrument
      </div>

      <div className=" flex  justify-center text-xs font-semibold text-slate-500">
        Actions
      </div>
    </div>
  );
}
