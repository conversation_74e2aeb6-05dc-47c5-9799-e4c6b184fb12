import React, { useEffect, useState } from "react";
import {
  MdOutlineDashboardCustomize,
  MdOutlineAccountCircle,
  MdOutlineEngineering,
  MdEvent,
  MdNote,
  MdPayments,
  MdOutlinePendingActions,
} from "react-icons/md";
import { MdProductionQuantityLimits } from "react-icons/md";

import SidebarItem from "./SidebarItem";

import styles from "styles/Sidebar.module.css";
import { FaBars, FaMapMarkedAlt } from "react-icons/fa";

import { TbDeviceMobileMessage, TbTemplate } from "react-icons/tb";
import { RiUserSearchFill } from "react-icons/ri";
import { VscFeedback } from "react-icons/vsc";
import { TbDeviceDesktopAnalytics } from "react-icons/tb";
import { LuListRestart } from "react-icons/lu";
import { RiUserShared2Fill } from "react-icons/ri";
import { BsFillGiftFill } from "react-icons/bs";
import { FiUsers } from "react-icons/fi";
import { MdLibraryMusic } from "react-icons/md";
import { BsShop } from "react-icons/bs";
import { MdOutlinePoll } from "react-icons/md";

export default function Sidebar(props: any) {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(true);

  const CollapseOperation = () => {
    setIsCollapsed(!isCollapsed);
  };

  useEffect(() => {
    if (props.isCollapsed) setIsCollapsed(true);
  }, [props.isCollapsed]);

  return (
    <div className="flex h-screen w-full flex-row">
      <div
        className={
          isCollapsed
            ? "flex w-[60px] flex-col overflow-y-auto overflow-x-hidden bg-gray-900 pt-5"
            : "animate-sidebarOpenAmination flex  w-[var(--sidebar-width)] flex-col overflow-y-auto overflow-x-hidden bg-gray-900 pt-5 duration-500"
        }
      >
        <div
          className={
            isCollapsed
              ? "flex h-auto w-full flex-row items-center justify-center border-b border-b-gray-900 pb-5"
              : "flex h-auto w-full flex-row items-center justify-around border-b border-b-gray-900 pb-5 "
          }
        >
          <span
            className={
              isCollapsed
                ? "hidden"
                : "text-[22px] font-medium text-[var(--font-color-bright)]"
            }
          >
            MuseCool
          </span>
          <div onClick={CollapseOperation} className="cursor-pointer">
            <FaBars color="white" size={25} />
          </div>
        </div>

        <div
          className={
            isCollapsed
              ? "mt-1 flex w-full flex-col items-center p-0"
              : "mt-1 w-full pl-3"
          }
        >
          <SidebarItem
            icon={<MdOutlineDashboardCustomize size={20} />}
            name="Dashboard"
            link="/"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<MdOutlineAccountCircle size={20} />}
            name="Students"
            link="/students"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<MdOutlineEngineering size={20} />}
            name="Tutors"
            link="/tutors"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<FaMapMarkedAlt size={18} />}
            name="Map"
            link="/map"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<MdOutlinePendingActions size={20} />}
            name="Pfl"
            link="/pfl"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<MdNote size={19} />}
            name="Lessons"
            link="/lesson"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<MdEvent size={20} />}
            name="Events"
            link="/events"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<MdPayments size={19} />}
            name="Payments"
            link="/payments"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<RiUserSearchFill size={22} />}
            name="LFT"
            link="/lft"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<TbTemplate size={20} />}
            name="Templates"
            link="/templates"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<VscFeedback size={20} />}
            name="Feedback"
            link="/feedback"
            isCollapsed={isCollapsed}
          />

          {/* <SidebarItem
            icon={<TbDeviceDesktopAnalytics size={20} />}
            name="Analytics"
            link="/analytics"
            isCollapsed={isCollapsed}
          /> */}
          <SidebarItem
            icon={<MdProductionQuantityLimits size={20} />}
            name="Product"
            link="/products"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<LuListRestart size={20} />}
            name="Restarts"
            link="/restarts"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<BsFillGiftFill size={18} />}
            name="Referral"
            link="/referral"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<TbDeviceMobileMessage size={22} />}
            name="Notification"
            link="/notifications"
            isCollapsed={isCollapsed}
          />
          <SidebarItem
            icon={<FiUsers size={22} />}
            name="Repeat Enquiries"
            link="/repeat-enquiries"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<MdLibraryMusic size={22} />}
            name="Concerts"
            link="/concerts"
            isCollapsed={isCollapsed}
          />

          <SidebarItem
            icon={<MdOutlinePoll size={22} />}
            name="Surveys"
            link="/surveys"
            isCollapsed={isCollapsed}
          />

          {/* <SidebarItem
            icon={<BsShop size={22} />}
            name="Shop Registration"
            link="/shop-registration"
            isCollapsed={isCollapsed}
          /> */}

          {/* <SidebarItem
            icon={<RiUserShared2Fill size={20} />}
            name="Income leads"
            link="/income-leads"
            isCollapsed={isCollapsed}
          /> */}
        </div>
      </div>

      <div
        className={
          isCollapsed
            ? "w-[calc(100%-60px)] pb-20"
            : "w-[calc(100%-var(--sidebar-width))] pb-20"
        }
      >
        {props.children}
      </div>
    </div>
  );
}
