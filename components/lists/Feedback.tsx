"use client";

import { tFeedbackL } from "types/feedback";
import React from "react";
import Avatar from "react-avatar";
import moment from "moment";

import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  Too<PERSON>ipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";
import { MdOutlineStar } from "react-icons/md";

import Link from "next/link";

type Props = {
  feedback: tFeedbackL;
  mutate: () => void;
};

export default function Feedback({ feedback, mutate }: Props) {
  return (
    <div className="mb-1.5 grid grid-cols-5 items-center gap-x-4 rounded-md border border-gray-200 bg-white  py-2.5">
      <div className="col-span-2 flex items-center pl-1.5">
        <Avatar name={feedback.tutor.fullName} round={true} size="45" />
        <div className="ml-2.5 flex flex-col space-y-0.5">
          <Link
            href={`/tutors/${feedback.tutor.id}`}
            target="_blank"
            className="block max-w-[180px] overflow-hidden truncate text-sm font-semibold text-gray-800 hover:underline"
          >
            {feedback.tutor.fullName}
          </Link>

          <div className="max-w-[180px] overflow-hidden truncate text-[11px] font-light text-gray-500">
            {feedback.student.fullName}
          </div>
        </div>
      </div>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <p
              className={`max-w-[180px] overflow-hidden truncate text-xs  ${
                feedback.feedback
                  ? "font-medium text-gray-800"
                  : " font-thin text-gray-400"
              }`}
            >
              {feedback.feedback ? feedback.feedback : "No feedback found"}
            </p>
          </TooltipTrigger>
          {feedback.feedback && (
            <TooltipContent className=" max-w-xs break-words rounded border  bg-slate-400 px-2.5 py-1 text-[10px]  font-medium text-slate-50">
              {feedback.feedback}
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>

      <div className="text-center text-xs font-medium text-gray-800">
        {moment(feedback.dateTime)
          .add(new Date().getTimezoneOffset() * -1, "minute")
          .format("DD MMM YYYY")}
      </div>

      <div className=" flex flex-row items-center justify-center space-x-1">
        {Array.from({ length: feedback.rating || 0 }, (_, index) => (
          <MdOutlineStar key={index} size={16} color="#25aa89" />
        ))}
      </div>
    </div>
  );
}

export function FeedbackListHeader() {
  return (
    <div className="grid grid-cols-5 gap-x-4 py-2">
      <div className="col-span-2 space-y-0.5">
        <div className="text-sm font-medium text-gray-600">Tutor Name</div>

        <div className="text-xs font-normal text-gray-500">Student Name</div>
      </div>

      <div className="text-sm font-medium text-gray-600">Feedback</div>

      <div className="text-center text-sm font-medium text-gray-600">Date</div>

      <div className=" text-center text-sm font-medium text-gray-600">
        Rating
      </div>
    </div>
  );
}
