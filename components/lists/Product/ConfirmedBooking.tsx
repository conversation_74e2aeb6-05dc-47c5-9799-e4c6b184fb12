"use client";

import { tConfirmBookingL } from "types/product";
import React from "react";
import moment from "moment";
import Avatar from "react-avatar";
import { MdOutlinePermMedia, MdOutlineNoPhotography } from "react-icons/md";

type Props = {
  booking: tConfirmBookingL;
  mutate: () => void;
};

export default function ConfirmedBooking({ booking, mutate }: Props) {
  return (
    <div className="relative mb-1.5 grid grid-cols-6 items-center gap-x-2 rounded-md  border border-gray-200 bg-white px-1.5 py-2.5">
      <div className="col-span-2 flex flex-row items-center">
        <Avatar name={booking.fields.name} round={true} size="56" />

        <div className="ml-2 flex flex-col space-y-1">
          <div className="flex flex-row items-center">
            <div className="max-w-44 overflow-hidden truncate text-sm font-semibold text-gray-800 hover:underline">
              {booking.fields.name}
            </div>

            {booking.fields.dwPermission ? (
              <MdOutlinePermMedia className="ml-1 text-green-600" size={13} />
            ) : (
              <MdOutlineNoPhotography className="ml-1 text-red-600" size={13} />
            )}
          </div>

          <div className="max-w-44 overflow-hidden truncate text-[11px] font-light text-gray-500">
            {booking.email}
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-1">
        <div className="max-w-40 overflow-hidden truncate text-xs font-medium text-gray-800">
          {booking.ticket}
        </div>
        <div className="max-w-36 overflow-hidden truncate text-[11px] font-light text-gray-500">
          {booking.quantity}
        </div>
      </div>

      <div className="space-y-1">
        <div className="max-w-36 overflow-hidden truncate text-xs font-medium text-gray-800">
          {moment(booking.dateTime)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
        <div className="max-w-36 overflow-hidden truncate text-[11px] font-light text-gray-500">
          {booking.intent}
        </div>
      </div>

      <div className="flex flex-col items-center justify-center space-y-1">
        <div className="max-w-36 overflow-hidden truncate text-xs font-medium text-gray-800">
          {`${booking.country === "United Kingdom" ? "£" : "$"}${booking.amount}`}
        </div>
      </div>
    </div>
  );
}

export function ConfirmedBookingHeader() {
  return (
    <div className="grid grid-cols-6 items-start gap-x-2 px-1.5 py-2">
      <div className="col-span-2 space-y-1">
        <div className="text-sm font-medium text-gray-500">Parent Name</div>
        <div className="text-xs font-normal text-gray-500">Email</div>
      </div>

      <div className="col-span-2 space-y-1">
        <div className="text-sm font-medium text-gray-500">Ticket Title</div>
        <div className="text-xs font-normal text-gray-500">Quantity</div>
      </div>

      <div className="space-y-1">
        <div className="text-sm font-medium text-gray-500">Date</div>
        <div className="text-xs font-normal text-gray-500">Intent</div>
      </div>

      <div className="flex flex-col items-center justify-center space-y-1">
        <div className="items-center text-sm font-medium text-gray-500">
          Amount
        </div>
      </div>
    </div>
  );
}
