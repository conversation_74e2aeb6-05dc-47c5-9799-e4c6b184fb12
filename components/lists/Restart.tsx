"use client";

import { tRestartL } from "types/restart";
import React from "react";
import Avatar from "react-avatar";
import Link from "next/link";
import moment from "moment";
import Image from "next/image";
import UKFlag from "@assets/Uk.png";
import USFlag from "@assets/US.png";

import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "components/ui/hover-card";
import useSWR from "swr";
import { tStudentC } from "types/student";
import { fetcher } from "@fetchers/fetcher";
import { Dots } from "react-activity";

type Props = {
  restart: tRestartL;
  mutate: () => void;
};

export default function Restart({ restart, mutate }: Props) {
  const {
    data: Comments,
    error: errorC,
    mutate: mutateC,
    isLoading: isLoadingC,
  } = useSWR<Array<tStudentC>>(
    restart.studentId ? `/student/${restart.studentId}/comments?items=3` : null,
    fetcher,
  );

  return (
    <div className="relative mb-1.5 grid grid-cols-6 items-center gap-x-2.5 rounded-md  border border-gray-200 bg-white px-1.5 py-2.5">
      <div className="col-span-2 flex flex-row items-center">
        <Avatar name={restart.student} round={true} size="50" />
        <div className="ml-2 flex flex-col space-y-1">
          <div className="flex flex-row items-center">
            <Link
              href={`/students/${restart.studentId}`}
              target="_blank"
              className="block max-w-44 overflow-hidden truncate text-sm font-semibold text-gray-800 hover:underline xl:max-w-52"
            >
              {restart.student}
            </Link>

            {restart?.country && (
              <Image
                src={restart.country === "United Kingdom" ? UKFlag : USFlag}
                alt={restart?.country}
                className="ml-1 h-4 w-4"
              />
            )}
          </div>
          <Link
            href={`/tutors/${restart.tutorId}`}
            target="_blank"
            className="max-w-44 overflow-hidden truncate text-[10px] text-sm font-light text-gray-500  hover:cursor-pointer hover:underline xl:max-w-52"
          >
            {restart.tutor}
          </Link>
        </div>
      </div>

      <div className="col-span-2">
        {Comments?.length ? (
          <HoverCard>
            <HoverCardTrigger asChild>
              <div className="cursor-pointer">
                <div className="line-clamp-2 max-w-52 break-words text-xs font-normal text-gray-600 xl:max-w-72 2xl:max-w-80">
                  {Comments[0].body}
                </div>

                <div className="flex w-full justify-start pt-1 text-[9px] font-light text-slate-500">
                  {moment(Comments[0].dateTime)
                    .add(new Date().getTimezoneOffset() * -1, "minute")
                    .fromNow()}
                </div>
              </div>
            </HoverCardTrigger>
            <HoverCardContent className="md:w-72 lg:w-80">
              {Comments?.map((comment: tStudentC) => {
                return (
                  <div key={comment.id} className="h-fit w-full py-2.5">
                    <div className="text-xs font-semibold text-slate-600">
                      {comment.adminName}
                    </div>

                    {/* //Body Section  */}
                    <div className="my-1 h-fit w-full rounded-md border border-slate-200 bg-white px-2 py-2 ">
                      <div className="w-full break-words text-[10.5px] font-normal text-slate-600">
                        {comment.body ? comment.body : "........... "}
                      </div>
                    </div>

                    <div className="flex w-full justify-end text-[9px] font-light text-slate-600">
                      {moment(comment?.dateTime)
                        .add(new Date().getTimezoneOffset() * -1, "minute")
                        .fromNow()}

                      {/* {moment(comment?.dateTime)
                      .add(new Date().getTimezoneOffset() * -1, "minute")
                      .format("DD MMM YYYY")}
                    ,{" "}
                    {moment(comment.dateTime)
                      .add(new Date().getTimezoneOffset() * -1, "minute")
                      .format("h:mm A")} */}
                    </div>
                  </div>
                );
              })}
            </HoverCardContent>
          </HoverCard>
        ) : (
          <div className="text-xs font-normal text-gray-600 opacity-60">
            {isLoadingC ? <Dots className="pl-6" /> : "No comments found"}
          </div>
        )}
      </div>

      <div className="flex items-center justify-center">
        <div className="text-xs font-medium text-gray-600">
          {restart.lastLesson
            ? moment(restart.lastLesson)
                .add(new Date().getTimezoneOffset() * -1, "minute")
                .fromNow()
            : "---"}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="text-xs font-medium text-gray-600">
          {`${restart.country === "United Kingdom" ? "£" : "$"}${Number.isInteger(restart.balance) ? restart.balance : restart.balance.toFixed(2)}`}
        </div>
      </div>
    </div>
  );
}

export function RestartListHeader({}) {
  return (
    <div className="grid grid-cols-6 items-start gap-x-2.5 px-1.5 py-2">
      <div className=" col-span-2 space-y-1">
        <div className="text-sm font-medium text-gray-500">Student Name</div>
        <div className="text-xs font-normal text-gray-500">Tutor Name</div>
      </div>

      <div className="col-span-2 space-y-1">
        <div className="text-sm font-medium text-gray-500">Comments</div>
      </div>

      <div className="flex flex-col items-center justify-center">
        <div className="text-sm font-medium text-gray-500">Last Lesson</div>
      </div>

      <div className="flex flex-col items-center justify-center">
        <div className="text-center text-sm font-medium text-gray-500">
          Balance
        </div>
      </div>
    </div>
  );
}
