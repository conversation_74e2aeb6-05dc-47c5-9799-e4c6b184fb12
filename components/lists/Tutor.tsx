"use client";

import React, { useEffect, useState } from "react";
import Avatar from "react-avatar";
import { tTutorL } from "types/tutor";
import { TbFileCertificate, TbListDetails } from "react-icons/tb";
import { HiClipboardDocumentCheck } from "react-icons/hi2";
import { GenerateQuery } from "core/Query";

import { Button } from "components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "components/ui/sheet";

import moment from "moment";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

import { RxDotFilled } from "react-icons/rx";
import { FcOk } from "react-icons/fc";
import Selects from "react-select";
import { IoIosSettings } from "react-icons/io";
import UpdateEmail from "@components/global/UpdateEmail";

type Props = {
  Tutor: tTutorL;
  mutate: any;
};

const Tutor = ({ Tutor, mutate }: Props) => {
  // const router: any = useRouter();

  // const handleOnClick = () => {
  //   router.push(`/tutors/${Tutor.id}`);
  // };

  return (
    <div className="mb-1.5 grid grid-cols-7  items-center gap-x-4 rounded-md border border-gray-200 bg-white py-2.5">
      <div className="relative col-span-2">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className=" absolute -top-1 left-1">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-1.5 p-1">
                <Link
                  href={`/tutors/${Tutor.id}`}
                  target="_blank"
                  className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <TbListDetails size={16} color="gray" />
                  <span className="ml-1 text-xs font-medium text-slate-600">
                    View
                  </span>
                </Link>
                <UpdateEmail
                  email={Tutor?.email}
                  operationName="Tutor Email"
                  mutate={mutate}
                />
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-5 flex flex-row items-center ">
          <Avatar name={Tutor.fullName} round={true} size="45" />
          <div className="ml-1.5 space-y-0.5">
            <div className="flex items-center">
              {Tutor.isActive && (
                <RxDotFilled size={22} color="#009900" className=" ml-[-8px]" />
              )}

              <Link
                href={`/tutors/${Tutor.id}`}
                target="_blank"
                className="max-w-[176px] overflow-hidden truncate text-sm font-semibold text-gray-800 hover:cursor-pointer hover:underline"
              >
                {Tutor.fullName}
              </Link>
            </div>

            <div className="max-w-[176px] overflow-hidden truncate  text-[11px] font-light text-gray-600 ">
              {Tutor.address ? Tutor.address : "....."}
            </div>
            <div className="text-[10px] font-extralight text-gray-500">
              {Tutor.country}
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-0.5 ">
        <div className="max-w-[176px] overflow-hidden truncate text-[13px] font-normal text-gray-800">
          {Tutor.email}
        </div>
        <div className="max-w-[176px] overflow-hidden truncate  text-[11px]  text-gray-600">
          {Tutor.phoneNumber}
        </div>
        <div className=" w-fit overflow-hidden truncate rounded-md bg-gray-200 px-2 py-[1px]  text-[9px] font-extralight text-gray-500">
          {Tutor.postCode ?? "...."}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="text-[13px] font-medium text-gray-800">
          {Tutor.type ?? "...."}
        </div>
        <div className="text-[12px] font-normal  text-gray-600">
          {Tutor.rank}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="text-[13px] font-normal text-gray-800">
          {moment(Tutor.joined)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
        <div className="text-[10px] font-normal  text-gray-600">
          {Tutor.assigned}
        </div>
      </div>

      <div className="space-y-0.5">
        {Tutor.cv ? (
          <div className="flex items-center">
            <Link href={Tutor.cv} target="_blank">
              <TbFileCertificate
                size={18}
                className=" mr-1  cursor-pointer text-gray-700 hover:text-gray-900"
              />
            </Link>
            {Tutor.haveGoodCv && <FcOk size={18} color="#006600" />}
          </div>
        ) : (
          <div className="text-[10px] font-light  text-gray-600">
            Not Given{" "}
          </div>
        )}

        {Tutor.dbs ? (
          <Link href={Tutor.dbs} target="_blank">
            <HiClipboardDocumentCheck
              size={18}
              className="mt-1 cursor-pointer text-gray-600  hover:text-gray-800"
            />
          </Link>
        ) : (
          <div className="text-[10px] font-light  text-gray-600">Not Given</div>
        )}
      </div>
    </div>
  );
};

export default Tutor;

type tTutorHeader = {
  query: string;
  updateQuery: any;
  name: string;
  setName: any;
  updatePage: any;
  pageReset: any;
};

type tSelector = {
  value: any;
  label: string;
};

export function TutorListHeader({
  query,
  updateQuery,
  name,
  setName,
  updatePage,
  pageReset,
}: tTutorHeader) {
  const [isPageActive, setIsPageActive] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);
  // const [isCountryMOpen, setIsCountryMOpen] = useState(false);

  const [country, setCountry] = useState<string>("");
  const [category, setCategory] = useState<string>("");
  const [instrument, setInstrument] = useState<string>("");
  const [rank, setRank] = useState<Array<tSelector>>();

  const [haveGoodCv, setHaveGoodCv] = useState<string>("");
  const [haveDbs, setHaveDbs] = useState<string>("");
  const [isActive, setIsActive] = useState<string>("");
  const [assigned, setAssigned] = useState<string>("");

  const UpdateState = {
    Country: (e: any) => {
      setCountry(e);
    },
    Category: (e: any) => {
      setCategory(e);
    },
    Instrument: (e: any) => {
      setInstrument(e);
    },

    HaveGoodCv: (e: any) => {
      setHaveGoodCv(e);
    },
    HaveDbs: (e: any) => {
      setHaveDbs(e);
    },
    IsActive: (e: any) => {
      setIsActive(e);
    },
  };

  const Action = {
    generateQuery: () => {
      var newRank = rank?.map((x) => x.value).toString();
      if (name.length > 2) {
        updateQuery(
          GenerateQuery({
            name,
            country,
            instrument,
            rank: newRank,
            haveGoodCv,
            haveDbs,
            isActive,
            category,
            assigned,
          }),
        );
      } else {
        updateQuery(
          GenerateQuery({
            country,
            instrument,
            rank: newRank,
            haveGoodCv,
            haveDbs,
            isActive,
            category,
            assigned,
          }),
        );
      }
    },

    Reset: () => {
      setInstrument("");
      setCountry("");
      setCategory("");
      setRank([]);
      setHaveGoodCv("");
      setHaveDbs("");
      setIsActive("");
      setAssigned("");
      updateQuery("");
      pageReset("");
      setName("");
    },
  };

  const reset = (value: any) => {
    setIsOpen(value);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get("p")) {
      updatePage(Number(urlParams.get("p")));
    }

    urlParams.delete("p");

    if (urlParams.toString() !== "") {
      // Action.UpdateQuery("&" + search.toString());
      const newRank = urlParams
        .get("rank")
        ?.split(",")
        .map((x) => {
          return { value: x, label: x };
        });

      setRank(newRank || []);
      setName(urlParams.get("name") || "");
      setCountry(urlParams.get("country") || "");
      setCountry(urlParams.get("category") || "");

      setInstrument(urlParams.get("instrument") || "");
      setHaveGoodCv(urlParams.get("haveGoodCv") || "");
      setHaveDbs(urlParams.get("haveDbs") || "");
      setIsActive(urlParams.get("urlParams") || "");
      setAssigned(urlParams.get("assigned") || "");

      updateQuery("&" + urlParams.toString());
    }

    setIsPageActive(true);
  }, []);

  useEffect(() => {
    if (isPageActive) {
      Action.generateQuery();
    }
  }, [name]);

  const ranks: Array<tSelector> = [
    { value: "1", label: "1" },
    { value: "2", label: "2" },
    { value: "3", label: "3" },
    { value: "4", label: "4" },
  ];

  // const resetCountryM = (value: any) => {
  //   setIsCountryMOpen(value);
  //   setCountry("");
  // };
  // const onCountryOpen = () => {
  //   setIsCountryMOpen(true);
  // };

  return (
    <Sheet onOpenChange={reset} open={isOpen}>
      <SheetTrigger asChild onClick={onOpen}>
        <div className="grid grid-cols-7 gap-x-4 py-1.5">
          <div className="col-span-2 space-y-0.5">
            <div className=" cursor-pointer text-sm font-medium text-gray-500">
              Name
            </div>
            <div className=" cursor-pointer text-xs font-normal text-gray-500">
              Address
            </div>

            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Country
            </div>
          </div>

          <div className="col-span-2 space-y-0.5">
            <div className=" cursor-pointer text-sm font-medium text-gray-500">
              Email
            </div>
            <div className=" cursor-pointer text-xs font-normal text-gray-500">
              Phone
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Postcode
            </div>
          </div>

          <div className="space-y-0.5">
            <div className=" cursor-pointer text-sm font-medium text-gray-500">
              Type
            </div>
            <div className=" cursor-pointer text-xs font-normal text-gray-500">
              Rank
            </div>
          </div>

          <div className="space-y-0.5">
            <div className=" cursor-pointer text-sm font-medium text-gray-500">
              Joined
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Assigned
            </div>
          </div>

          <div className="space-y-0.5">
            <div className=" cursor-pointer text-sm font-medium text-gray-500">
              CV
            </div>
            <div className=" cursor-pointer text-xs font-normal text-gray-500">
              DBS
            </div>
          </div>
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter Tutor Information</SheetTitle>
          {/* <SheetDescription>
              Make changes to your profile here. Click save when you're done.
            </SheetDescription> */}
        </SheetHeader>

        <div className="hide-scrollbar h-full w-full overflow-y-scroll px-1 py-4">
          <div className="text-xs font-semibold text-slate-700">Rank</div>
          <div className="mt-1">
            <Selects
              isMulti
              id="multiRanks"
              name="multiRanks"
              options={ranks}
              menuPosition="fixed"
              isSearchable
              placeholder="Select Ranks"
              value={rank}
              onChange={(e: any) => setRank(e)}
              className="border-slate-300 text-xs font-normal text-gray-700 "
            />
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Country
          </div>

          <div className="mt-1">
            <Select
              onValueChange={UpdateState.Country}
              defaultValue={country}
              // open={isCountryMOpen}
              // onOpenChange={resetCountryM}
            >
              <SelectTrigger
                // onClick={onCountryOpen}
                className="borde mb-2 w-full rounded-sm border-slate-300 bg-white"
              >
                {/* <SelectValue placeholder="Select a type" /> */}
                <div className="">{country ? country : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                  <SelectItem value="United States">United States</SelectItem>
                  <SelectItem value="Ukraine">Ukraine</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Instrument
          </div>
          <div className=" mt-1">
            <Select
              onValueChange={UpdateState.Instrument}
              defaultValue={instrument}
            >
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">
                  {instrument ? instrument : "All"}
                </div>
              </SelectTrigger>

              <SelectContent className="hide-scrollbar h-52 overflow-y-scroll py-2 text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="ABRSM Music Theory Grades">
                    ABRSM Music Theory Grades
                  </SelectItem>
                  <SelectItem value="Bass Guitar">Bass Guitar</SelectItem>
                  <SelectItem value="Cello">Cello</SelectItem>
                  <SelectItem value="Clarinet">Clarinet</SelectItem>
                  <SelectItem value="Double Bass">Double Bass</SelectItem>
                  <SelectItem value="Drums">Drums</SelectItem>
                  <SelectItem value="Flute">Flute</SelectItem>
                  <SelectItem value="French Horn">French Horn</SelectItem>
                  <SelectItem value="Guitar">Guitar</SelectItem>
                  <SelectItem value="Harp">Harp</SelectItem>
                  <SelectItem value="Jazz Piano">Jazz Piano</SelectItem>

                  <SelectItem value="Oboe">Oboe</SelectItem>
                  <SelectItem value="Piano - grades 1 to 5">
                    Piano - grades 1 to 5
                  </SelectItem>
                  <SelectItem value="Piano - grades 5 and above">
                    Piano - grades 5 and above
                  </SelectItem>
                  <SelectItem value="Sax">Sax</SelectItem>
                  <SelectItem value="Singing">Singing</SelectItem>
                  <SelectItem value="Trombone">Trombone</SelectItem>
                  <SelectItem value="Trumpet">Trumpet</SelectItem>
                  <SelectItem value="Viola">Viola</SelectItem>
                  <SelectItem value="Violin">Violin</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Category
          </div>
          <div className="mt-1">
            <Select
              onValueChange={UpdateState.Category}
              defaultValue={category}
            >
              <SelectTrigger className="borde mb-2 w-full rounded-sm border-slate-300 bg-white">
                <div className="">{category ? category : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="Regular">Regular</SelectItem>
                  <SelectItem value="Tier 4">Tier 4</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Have Good Cv
          </div>
          <div className="mt-1">
            <Select
              onValueChange={UpdateState.HaveGoodCv}
              defaultValue={haveGoodCv}
            >
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">
                  {haveGoodCv ? haveGoodCv : "All"}
                </div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Have Dbs
          </div>
          <div className="mt-1">
            <Select onValueChange={UpdateState.HaveDbs} defaultValue={haveDbs}>
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">{haveDbs ? haveDbs : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Is Active
          </div>
          <div className="mt-1">
            <Select
              onValueChange={UpdateState.IsActive}
              defaultValue={isActive}
            >
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">{isActive ? isActive : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-4 text-xs font-semibold text-slate-700">
            Assigned
          </div>
          <div className="mt-1">
            <input
              type="number"
              placeholder="Enter number of assigned students"
              value={assigned}
              onChange={(e) => setAssigned(e.target.value)}
              className="mb-3 w-full rounded-sm border border-slate-300 bg-white px-3 py-2 text-xs font-normal text-gray-700 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              min="0"
            />
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <div className="fixed bottom-0 flex  flex-row items-center justify-between space-x-4 py-3">
                <Button
                  type="reset"
                  onClick={Action.Reset}
                  className="border border-black bg-white text-black hover:bg-black hover:text-white "
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  onClick={Action.generateQuery}
                  className="border border-blue-600 bg-blue-600 hover:bg-white hover:text-blue-600"
                >
                  Apply Filter
                </Button>
              </div>
            </SheetClose>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}
