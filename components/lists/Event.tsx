"use client";

import { tEventL } from "types/type";
import React from "react";
import moment from "moment";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import { MdDelete } from "react-icons/md";
import { IoIosSettings } from "react-icons/io";
import Delete from "@components/global/Delete";

type Props = {
  event: tEventL;
  mutate: () => void;
};

export default function Event({ event, mutate }: Props) {
  return (
    <div className="mb-1.5 grid grid-cols-5 items-center gap-x-4 rounded-md border border-gray-200 bg-white px-1  py-3">
      <div className="relative col-span-2 ">
        {/* // DropdownMenu Open For Actions   */}
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute -top-2 left-0">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col p-1">
                <div className="w-40 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`/event/${event.id}`}
                    mutate={mutate}
                    successMsg={`Event '${event.title}' has been successfully removed!`}
                    erroMsg="Failed to delete the event"
                    headerTitle="Delete"
                    icon={<MdDelete size={16} color="gray" />}
                    des="event details"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <p className="ml-5 max-w-[256px] overflow-hidden truncate text-sm font-medium text-gray-600">
                {event.title}
              </p>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm break-words">
              <p>{event.title}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="max-w-[176px] overflow-hidden truncate text-xs font-light text-gray-600">
        {event.location}
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[120px] overflow-hidden truncate text-xs font-normal text-gray-600">
          {moment(event.date)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[120px] overflow-hidden truncate  text-xs font-normal text-gray-600 ">
          {event.visibility ? event.visibility : "---"}
        </div>
      </div>
    </div>
  );
}

export function EventLisHeader() {
  return (
    <div className="grid grid-cols-5 gap-x-4 py-2">
      <div className="col-span-2 text-sm font-medium text-gray-500">Title</div>
      <div className="text-sm font-medium text-gray-500 ">Location</div>
      <div className="text-center text-sm font-medium text-gray-500">Date</div>

      <div className=" text-center text-sm font-medium text-gray-500">
        Country
      </div>
      {/* <div className=" text-center text-sm font-medium text-gray-500">
        Action
      </div> */}
    </div>
  );
}
