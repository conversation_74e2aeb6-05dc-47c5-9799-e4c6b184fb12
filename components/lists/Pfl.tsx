"use client";

import React, { useState } from "react";
import {
  MdAssignmentAdd,
  MdCancelPresentation,
  MdDateRange,
  MdFileDownloadDone,
  MdOutlineCategory,
  MdOutlineContactMail,
  MdOutlineEmail,
  MdOutlineSafetyDivider,
  MdOutlineVideoCall,
  MdPreview,
  MdRepeatOn,
  MdReviews,
  MdWc,
} from "react-icons/md";
import {
  FaBirthdayCake,
  FaChalkboardTeacher,
  FaHome,
  FaRegUser,
  FaUserCheck,
  FaUserSecret,
  FaUserTie,
} from "react-icons/fa";
import { GoDeviceMobile } from "react-icons/go";
import { GoDotFill } from "react-icons/go";

import { TbCheckbox, TbChecks } from "react-icons/tb";
import { tClientEmail, tPflL, tTutorEmail } from "types/pending1stL";
import moment from "moment";
import { BiCommentEdit } from "react-icons/bi";
import { ImPriceTags } from "react-icons/im";
import { GiAwareness, GiCheckMark, GiDuration } from "react-icons/gi";
import { RiDeleteBin6Line, RiMoneyPoundBoxLine } from "react-icons/ri";
import { HiOutlineMail } from "react-icons/hi";
import { TiTick } from "react-icons/ti";
import { ImCross } from "react-icons/im";

import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import api from "fetchers/BaseUrl";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import { Textarea } from "@components/ui/textarea";
import { Input } from "@components/ui/input";
import { Dots } from "react-activity";
import { Button } from "@components/ui/button";
import toast from "react-hot-toast";
import { getSession } from "next-auth/react";
import { BooleanToString, StringToBoolean } from "core/Converters";

import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import { Checkbox } from "@components/ui/checkbox";

import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";
import Image from "next/image";
import { FiAlertCircle } from "react-icons/fi";
import { MdEmail } from "react-icons/md";
import { IoIosSettings, IoMdSwap } from "react-icons/io";
import { FaHouseUser } from "react-icons/fa";
import Selects from "react-select";

import useSWR from "swr";
import { fetcher } from "@fetchers/fetcher";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import Link from "next/link";

import { Calendar } from "components/ui/calendar";

import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { SlCalender } from "react-icons/sl";

type Props = {
  P1stLesson: tPflL;
  mutate: () => void;
};

export default function Pfl({ P1stLesson, mutate }: Props) {
  const [isSelected, setIsSelected] = useState<boolean>(false);

  const Handelar = {
    RightClick: () => {
      setIsSelected(true);
    },
  };

  const {
    data: Student,
    error: StduentError,
    mutate: mutateStu,
    isLoading: isStuLoading,
  } = useSWR<tClientEmail>(
    P1stLesson.id ? `/pfl/${P1stLesson.id}/client-email` : null,
    fetcher,
  );

  const {
    data: Tutor,
    error: TutorError,
    mutate: mutateTutor,
    isLoading: isTutorLoading,
  } = useSWR<tTutorEmail>(
    P1stLesson.id ? `/pfl/${P1stLesson.id}/tutor-email` : null,
    fetcher,
  );

  return (
    <div
      className={`mb-2 grid h-fit grid-cols-8 items-center gap-x-2 border border-gray-200 px-2 py-3 ${
        isSelected
          ? "border-l border-r border-l-gray-600 border-r-gray-600 bg-gray-100"
          : "rounded-md bg-white"
      }`}

      // onContextMenu={Handelar.RightClick}
    >
      <div className="flex h-full flex-col">
        <div className="flex justify-start">
          <div className="flex w-full flex-row  items-center justify-between px-2">
            {/* //Action .... */}
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="ml-[-10px]">
                  <IoIosSettings
                    size={22}
                    className="cursor-pointer text-slate-700"
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuGroup>
                  <div className="flex flex-col px-1 pt-1">
                    <TransferStudent lesson={P1stLesson} mutate={mutate} />
                    <EmailToStudent
                      student={Student}
                      mutate={mutate}
                      mutateStuEmail={mutateStu}
                      isStuLoading={isStuLoading}
                    />
                    <EmailToTutor
                      tutor={Tutor}
                      mutate={mutate}
                      mutateTutorEmail={mutateTutor}
                      isTutorLoading={isTutorLoading}
                    />
                    <MarkDone lesson={P1stLesson} mutate={mutate} />
                    <DeleteFromList lesson={P1stLesson} mutate={mutate} />
                  </div>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex flex-col items-end justify-center">
              <div className="max-w-[90px] overflow-hidden truncate whitespace-nowrap  text-[8px] font-medium text-gray-700">
                {P1stLesson?.admin}
              </div>

              <div className="text-[8px] font-normal text-gray-600">
                {P1stLesson?.added &&
                  moment(P1stLesson.added)
                    .add(new Date().getTimezoneOffset() * -1, "minute")
                    .fromNow(true)}
              </div>
            </div>
          </div>
        </div>

        {/* //Student Info  */}
        <div className="flex h-full flex-col items-center justify-center space-y-[2px]">
          <div className=" flex flex-row items-center">
            <EditStudent lesson={P1stLesson} mutate={mutate} />
            {P1stLesson.country && (
              <Image
                src={P1stLesson.country === "United Kingdom" ? UKFlag : USFlag}
                alt={P1stLesson?.country}
                className=" ml-1 h-4 w-4"
              />
            )}
          </div>

          <div
            className="max-w-[120px] cursor-pointer overflow-hidden truncate whitespace-nowrap pt-[2px] text-xs font-medium text-gray-600 hover:underline"
            onClick={() => {
              window.open(`/students/${P1stLesson?.studentId}`, "_blank");
            }}
          >
            {P1stLesson?.child}
          </div>
          <div className="max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.email}
          </div>
          <div className="max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.address}
          </div>
          <div className="max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.phoneNumber}
          </div>
          <div className="max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.postCode}
          </div>
        </div>
      </div>

      {/* //Finding Tutor... */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex flex-row items-center">
          <EditFindingTutor lesson={P1stLesson} mutate={mutate} />
          {P1stLesson?.isTutorFound ? (
            <GoDotFill
              color="green"
              size={14}
              style={{ marginBottom: "10px", marginLeft: "2px" }}
            />
          ) : (
            <GoDotFill
              color="#d90416"
              size={14}
              style={{ marginBottom: "10px", marginLeft: "2px" }}
            />
          )}
        </div>

        <div className="flex flex-row items-center space-x-1">
          <MdDateRange
            size={14}
            color={P1stLesson?.proposedDate ? "#5a5a5a" : "#d90416"}
            className=" w-1/5"
          />

          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.proposedDate
              ? P1stLesson?.proposedDate
              : "No Proposed Date"}
          </div>
        </div>

        <div className="flex flex-row items-center space-x-1">
          <FaChalkboardTeacher
            size={13}
            color={P1stLesson?.lessonType ? "#5a5a5a" : "#d90416"}
            className=" w-1/5"
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.lessonType
              ? P1stLesson.lessonType + " Lessons"
              : "Unknown Lesson Type"}
          </div>
        </div>
        <div className="mt-[2px] flex flex-row items-center space-x-1">
          <FaUserTie
            size={13}
            color={P1stLesson?.tutors ? "#5a5a5a" : "#d90416"}
            className="mb-[2px] w-1/5"
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.tutors ? P1stLesson.tutors : "Approached Tutors?"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <TbChecks
            size={14}
            color={P1stLesson?.tutor ? "#5a5a5a" : "#d90416"}
            className=" w-1/5"
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.tutor ? P1stLesson.tutor : "Not Assigned"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <BiCommentEdit
            size={14}
            color={P1stLesson?.actionTaken ? "#5a5a5a" : "#d90416"}
            className=" w-1/5"
          />

          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.actionTaken
              ? P1stLesson.actionTaken
              : "No Action Taken"}
          </div>
        </div>
      </div>

      {/* //Con.Date Time  */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex  items-center justify-center ">
          <EditConDateTime lesson={P1stLesson} mutate={mutate} />
        </div>

        <div className="flex flex-col items-center justify-center py-3">
          {P1stLesson?.isMatched ? (
            <TbCheckbox
              size={40}
              color="green"
              style={{ marginBottom: "5px" }}
            />
          ) : (
            <MdCancelPresentation
              size={40}
              color="#d90416"
              style={{ marginBottom: "5px" }}
            />
          )}

          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-medium text-gray-600">
            {P1stLesson?.timeIssue ? P1stLesson.timeIssue : ""}
          </div>
          <div className="max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-medium text-gray-600">
            {P1stLesson?.timePreference ? P1stLesson?.timePreference : ""}
          </div>
          {P1stLesson?.confirmedDate && (
            <div className="max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-medium text-gray-600">
              {moment(P1stLesson.confirmedDate)
                .add(new Date().getTimezoneOffset() * -1, "minute")
                .format("DD MMM YYYY")}
            </div>
          )}
        </div>
      </div>

      {/* //Instrument  */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex  items-center justify-center ">
          <EditInstrument lesson={P1stLesson} mutate={mutate} />
        </div>

        <div className="flex flex-col items-center justify-center py-3">
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-normal text-gray-700">
            {P1stLesson?.instrumentName ? P1stLesson?.instrumentName : ""}
          </div>

          {!P1stLesson?.needInstrument ? (
            <>
              <TbCheckbox
                size={40}
                color="green"
                style={{ marginBottom: "5px" }}
              />

              <div className="text-center text-[10px] font-normal text-gray-700">
                No Instrument Required
              </div>
            </>
          ) : (
            <>
              {P1stLesson?.needInstrument && P1stLesson.isDelivered ? (
                <>
                  <TbCheckbox
                    size={40}
                    color="green"
                    style={{ marginBottom: "5px" }}
                  />
                </>
              ) : (
                <MdCancelPresentation
                  size={40}
                  color="#d90416"
                  style={{ marginBottom: "5px" }}
                />
              )}

              <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-medium text-gray-600">
                {P1stLesson?.aboutInstrument ? P1stLesson.aboutInstrument : ""}
              </div>

              <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-medium text-gray-600">
                {P1stLesson.instrumentPrice &&
                  ` | £${P1stLesson.instrumentPrice}`}
                {` | ${P1stLesson.noOfInstalments}`}
              </div>

              {!P1stLesson.isDelivered ? (
                <div className="text-center text-[10px] font-medium text-gray-600">
                  Not Delivered
                </div>
              ) : (
                P1stLesson.deliveryDate && (
                  <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap text-center text-[10px] font-medium text-gray-600">
                    {moment(P1stLesson.deliveryDate)
                      .add(new Date().getTimezoneOffset() * -1, "minute")
                      .format("DD MMM YYYY")}
                  </div>
                )
              )}

              {P1stLesson?.needInstrument && (
                <div className="text-center text-[10px] font-medium text-gray-600">
                  {P1stLesson.hasInContractSigned
                    ? "Contract Signed"
                    : "Contract Not Signed"}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* //Lesson Prep */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex flex-row items-center">
          <EditLessonPrep lesson={P1stLesson} mutate={mutate} />
        </div>

        <div className="flex flex-row items-center space-x-1">
          <RiMoneyPoundBoxLine
            size={14}
            color={P1stLesson?.havePaid ? "#5a5a5a" : "#d90416"}
          />
          <div className="text-[10px] font-normal text-gray-700">
            {P1stLesson?.havePaid ? "Made The Payment" : "Not Paid Yet"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <FaHome
            size={14}
            color={P1stLesson?.haveAddress ? "#5a5a5a" : "#d90416"}
          />
          <div className="text-[10px] font-normal text-gray-700">
            {P1stLesson?.haveAddress ? "Have Address" : "Doesn’t Have Address"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <FaUserSecret
            size={14}
            color={P1stLesson?.isAdult ? "#5a5a5a" : "#d90416"}
          />
          <div className="text-[10px] font-normal text-gray-700">
            {P1stLesson?.isAdult ? "Adult" : "Non Adult"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <div
            style={{
              width: "16px",
              marginBottom: "-3px",
              marginTop: "2px",
            }}
          >
            <GiAwareness
              size={14}
              color={P1stLesson?.aboutStudent ? "#5a5a5a" : "#d90416"}
            />
          </div>
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.aboutStudent
              ? P1stLesson.aboutStudent
              : "Tutor Not Informed About Student"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <GiDuration
            size={14}
            color={P1stLesson?.proposedDurations ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.proposedDurations
              ? P1stLesson?.proposedDurations
              : "No Proposed Duration"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <ImPriceTags
            size={14}
            color={P1stLesson?.pricesForTutor ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.pricesForTutor
              ? P1stLesson?.pricesForTutor
              : "Regular Price"}
          </div>
        </div>

        {P1stLesson?.lessonType === "Online" && (
          <div className="flex flex-row items-center space-x-1">
            <div className="">
              <MdOutlineVideoCall
                size={17}
                color={P1stLesson?.zoomLink ? "#5a5a5a" : "#d90416"}
              />
            </div>

            {P1stLesson?.zoomLink ? (
              <Link href={P1stLesson.zoomLink} passHref legacyBehavior>
                <a
                  className="max-w-[120px] cursor-pointer overflow-hidden truncate whitespace-nowrap text-[10px] font-medium italic text-blue-600 "
                  target="_blank"
                >
                  {P1stLesson?.zoomLink}
                </a>
              </Link>
            ) : (
              <div className="text-[10px] font-normal text-gray-700">
                No link is provided
              </div>
            )}
          </div>
        )}

        <div className="flex flex-row items-center space-x-1">
          <MdWc size={14} color={P1stLesson?.gender ? "#5a5a5a" : "#d90416"} />

          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson.gender || "Gender?"}
          </div>
        </div>

        <div className="flex flex-row items-center space-x-1">
          <FaBirthdayCake
            size={12}
            color={P1stLesson?.dob ? "#5a5a5a" : "#d90416"}
          />

          <div className="max-w-28 truncate  text-[10px] font-normal text-gray-700">
            {P1stLesson.dob
              ? moment(P1stLesson.dob)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .format("DD MMM YYYY")
              : "Date of Birth?"}
          </div>
        </div>
      </div>

      {/* //TutorInfo */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex flex-row items-center space-x-1">
          <EditTutorInfo lesson={P1stLesson} mutate={mutate} />
        </div>

        <div className="flex flex-row items-center space-x-1">
          <FaRegUser
            size={14}
            color={P1stLesson?.tutorName ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.tutorName ? P1stLesson?.tutorName : "Tutor Name?"}
          </div>
        </div>

        <div className="flex flex-row items-center space-x-1">
          <HiOutlineMail
            size={14}
            color={P1stLesson?.tutorEmail ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.tutorEmail ? P1stLesson?.tutorEmail : "Tutor Email?"}
          </div>
        </div>

        <div className="flex flex-row items-center space-x-1">
          <MdOutlineCategory
            size={15}
            color={P1stLesson?.category ? "#5a5a5a" : "#d90416"}
          />

          <div className="text-[10px] font-normal text-gray-700">
            {P1stLesson?.category === "New"
              ? `New (${P1stLesson.isReferral ? "Referred" : "Not Referred"})`
              : P1stLesson?.category || "New/Old?"}
          </div>
        </div>

        <div className="flex flex-row items-center space-x-1">
          <MdOutlineSafetyDivider
            size={16}
            color={P1stLesson?.tutorType ? "#5a5a5a" : "#d90416"}
          />
          <div className="text-[10px] font-normal text-gray-700">
            {P1stLesson.tutorType === "T4"
              ? `Tier 4 (${P1stLesson.isClCompleted ? "Completed" : "Incomplete"})`
              : P1stLesson.tutorType || "UA/UK/US/Tier 4?"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <MdAssignmentAdd
            size={14}
            color={P1stLesson?.hasSigned ? "#5a5a5a" : "#d90416"}
          />
          <div className="text-[10px] font-normal text-gray-700">
            {P1stLesson?.hasSigned ? "Contract Signed" : "Contract Not Signed"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <FaUserCheck
            size={16}
            color={P1stLesson?.isIntroduced ? "#5a5a5a" : "#d90416"}
          />
          <div className="max-w-[120px] truncate text-[10px] font-normal text-gray-700">
            {P1stLesson?.isIntroduced
              ? "Introduced To MuseCool"
              : "Not Introduced To MuseCool"}
          </div>
        </div>
      </div>

      {/* //Different Tutor  */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex items-center justify-center">
          <EditDiffTutor lesson={P1stLesson} mutate={mutate} />
        </div>
        <div className="flex flex-col items-center justify-center py-3">
          {P1stLesson?.isTutorDifferent ? (
            P1stLesson.hasClientAccepted ? (
              <TbCheckbox
                size={40}
                color="green"
                style={{ marginBottom: "5px" }}
              />
            ) : (
              <MdCancelPresentation
                size={40}
                color="#d90416"
                style={{ marginBottom: "5px" }}
              />
            )
          ) : (
            <TbCheckbox
              size={40}
              color="green"
              style={{ marginBottom: "5px" }}
            />
          )}

          {P1stLesson?.isTutorDifferent && (
            <>
              <div className="text-[10px] font-medium text-gray-600">
                {P1stLesson?.haveGoodCv
                  ? "Have A Good CV"
                  : "Doesn’t Have A Good CV"}
              </div>
              <div className=" text-[10px] font-medium text-gray-600">
                {P1stLesson?.hasClientInformed
                  ? "Client Informed"
                  : "Client Not Informed"}
              </div>
              <div className="text-[10px] font-medium text-gray-600">
                {P1stLesson?.hasClientAccepted
                  ? "Client Accepted"
                  : "Client Not Accepted"}
              </div>
            </>
          )}
        </div>
      </div>

      {/* //Final */}
      <div className="h-full space-y-1 border-l border-l-gray-300 p-2">
        <div className="flex flex-row items-center">
          <div
            className={`cursor-pointer pb-3 text-center text-xs font-medium ${
              P1stLesson?.isConfirmed &&
              !(P1stLesson?.isClientOk && P1stLesson.isTutorOk)
                ? "text-red-700"
                : "text-gray-700"
            } hover:underline`}
          >
            <EditFinal lesson={P1stLesson} mutate={mutate} />
          </div>
          {P1stLesson?.isConfirmed ? (
            <>
              <GoDotFill
                color="green"
                size={14}
                style={{ marginBottom: "10px", marginLeft: "2px" }}
              />
              {P1stLesson?.isClientOk && P1stLesson.isTutorOk ? (
                <TiTick
                  size={16}
                  color="green"
                  style={{ marginBottom: "10px", marginLeft: "2px" }}
                />
              ) : (
                <ImCross
                  size={10}
                  color="#d90416"
                  style={{ marginBottom: "10px", marginLeft: "2px" }}
                />
              )}
            </>
          ) : (
            <GoDotFill
              color="#d90416"
              size={14}
              style={{ marginBottom: "10px", marginLeft: "2px" }}
            />
          )}
        </div>
        <div className="flex flex-row items-center space-x-1">
          <MdOutlineContactMail
            size={14}
            color={P1stLesson?.mailedToStudent ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.mailedToStudent
              ? "Emailed To Student"
              : "Not Emailed To Student"}
          </div>
        </div>

        <div className="flex flex-row items-center space-x-1">
          <MdOutlineEmail
            size={14}
            color={P1stLesson?.mailedToTutor ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.mailedToTutor
              ? "Emailed To Tutor"
              : "Not Emailed To Tutor"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <MdPreview
            size={14}
            color={P1stLesson?.studentOpinion ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            Opinion(S):{" "}
            {P1stLesson?.studentOpinion
              ? P1stLesson?.studentOpinion
              : "Unknown"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <MdReviews
            size={14}
            color={P1stLesson?.tutorOpinion ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            Opinion(T):{" "}
            {P1stLesson?.tutorOpinion ? P1stLesson?.tutorOpinion : "Unknown"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <GoDeviceMobile
            size={14}
            color={P1stLesson?.confirmedInApp ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.confirmedInApp
              ? "Confirmed In App"
              : "Not Confirmed In App"}
          </div>
        </div>
        <div className="flex flex-row items-center space-x-1">
          <MdRepeatOn
            size={14}
            color={P1stLesson?.scheduledSec ? "#5a5a5a" : "#d90416"}
          />
          <div className=" max-w-[120px] overflow-hidden truncate whitespace-nowrap  text-[10px] font-normal text-gray-700">
            {P1stLesson?.scheduledSec
              ? "Scheduled (2nd)"
              : "Not Scheduled (2nd)"}
          </div>
        </div>
      </div>
    </div>
  );
}

type tActionProps = {
  lesson: tPflL;
  mutate: () => void;
};

function EditStudent({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    fullname: yup.string().required("Full name is required!"),
    phoneNumber: yup.string().required("Phone number is required!"),
    postCode: yup.string().required("Postcode is required!"),
    address: yup.string().required("Address is required!"),
  });

  const defaultValues = {
    fullname: lesson.student,
    phoneNumber: lesson.phoneNumber,
    postCode: lesson.postCode,
    address: lesson.address,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    console.log("Data ...: ", data);
    console.log("Clicked....");

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/pfl/${lesson?.id}/student`,
        { ...data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Student information has been successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Faild to edit student information!");
    }
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="max-w-[120px] cursor-pointer overflow-hidden truncate whitespace-nowrap text-sm font-semibold text-gray-800 hover:underline">
          {lesson.student}
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>General Information</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="fullname"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter full name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter phone number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="postCode"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>PostCode</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter postcode"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="Enter address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditFindingTutor({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    // proposedDate: yup.number().required("Proposed date is required!"),
    // lessonType: yup.number().required("Lesson type is required!"),
    // tutors: yup.number().required("Tutors is required!"),
  });

  const defaultValues = {
    proposedDate: lesson.proposedDate,
    lessonType: lesson.lessonType,
    tutors: lesson.tutors,
    tutor: lesson.tutor,
    actionTaken: lesson.actionTaken,
    isTutorFound: BooleanToString(lesson.isTutorFound),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    const { isTutorFound } = data;

    try {
      const session = await getSession();
      const token = session?.token;
      const response = await api.put(
        `/pfl/${lesson.id}/finding-tutor`,
        {
          ...data,
          isTutorFound: StringToBoolean(isTutorFound),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success("Finding tutor information updated successfully!");
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);

      toast.error("Failed to update the information for finding a tutor!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer pb-3 text-center text-xs font-medium text-gray-700 hover:underline ">
          Finding Tutor
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Finding Tutor</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="proposedDate"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Proposed Date And Time</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter proposed date and time"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lessonType"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>LessonType</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("lessonType")
                            ? form.getValues("lessonType")
                            : "Please select an option"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={null as any}>
                        Please select an option
                      </SelectItem>
                      <SelectItem value="In-Person">In-Person</SelectItem>
                      <SelectItem value="Online">Online</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tutors"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Sortlisted Tutors </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter sortlisted tutors"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="tutor"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Selected Tutor </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter selected tutor"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="actionTaken"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Action Taken</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Type your comment here."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isTutorFound"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Is Tutor Found?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("isTutorFound") === "true"
                            ? "Yes"
                            : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}
            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditConDateTime({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [isConfirmedDate, setIsConfirmedDate] = useState<boolean>(
    lesson.confirmedDate ? true : false,
  );

  const UpdateIsConDate = (value: boolean) => {
    setIsConfirmedDate(value);
  };

  var resolver = yup.object().shape({
    // timePreference: yup.string().test({
    //   name: "pattern",
    //   test: (value: any) => /^(0?[1-9]|1[0-2]):[0-5][0-9] [AP]M$/.test(value),
    //   message: "Wrong time format. Follow this HH:MM AM/PM",
    // }),

    timePreference: yup
      .string()
      .matches(
        /^(0?[1-9]|1[0-2]):[0-5][0-9] [AP]M$/, // check the time format
        {
          message: "Wrong time format. Follow this HH:MM AM/PM", // show the error message
          excludeEmptyString: true, // skip validation for empty strings
        },
      )
      .notRequired(), // make the field not required
    // .nullable(true), // allow null values
  });

  const defaultValues = {
    timeIssue: lesson.timeIssue,
    timePreference: lesson.timePreference,
    isMatched: BooleanToString(lesson.isMatched),
    confirmedDate: lesson.confirmedDate
      ? new Date(lesson.confirmedDate)
      : new Date(),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    UpdateIsConDate(lesson.confirmedDate ? true : false);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    const { isMatched, confirmedDate } = data;
    try {
      const session = await getSession();
      const token = session?.token;
      const response = await api.put(
        `/pfl/${lesson.id}/confirm-date`,
        {
          ...data,
          isMatched: StringToBoolean(isMatched),
          confirmedDate:
            confirmedDate && isConfirmedDate
              ? moment(confirmedDate).format("YYYY-MM-DD")
              : null,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Confirm Date information updated successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update the confirm date information!");
    }
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer pb-3 text-xs font-medium text-gray-700 hover:underline">
          Con. Date Time
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Con. Date Time</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="timeIssue"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>About Time Issue</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter time issue"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="mt-2 flex items-center space-x-1.5">
              <Checkbox
                checked={isConfirmedDate}
                onCheckedChange={() => UpdateIsConDate(!isConfirmedDate)}
              />
              <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Confirmed Date
              </label>
            </div>

            {isConfirmedDate && (
              <FormField
                control={form.control}
                name="confirmedDate"
                render={({ field }) => (
                  <FormItem className="mb-[-4px] py-2">
                    {/* <FormLabel>Confirmed Date</FormLabel> */}
                    <FormControl>
                      <ReactDatePicker
                        dateFormat="yyyy-MM-dd"
                        selected={field.value}
                        onChange={field.onChange}
                        className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="timePreference"
              render={({ field }) => (
                <FormItem className="mt-1 py-2">
                  <FormLabel>Confirmed Time</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter confirmed time"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isMatched"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Is Matched?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("isMatched") === "true"
                            ? "Yes"
                            : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}
            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => {
                  form.reset(defaultValues);
                  UpdateIsConDate(lesson.confirmedDate ? true : false);
                }}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditInstrument({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isDelivered, setIsDelivered] = useState<boolean>(
    lesson.isDelivered ? true : false,
  );

  const UpdateIsDelivered = (value: boolean) => {
    setIsDelivered(value);
  };

  var resolver = yup.object().shape({
    // instrumentPrice: yup
    //   .number()
    //   .required("Instrument price is required!")
    //   .positive("Instrument price must be greater than 0")
    //   .typeError("Instrument price is required!"),
    // noOfInstalments: yup
    //   .number()
    //   .required("Number of installments is required!")
    //   .positive("Number of installments must be greater than 0")
    //   .integer("Number of installments must be an integer")
    //   .typeError("Number of installments is required!"),
  });

  const defaultValues = {
    instrumentName: lesson.instrumentName || "",
    aboutInstrument: lesson.aboutInstrument || "",
    needInstrument: BooleanToString(lesson.needInstrument) || "",

    deliveryDate: lesson.deliveryDate
      ? new Date(lesson.deliveryDate)
      : new Date(),

    instrumentPrice: lesson.instrumentPrice || 0,
    noOfInstalments: lesson.noOfInstalments || 0,
    hasInContractSigned: BooleanToString(lesson.hasInContractSigned) || "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    // console.log("Clicked...");

    const {
      needInstrument,
      instrumentPrice,
      noOfInstalments,
      deliveryDate,
      hasInContractSigned,
    } = data;

    try {
      const session = await getSession();
      const token = session?.token;

      const updatedData = {
        ...data,
        needInstrument: StringToBoolean(needInstrument),
        isDelivered: isDelivered,
        instrumentPrice: instrumentPrice || 0,
        noOfInstalments: noOfInstalments || 0,
        deliveryDate:
          isDelivered && deliveryDate
            ? moment(deliveryDate).format("YYYY-MM-DD")
            : null,

        hasInContractSigned: StringToBoolean(hasInContractSigned),
      };

      const response = await api.put(
        `/pfl/${lesson?.id}/instrument`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      console.log("Response", response);

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Intrument information edited successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error)
      toast.error("Failed to edit the instrument information!");
    }
  };

  // console.log(
  //   "Submit ",
  //   form.getValues("needInstrument"),
  //   typeof form.getValues("needInstrument"),
  // );

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer pb-3 text-xs font-medium text-gray-700 hover:underline">
          Instrument
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Instrument</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="instrumentName"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Instrument Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter instrument name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="needInstrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>
                    Need{" "}
                    {lesson.instrumentName
                      ? lesson.instrumentName
                      : "Instrument"}
                    ?
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        {form.getValues("needInstrument") === "true"
                          ? "Yes"
                          : "No"}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.getValues("needInstrument") === "true" && (
              <>
                <FormField
                  control={form.control}
                  name="aboutInstrument"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>Note About Instrument</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter note about instrument"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hasInContractSigned"
                  render={({ field }) => (
                    <FormItem className=" py-2">
                      <FormLabel>Has Signed Instrument Contract?</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div>
                              {form.getValues("hasInContractSigned") === "true"
                                ? "Yes"
                                : "No"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instrumentPrice"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>Instrument Price (Total Amount)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter instrument price"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="noOfInstalments"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel> Number Of Installments</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter no of instalments"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="mt-2 flex items-center space-x-1.5">
                  <Checkbox
                    checked={isDelivered}
                    onCheckedChange={() => UpdateIsDelivered(!isDelivered)}
                  />
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Is Delivered
                  </label>
                </div>
                {isDelivered && (
                  <FormField
                    control={form.control}
                    name="deliveryDate"
                    render={({ field }) => (
                      <FormItem className="mb-[-4px] py-2">
                        <FormControl>
                          <ReactDatePicker
                            dateFormat="yyyy-MM-dd"
                            selected={field.value}
                            onChange={field.onChange}
                            className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                          />
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </>
            )}

            {/* Submission Button .... */}
            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => {
                  form.reset(defaultValues);
                  UpdateIsDelivered(lesson.deliveryDate ? true : false);
                }}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>

              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditLessonPrep({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [birthDate, setBirthDate] = useState<Date | null>(
    lesson.dob ? new Date(lesson.dob) : null,
  );

  var resolver = yup.object().shape({});
  const defaultValues = {
    havePaid: BooleanToString(lesson.havePaid) || "false",
    haveAddress: BooleanToString(lesson.haveAddress) || "false",
    isAdult: BooleanToString(lesson.isAdult) || "false",
    aboutStudent: lesson.aboutStudent || "",
    proposedDurations: lesson.proposedDurations || "",
    pricesForTutor: lesson.pricesForTutor || "",
    zoomLink: lesson.zoomLink || "",
    meetingId: lesson.meetingId || "",
    passcode: lesson.passcode || "",

    gender: lesson.gender || null,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    onReset();
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onReset = () => {
    form.reset(defaultValues);
    setBirthDate(lesson.dob ? new Date(lesson.dob) : null);
  };

  // Handler to properly convert the Calendar's selection
  const handleDateSelect = (date: Date | undefined) => {
    setBirthDate(date || null); // Convert undefined to null
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    const { havePaid, haveAddress, isAdult } = data;

    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(
        `/pfl/${lesson.id}/lesson-prep`,
        {
          ...data,
          havePaid: StringToBoolean(havePaid),
          haveAddress: StringToBoolean(haveAddress),
          isAdult: StringToBoolean(isAdult),
          dob: birthDate ? moment(birthDate).format("YYYY-MM-DD") : null,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Lesson preperation information edited successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error);
      toast.error("Failed to edit the information for lesson preperation!");
    }
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer pb-3 text-center text-xs font-medium text-gray-700 hover:underline">
          Lesson Prep
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[460px]">
        <DialogHeader>
          <DialogTitle>Lesson Prepetration</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="havePaid"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Made Payment?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="">
                          {form.getValues("havePaid") === "true"
                            ? "Yes, Made The Payment"
                            : "Not Paid Yet"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">
                        Yes, Made The Payment
                      </SelectItem>
                      <SelectItem value="false">Not Paid Yet</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className=" flex w-full flex-row items-center space-x-2">
              <FormField
                control={form.control}
                name="haveAddress"
                render={({ field }) => (
                  <FormItem className=" w-1/2 py-2">
                    <FormLabel>Have Full Address?</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className=" capitalize">
                            {form.getValues("haveAddress") === "true"
                              ? "Yes"
                              : "No"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="isAdult"
                render={({ field }) => (
                  <FormItem className=" w-1/2 py-2">
                    <FormLabel>Is Adult Learner?</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className=" capitalize">
                            {form.getValues("isAdult") === "true"
                              ? "Yes"
                              : "No"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex w-full flex-row items-center space-x-2.5 py-2">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Gender</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("gender")
                            ? form.getValues("gender")
                            : "Select gender"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="w-1/2">
                <FormLabel>Birthdate</FormLabel>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="birthDate"
                      variant={"outline"}
                      className="mt-1 flex w-full items-center justify-start bg-white px-2.5 font-normal"
                    >
                      <SlCalender size={16} className="mr-2" />

                      {birthDate ? (
                        moment(birthDate).format("MMM D, YYYY")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="single"
                      defaultMonth={birthDate ?? new Date()}
                      selected={birthDate ?? undefined} // Convert null to undefined
                      onSelect={handleDateSelect} // Use the handler instead of setBirthDate directly
                      numberOfMonths={1}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <FormField
              control={form.control}
              name="proposedDurations"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Proposed Durations</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter proposed durations"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="pricesForTutor"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Tutor Fees</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter tutor fees"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {lesson.lessonType === "Online" && (
              <>
                <FormField
                  control={form.control}
                  name="zoomLink"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>Zoom Link</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter zoom link"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className=" flex w-full flex-row items-center space-x-2">
                  <FormField
                    control={form.control}
                    name="meetingId"
                    render={({ field }) => (
                      <FormItem className="w-1/2 py-2">
                        <FormLabel>Meeting Id</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="Enter meeting id"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="passcode"
                    render={({ field }) => (
                      <FormItem className="w-1/2 py-2">
                        <FormLabel>Passcode</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="Enter passcode"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </>
            )}

            <FormField
              control={form.control}
              name="aboutStudent"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Information About Student (For Tutor)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Comment here about student"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}
            <div className="mt-5 flex w-full flex-row items-center justify-between">
              <div>
                <Button
                  type="reset"
                  onClick={onReset}
                  className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
                >
                  Reset
                </Button>
              </div>

              <div className="flex w-full flex-row justify-end space-x-2.5">
                <Button
                  type="reset"
                  onClick={() => setBirthDate(null)}
                  className="border border-red-600 bg-white text-red-600 hover:bg-red-600 hover:text-white "
                >
                  Clear Birthdate
                </Button>

                <Button
                  type="submit"
                  onClick={form.handleSubmit(onSubmit)}
                  className="border border-cyan-600 bg-cyan-600 hover:bg-white hover:text-cyan-600"
                >
                  {isLoading ? <Dots /> : "Submit"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditTutorInfo({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    tutorEmail: yup
      .string()
      .matches(
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, //
        {
          message:
            "Invalid email address. Please check if there is any extra space added.", // show the error message
          excludeEmptyString: true, // skip validation for empty strings
        },
      )
      .notRequired(), // make the field not required
    // .nullable(true), // allow null values
  });
  const defaultValues = {
    tutorName: lesson.tutorName,
    tutorEmail: lesson.tutorEmail,
    category: lesson.category,
    tutorType: lesson.tutorType,
    hasSigned: BooleanToString(lesson.hasSigned),
    isIntroduced: BooleanToString(lesson.isIntroduced),

    isReferral: BooleanToString(lesson.isReferral),
    isClCompleted: BooleanToString(lesson.isClCompleted),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    const { hasSigned, isIntroduced, isReferral, isClCompleted } = data;
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(
        `/pfl/${lesson?.id}/tutor-info`,
        {
          ...data,
          hasSigned: StringToBoolean(hasSigned),
          isIntroduced: StringToBoolean(isIntroduced),
          isReferral: StringToBoolean(isReferral),
          isClCompleted: StringToBoolean(isClCompleted),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("tutor information edited successfully!");
      } else toast.error("Unexpected response from the server!");
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error)
      toast.error("Failed to edit the tutor information!");
    }
  };

  const watchCategory = form.watch("category");
  const watchTutorType = form.watch("tutorType");

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer pb-3 text-center text-xs font-medium text-gray-700 hover:underline">
          Tutor Info
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Tutor Information</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="tutorName"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Tutor's First Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter tutor name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tutorEmail"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Tutor's Email</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter tutor email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex w-full flex-row items-center space-x-2">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem className=" w-1/2 py-2">
                    <FormLabel>Tutor's Category </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className=" capitalize">
                            {form.getValues("category")
                              ? form.getValues("category")
                              : "Select an option"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Old">Old</SelectItem>
                        <SelectItem value="New">New</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="tutorType"
                render={({ field }) => (
                  <FormItem className="w-1/2 py-2">
                    <FormLabel>Tutor's Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className=" capitalize">
                            {form.getValues("tutorType")
                              ? form.getValues("tutorType")
                              : "Select an option"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="UA">UA</SelectItem>
                        <SelectItem value="UK">UK</SelectItem>
                        <SelectItem value="US">US</SelectItem>
                        <SelectItem value="T4">T4</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {watchCategory === "New" && (
              <FormField
                control={form.control}
                name="isReferral"
                render={({ field }) => (
                  <FormItem className=" py-2">
                    <FormLabel>Is Referred?</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("isReferral") === "true"
                            ? "Yes"
                            : "No"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {watchTutorType === "T4" && (
              <FormField
                control={form.control}
                name="isClCompleted"
                render={({ field }) => (
                  <FormItem className="py-2">
                    <FormLabel>Is Checklist Completed?</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("isClCompleted") === "true"
                            ? "Yes"
                            : "No"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="hasSigned"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Contract Signed?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("hasSigned") === "true"
                            ? "Yes"
                            : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isIntroduced"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Is Introduced To MuseCool?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("isIntroduced") === "true"
                            ? "Yes"
                            : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}
            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditDiffTutor({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({});
  const defaultValues = {
    isTutorDifferent: BooleanToString(lesson.isTutorDifferent),
    haveGoodCv: BooleanToString(lesson.haveGoodCv),
    hasClientInformed: BooleanToString(lesson.hasClientInformed),
    hasClientAccepted: BooleanToString(lesson.hasClientAccepted),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    const {
      isTutorDifferent,
      haveGoodCv,
      hasClientInformed,
      hasClientAccepted,
    } = data;
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(
        `/pfl/${lesson.id}/different-tutor`,
        {
          ...data,
          isTutorDifferent: StringToBoolean(isTutorDifferent),
          haveGoodCv: StringToBoolean(haveGoodCv),
          hasClientInformed: StringToBoolean(hasClientInformed),
          hasClientAccepted: StringToBoolean(hasClientAccepted),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Different tutor information edited successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error)
      toast.error("Failed to edit the information for different tutor!");
    }
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer pb-3 text-center text-xs font-medium text-gray-700 hover:underline">
          Different Tutor
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Different Tutor</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="isTutorDifferent"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Different Tutor?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("isTutorDifferent") === "true"
                            ? "Yes"
                            : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.getValues("isTutorDifferent") === "true" && (
              <>
                <FormField
                  control={form.control}
                  name="haveGoodCv"
                  render={({ field }) => (
                    <FormItem className=" py-2">
                      <FormLabel>Have Good CV?</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className=" capitalize">
                              {form.getValues("haveGoodCv") === "true"
                                ? "Yes"
                                : "No"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="hasClientInformed"
                  render={({ field }) => (
                    <FormItem className=" py-2">
                      <FormLabel>Is The Client Informed?</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className=" capitalize">
                              {form.getValues("hasClientInformed") === "true"
                                ? "Informed"
                                : "Not Informed Yet"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="true">Informed</SelectItem>
                          <SelectItem value="false">
                            Not Informed Yet
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="hasClientAccepted"
                  render={({ field }) => (
                    <FormItem className=" py-2">
                      <FormLabel>Is The Client Accepted?</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className=" capitalize">
                              {form.getValues("hasClientAccepted") === "true"
                                ? "Accepted"
                                : "Not Accepted"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="true">Accepted</SelectItem>
                          <SelectItem value="false">Not Accepted</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {/* Submission Button .... */}
            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function EditFinal({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({});

  const defaultValues = {
    isConfirmed: BooleanToString(lesson.isConfirmed),
    mailedToStudent: BooleanToString(lesson.mailedToStudent),
    mailedToTutor: BooleanToString(lesson.mailedToTutor),
    studentOpinion: lesson.studentOpinion,
    tutorOpinion: lesson.tutorOpinion,
    confirmedInApp: BooleanToString(lesson.confirmedInApp),
    scheduledSec: BooleanToString(lesson.scheduledSec),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    const {
      isConfirmed,
      mailedToStudent,
      mailedToTutor,
      confirmedInApp,
      scheduledSec,
    } = data;
    try {
      const session = await getSession();
      const token = session?.token;
      const response = await api.put(
        `/pfl/${lesson?.id}/final`,
        {
          ...data,
          isConfirmed: StringToBoolean(isConfirmed),
          mailedToStudent: StringToBoolean(mailedToStudent),
          mailedToTutor: StringToBoolean(mailedToTutor),
          confirmedInApp: StringToBoolean(confirmedInApp),
          scheduledSec: StringToBoolean(scheduledSec),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Final information edited successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error);
      toast.error("Failed to edit the Final information !");
    }
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div>Final</div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Final</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="isConfirmed"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Is Ready?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("isConfirmed") === "true"
                            ? "Yes, Ready To Email"
                            : "Not Ready To Email"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes, Ready To Email</SelectItem>
                      <SelectItem value="false">Not Ready To Email</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className=" flex w-full flex-row items-start space-x-2">
              <FormField
                control={form.control}
                name="mailedToStudent"
                render={({ field }) => (
                  <FormItem className=" w-1/2 py-2">
                    <FormLabel>Emailed To Student</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className=" capitalize">
                            {form.getValues("mailedToStudent") === "true"
                              ? "Yes"
                              : "No"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="mailedToTutor"
                render={({ field }) => (
                  <FormItem className=" w-1/2 py-2">
                    <FormLabel>Emailed To Tutor</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className="">
                            {form.getValues("mailedToTutor") === "true"
                              ? "Yes"
                              : "No"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="confirmedInApp"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>Lesson Confirmed In App?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("confirmedInApp") === "true"
                            ? "Yes"
                            : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Yes</SelectItem>
                      <SelectItem value="false">No</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="scheduledSec"
              render={({ field }) => (
                <FormItem className=" py-2">
                  <FormLabel>2nd Lesson</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("scheduledSec") === "true"
                            ? "Scheduled"
                            : "Not Scheduled Yet"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="true">Scheduled</SelectItem>
                      <SelectItem value="false">Not Scheduled Yet</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="studentOpinion"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Student's Opinion </FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter student opinion" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="tutorOpinion"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Tutor's Opinion</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter tutor opinion" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}
            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

type tAdmins = {
  id: string;
  fullname: string;
  students: number;
  pending: number;
};

type tStuProps = {
  student: tClientEmail | undefined;
  mutate: () => void;
  mutateStuEmail: () => void;
  isStuLoading: boolean;
};
type tTutorProps = {
  tutor: tTutorEmail | undefined;
  mutate: () => void;
  mutateTutorEmail: () => void;
  isTutorLoading: boolean;
};

function TransferStudent({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);
  const [admin, setAdmin] = useState<any>();

  const {
    data: adminList,
    isLoading: adminLoading,
    mutate: mutateAdmins,
  } = useSWR<Array<tAdmins>>("/pfl/admin-list", fetcher);

  const reset = (value: any) => {
    setIsOpen(value);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const Action = {
    Cancel: () => {
      setAdmin("");
      setIsOpen(false);
    },

    Submit: async (data: any) => {
      setIsLoading(true);
      const Session = await getSession();
      const token = Session?.token;

      // console.log (admin, " :Admin");

      try {
        const response = await api.put(
          `/pfl/transfer`,
          {
            id: lesson.id,
            adminId: admin.value,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        );

        if (response.status === 200) {
          mutate();
          mutateAdmins();
          setIsLoading(false);
          setAdmin("");
          setIsOpen(false);
          toast.success("Transfer student successfully!");
        } else toast.error("Unexpected response from the server!");
      } catch (error: any) {
        setIsLoading(false);
        // console.log(error)
        toast.error("Faild to transfer student!");
      }
    },
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <IoMdSwap size={14} className=" text-slate-600" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Transfer Student
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Transfer Student</DialogTitle>
          <DialogDescription className="py-3 text-center">
            {lesson.admin}
          </DialogDescription>
        </DialogHeader>
        <div className="hide-scrollbar relative  w-full">
          <Selects
            id="asset"
            instanceId="asset"
            maxMenuHeight={200}
            options={adminList?.map((admin: tAdmins) => {
              return {
                value: admin.id,
                label: `${admin.fullname} [${admin.students}] - ${admin.pending}`,
              };
            })}
            placeholder="Select An Admin"
            value={admin}
            onChange={(e: any) => setAdmin(e)}
            className="mb-6 border-slate-300 text-xs font-normal text-gray-700 "
          />
        </div>
        {/* <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="adminId"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Instruments</FormLabel>
                  <div className="hide-scrollbar relative  w-full">
                    <Selects
                      id="asset"
                      instanceId="asset"
                      maxMenuHeight={200}
                      options={adminList?.map((admin: tAdmins) => {
                        return {
                          value: admin.id,
                          label: `${admin.fullname} [${admin.students}] - ${admin.pending}`,
                        };
                      })}
                      placeholder="Select An Admin"
                      value={admin}
                      onChange={(e: any) => setAdmin(e)}
                      className="mb-6 border-slate-300 text-xs font-normal text-gray-700 "
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form> */}

        {/* Submission Button .... */}
        <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
          <Button
            type="reset"
            onClick={Action.Cancel}
            className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
          >
            Reset
          </Button>
          <Button
            type="submit"
            onClick={Action.Submit}
            className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
          >
            {isLoading ? <Dots /> : "Submit"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function EmailToStudent({
  student,
  mutate,
  isStuLoading,
  mutateStuEmail,
}: tStuProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    fullName: yup.string().required("Full name is required!"),
    childName: yup.string().required("Child name name is required!"),
    lessonDate: yup.string().required("Lesson date is required!"),
    lessonTime: yup.string().required("Lesson time is required!"),
    lessonType: yup.string().required("Lesson type is required!"),
    instrument: yup.string().required("Instrument name is required!"),
    isAdult: yup.string().required("Adult field is required!"),
    // zoomlink: yup.string().required("Zoom link is required!"),

    // zoomLink: yup.string().when("lessonType", {
    //   is: (value: any) => value === "Online",
    //   then: yup.string().required("Zoom link is required!"),
    //   otherwise: yup.string().notRequired(),
    // }),
  });

  const defaultValues = {
    fullName: student?.fullName,
    isAdult: BooleanToString(student?.isAdult),
    childName: student?.childName,
    instrument: student?.instrument,
    lessonDate: student?.lessonDate
      ? moment(student?.lessonDate).format("DD MMMM YYYY")
      : "",

    lessonTime: student?.lessonTime,
    lessonType: student?.lessonType,
    zoomLink: student?.zoomLink,
    meetingId: student?.meetingId,
    passcode: student?.passcode,
  };

  const form = useForm<any>({
    defaultValues,
    resolver: yupResolver(resolver),
  });

  const onSubmit = async (data: any) => {
    const { isAdult } = data;
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.post(
        `/pfl/${student?.id}/client-email`,
        {
          ...data,
          isAdult: StringToBoolean(isAdult),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        mutateStuEmail();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Email send successfully!");
      } else toast.error("Unexpected response from the server!");
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error)
      toast.error("Error Found!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const watchLessonType = form.watch("lessonType");

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <FaHouseUser size={14} className=" text-slate-600" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Email Student
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Send Student Email</DialogTitle>
        </DialogHeader>
        {isStuLoading ? (
          <ListSkeltons height={45} count={10} />
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter full name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="childName"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Child Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter child name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="lessonDate"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Date</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter lesson date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lessonTime"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Time</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter lesson time"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="lessonType"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Type </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className="capitalize">
                              {form.getValues("lessonType")
                                ? form.getValues("lessonType")
                                : "Please select an option"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={null as any}>
                            Please select an option
                          </SelectItem>
                          <SelectItem value="In-Person">In-Person</SelectItem>
                          <SelectItem value="Online">Online</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instrument"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Instrument Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter instrument name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {watchLessonType === "Online" && (
                <>
                  <FormField
                    control={form.control}
                    name="zoomLink"
                    render={({ field }) => (
                      <FormItem className="py-2">
                        <FormLabel>Zoom Link</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="Enter zoom link"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className=" flex flex-row items-center space-x-2">
                    <FormField
                      control={form.control}
                      name="meetingId"
                      render={({ field }) => (
                        <FormItem className="w-1/2 py-2">
                          <FormLabel>Meeting Id</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter meeting id"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="passcode"
                      render={({ field }) => (
                        <FormItem className="w-1/2 py-2">
                          <FormLabel>Passcode</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter passcode"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </>
              )}

              <FormField
                control={form.control}
                name="isAdult"
                render={({ field }) => (
                  <FormItem className=" py-2">
                    <FormLabel>Adult</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className=" capitalize">
                            {form.getValues("isAdult")
                              ? form.getValues("isAdult") === "true"
                                ? "Yes"
                                : "No"
                              : "Please select an option"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={null as any}>
                          Please select an option
                        </SelectItem>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Submission Button .... */}
              <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
                <Button
                  type="reset"
                  onClick={() => form.reset(defaultValues)}
                  className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  onClick={form.handleSubmit(onSubmit)}
                  className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                >
                  {isLoading ? <Dots /> : "Submit"}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}

function EmailToTutor({
  tutor,
  mutate,
  isTutorLoading,
  mutateTutorEmail,
}: tTutorProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    tutorName: yup.string().required("Tutor name is required!"),
    tutorEmail: yup.string().required("Tutor Email is required!"),
    category: yup.string().required("Category is required!"),
    fullName: yup.string().required("Student name is required!"),
    email: yup.string().required("Student email is required!"),
    phoneNumber: yup.string().required("Phone number is required!"),
    address: yup.string().required("Address is required!"),
    childName: yup.string().required("Child name name is required!"),
    childAge: yup.string().required("Child age name is required!"),
    lessonDate: yup.string().required("Lesson date is required!"),
    lessonTime: yup.string().required("Lesson time is required!"),
    lessonType: yup.string().required("Lesson type is required!"),
    lessonDuration: yup.string().required("Lesson duration is required!"),
    aboutChild: yup.string().required("This field is required!"),
    isAdult: yup.string().required("Adult field is required!"),
  });

  const defaultValues = {
    fullName: tutor?.fullName,
    email: tutor?.email,
    phoneNumber: tutor?.phoneNumber,
    address: tutor?.address,
    childName: tutor?.childName,
    childAge: tutor?.childAge,
    category: tutor?.category,
    lessonDate: tutor?.lessonDate
      ? moment(tutor?.lessonDate).format("DD MMMM YYYY")
      : "",
    lessonTime: tutor?.lessonTime,
    lessonType: tutor?.lessonType,
    lessonDuration: tutor?.lessonDuration,
    aboutChild: tutor?.aboutChild,

    tutorName: tutor?.tutorName,
    tutorEmail: tutor?.tutorEmail,
    isAdult: BooleanToString(tutor?.isAdult),
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    const { isAdult } = data;
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.post(
        `/pfl/${tutor?.id}/tutor-email`,
        {
          ...data,
          isAdult: StringToBoolean(isAdult),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        mutateTutorEmail();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Email send successfully!");
      } else toast.error("Unexpected response from the server!");
    } catch (error: any) {
      setIsLoading(false);
      // console.log(error)
      toast.error("Eroor Found!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <MdEmail size={14} className=" text-slate-600" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Email Tutor
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Send Tutor Email</DialogTitle>
        </DialogHeader>

        {isTutorLoading ? (
          <ListSkeltons height={45} count={10} />
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="tutorName"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Tutor Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter tutor name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tutorEmail"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Tutor Email</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter tutor email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex w-full flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem className=" w-1/2 py-2">
                      <FormLabel>Category </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className=" capitalize">
                              {form.getValues("category")
                                ? form.getValues("category")
                                : "Select an option"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={null as any}>
                            Please select an option
                          </SelectItem>
                          <SelectItem value="Old">Old</SelectItem>
                          <SelectItem value="New">New</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="isAdult"
                  render={({ field }) => (
                    <FormItem className=" w-1/2 py-2">
                      <FormLabel>Adult</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className=" capitalize">
                              {form.getValues("isAdult")
                                ? form.getValues("isAdult") === "true"
                                  ? "Yes"
                                  : "No"
                                : "Please select an option"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={null as any}>
                            Please select an option
                          </SelectItem>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Student Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter student name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Student Email</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter student email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter phone number"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter address"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="childName"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Child Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter child name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="childAge"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Child Age</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter child age"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="lessonDate"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Date</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter lesson date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lessonTime"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Time</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter lesson time"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" flex flex-row items-center space-x-2">
                <FormField
                  control={form.control}
                  name="lessonType"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Type </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <div className="capitalize">
                              {form.getValues("lessonType")
                                ? form.getValues("lessonType")
                                : "Please select an option"}
                            </div>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={null as any}>
                            Please select an option
                          </SelectItem>
                          <SelectItem value="In-Person">In-Person</SelectItem>
                          <SelectItem value="Online">Online</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lessonDuration"
                  render={({ field }) => (
                    <FormItem className="w-1/2 py-2">
                      <FormLabel>Lesson Duration</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter lesson duration"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="aboutChild"
                render={({ field }) => (
                  <FormItem className="py-2">
                    <FormLabel>Written About Child</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Type your comment here."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Submission Button .... */}
              <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
                <Button
                  type="reset"
                  onClick={() => form.reset(defaultValues)}
                  className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  onClick={form.handleSubmit(onSubmit)}
                  className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                >
                  {isLoading ? <Dots /> : "Submit"}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}

function MarkDone({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [isOpen, setIsOpen] = useState(false);
  const reset = (value: any) => {
    setIsOpen(value);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onDelte = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      let response = await api.post(
        // `/pfl/${lesson.id}/mark-as-done`,
        `/pfl/${lesson.id}/mark-as-done`,
        {},
        {
          headers: {
            Authorization: "Bearer " + token,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success(`${lesson.student}'s lesson marked as done!`);
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (e: any) {
      setIsLoading(false);
      toast.error("Failed to mark the lesson as done!");
    }
  };

  return (
    <AlertDialog onOpenChange={reset} open={isOpen}>
      <AlertDialogTrigger onClick={onOpen} asChild>
        <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <MdFileDownloadDone size={14} className=" text-slate-600" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Mark As Done
          </span>
        </div>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <div className=" flex flex-row items-center">
              <GiCheckMark size={19} color="green" className="mr-2" />
              Mark As Done?
            </div>
          </AlertDialogTitle>
          <AlertDialogDescription>
            Please take a moment to confirm your action. This process cannot be
            undone. Review all the details carefully before proceeding.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelte}
            className="border bg-blue-700 hover:border-blue-700 hover:bg-white hover:text-blue-700"
          >
            {isLoading ? <Dots /> : "Confirm"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function DeleteFromList({ lesson, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDelete = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      let response = await api.delete(`/pfl/${lesson?.id}`, {
        headers: {
          Authorization: "Bearer " + token,
        },
      });
      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success(
          `${lesson?.student}'s lesson has been deleted successfully! `,
        );
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (e: any) {
      setIsLoading(false);
      toast.error("Faild to Remove Lesson");
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <RiDeleteBin6Line size={15} className=" text-slate-600" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Remove From List
          </span>
        </div>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <div className=" flex flex-row items-center">
              <FiAlertCircle size={19} color="red" className="mr-2" />
              Are you sure to remove this PFL from the list?
            </div>
          </AlertDialogTitle>
          <AlertDialogDescription>
            This action is irreversible and will permanently delete the details
            of {lesson?.student} from PFL.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
