"use client";

import React, { useEffect, useState } from "react";
import { tStudentL } from "../../types/student";
// import styles from "styles/StudentList.module.css";
import Avatar from "react-avatar";
import moment from "moment";
import { Fc<PERSON><PERSON><PERSON>ndroid } from "react-icons/fc";
import { RiNotificationOffLine } from "react-icons/ri";
import { TiUserDelete } from "react-icons/ti";

import { GiAchievement } from "react-icons/gi";
import Link from "next/link";
import { Button } from "components/ui/button";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "components/ui/sheet";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import { Input } from "@components/ui/input";
import Selects from "react-select";
import { GenerateQuery } from "core/Query";
import { Textarea } from "@components/ui/textarea";
import { IoIosSettings } from "react-icons/io";
import UpdateEmail from "@components/global/UpdateEmail";
import Delete from "@components/global/Delete";
import UpdateStatus from "@components/global/UpdateStatus";
import { TbListDetails } from "react-icons/tb";
import { MdDelete } from "react-icons/md";

import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";

import { DateRange } from "react-day-picker";

import { Calendar } from "components/ui/calendar";
import { SlCalender } from "react-icons/sl";
import { FaUserCheck } from "react-icons/fa";

import Image from "next/image";
import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";
import ChildImage from "assets/Child.png";
import AdultImage from "assets/Adult.png";

type Props = {
  student: tStudentL;
  mutate: () => void;
};

export default function Student({ student, mutate }: Props) {
  const [isSelected, setIsSelected] = useState<boolean>(false);

  // const router = useRouter();

  function roundUp(num: number, precision: number) {
    precision = Math.pow(10, precision);
    return Math.ceil(num * precision) / precision;
  }

  // const countryFlags: any = {
  //   "United Kingdom": UKFlag,
  //   "United States": USFlag,
  // };

  const GetStatusColor = (status: string) => {
    switch (status) {
      case "Regular":
        return "bg-lime-600 border-lime-600 text-lime-600";
      case "Gave up":
        return "bg-red-600 border-red-600 text-red-600";
      case "Looking for tutor":
        return "bg-blue-600 border-blue-600 text-blue-600";
      case "Green":
        return "bg-green-600 border-green-600 text-green-600";
      case "New":
        return "bg-amber-600 border-amber-600 text-amber-600";
      case "Pending 1st lesson":
        return "bg-violet-600 border-violet-600 text-violet-600";
      case "Multiple Children":
        return "bg-pink-600 border-pink-600";
      case "Call much later":
        return "bg-cyan-600 border-cyan-600 text-cyan-600";
      default:
        return "bg-teal-600 border-teal-600 text-teal-600";
    }
  };

  const giveUpReason = student?.gupReasons?.split(",");
  const childList = student?.children?.split(",");

  return (
    <div
      // className={isSelected ? styles.SInfoContainer : styles.infoContainer}
      className={`mb-1.5 grid grid-cols-9 items-center gap-x-4 rounded-md border border-gray-200 py-2.5${
        isSelected
          ? "rounded-none border-x-2 border-gray-200 border-x-gray-900 bg-gray-100 "
          : " bg-white"
      }`}
    >
      <div className="relative col-span-2">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className=" absolute -top-1 left-1">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-1.5 p-1">
                <Link
                  href={`/students/${student.id}`}
                  target="_blank"
                  className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <TbListDetails size={16} color="gray" />
                  <span className="ml-1 text-xs font-medium text-slate-600">
                    View
                  </span>
                </Link>
                <UpdateEmail
                  email={student.email}
                  operationName="Student Email"
                  mutate={mutate}
                />
                <div className="w-44 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <UpdateStatus
                    student={student}
                    mutate={mutate}
                    icon
                    title="Update Status"
                  />
                </div>
                <div className="w-44 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`admin/rmuser?id=${student.id}`}
                    mutate={mutate}
                    successMsg={`${student.fullname} has been successfully deleted!`}
                    erroMsg="Faild to delete student!"
                    headerTitle="Delete"
                    icon={<MdDelete size={16} color="gray" />}
                    des="student details"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-5 flex flex-row items-center ">
          <Avatar name={student.fullname} round={true} size="45" />
          <div className="ml-1.5 space-y-0.5">
            <div className="flex flex-row items-center">
              {student?.country && (
                <Image
                  src={student.country === "United Kingdom" ? UKFlag : USFlag}
                  alt={student?.country}
                  className="mr-1 h-4 w-4"
                />
              )}

              <Link
                href={`/students/${student.id}`}
                target="_blank"
                className="mr-1 max-w-36 overflow-hidden truncate text-sm font-semibold text-gray-800 hover:cursor-pointer hover:underline"
              >
                {student.fullname}
              </Link>

              {student.isChild && (
                <Image
                  // src={student.isChild === true ? ChildImage : AdultImage}
                  src={ChildImage}
                  alt={student.isChild ? "Child" : "Adult"}
                  width={22} // required for next/image static imports
                  height={22}
                  className="mx-[1px]"
                />
              )}

              {student.isUsingApp && <FcPhoneAndroid size={13} />}

              {/* <Image
              src={countryFlags[student?.country]}
              alt={student?.country}
              className="ml-[2px] h-4 w-4"
            /> */}

              {student.hasGoal && <GiAchievement size={15} />}

              {student.enquiryStatus === "Good" && (
                <FaUserCheck size={16} color="#008000" className="mx-0.5" />
              )}
            </div>

            <div className="max-w-[180px] overflow-hidden truncate text-[11px] font-light text-gray-500">
              {student.tutor ? student.tutor : "Not assigned"}
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="max-w-[180px] overflow-hidden truncate text-[10px] font-extralight text-gray-500">
                    {student.children ? student.children : "No child found!"}
                  </div>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm break-words">
                  {childList?.map((child, index) => (
                    <div className="flex flex-row items-start" key={index}>
                      <TiUserDelete size={12} />

                      <div className="ml-[2px]  mt-[-1px] w-44 text-[10px] font-light text-gray-100">
                        {child ? child : "No child"}
                      </div>
                    </div>
                  ))}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-0.5">
        <div className="flex flex-row items-center">
          <div className="max-w-[180px] overflow-hidden truncate text-xs font-medium text-gray-800">
            {student.email}
          </div>
          {student.isUnsubscribed && (
            <div>
              <RiNotificationOffLine
                size={12}
                style={{ marginLeft: "5px", color: "red" }}
              />
            </div>
          )}
        </div>
        <div className="max-w-[180px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {student.phone}
        </div>
        <div className="flex w-fit items-center justify-center  rounded-md bg-gray-200 px-2 py-0.5 ">
          <div className="max-w-[175px] overflow-hidden truncate text-[8px] font-extralight text-gray-500">
            {student.postCode ?? "...."}
          </div>
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[120px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {student.instrument}
        </div>
        <div className="max-w-[120px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {student.age}
        </div>
        <div className="flex flex-row items-center">
          <div className="max-w-[120px] overflow-hidden truncate text-[10px] font-extralight text-gray-500">
            {student.lessons}
          </div>
          <div className="max-w-[120px] overflow-hidden truncate text-[10px] font-extralight text-gray-500">
            , {student.courses}
          </div>
        </div>
      </div>

      {/* <div className="space-y-0.5">
        <div
          style={{
            background: GetStatusColor(student.status) + "12",
            width: "fit-content",
            borderColor: GetStatusColor(student.status),
            borderWidth: "1px",
            borderRadius: "3px",
            borderStyle: "solid",
            padding: "2px 10px",
            marginBottom: "5px",
          }}
        >
          <div
            style={{
              color: GetStatusColor(student.status),
              fontSize: "11px",
              fontWeight: "500",
            }}
          >
            {student.status}
          </div>
        </div>

        
      </div> */}

      <div className="space-y-1">
        <div
          className={`w-fit rounded border ${GetStatusColor(
            student.status,
          )} bg-opacity-10 px-2 py-0.5 text-xs font-medium`}
        >
          {student.status}
        </div>

        {student.gupReasons && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[120px] overflow-hidden truncate text-[11px] font-light text-gray-500">
                  {student.gupReasons}
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm break-words">
                {giveUpReason?.map((reason, index) => (
                  <div
                    className="text-[10px] font-light text-gray-100"
                    key={index}
                  >
                    {" "}
                    - {reason}
                  </div>
                ))}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {student.status === "Get to restart" && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="max-w-[120px] overflow-hidden truncate text-[11px] font-light text-gray-500">
                  {student.note}
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm break-words">
                <div className="text-[10px] font-light text-gray-100">
                  {student.note}
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[120px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {moment(student?.addedOn).format("DD MMM YYYY")}
        </div>
        <div className="max-w-[120px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {moment(student.lastModified).format("DD MMM YYYY")}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[120px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {student.upcoming ? moment(student?.upcoming).fromNow() : "Not found"}
        </div>
        <div className="max-w-[120px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {student.lastLesson
            ? moment(student.lastLesson).fromNow()
            : "Not found"}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[120px] overflow-hidden truncate text-sm font-normal text-gray-800">
          {/* {balance ? `${roundUp(balance.balance, 2)}` : "..."} */}
          {student.balance
            ? `${student.currency}${roundUp(student.balance, 2)}`
            : roundUp(student.balance, 2) === 0
              ? `${student.currency}0`
              : "..."}
        </div>
      </div>
    </div>
  );
}

type headerProps = {
  updateQuery?: any;
  name?: string;
  setName?: any;
  updatePage?: any;
  pageReset: any;
};

type tSelector = {
  value: any;
  label: string;
};

export function StudentListHeader({
  updateQuery,
  name,
  setName,
  updatePage,
  pageReset,
}: headerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isPageActive, setIsPageActive] = useState<boolean>(false);

  const [status, setStatus] = useState<Array<tSelector>>();

  const [postCode, setPostCode] = useState<string>("");
  const [instrument, setInstrument] = useState<string>("");
  const [tutor, setTutor] = useState<string>("");
  const [child, setChild] = useState<string>("");

  const [minLesson, setMinLesson] = useState<number | null>(null);
  const [maxLesson, setMaxLesson] = useState<number | null>(null);
  const [lastLesson, setLastLesson] = useState<number | null>(null);
  const [upcomingLesson, setUpcomingLesson] = useState<number | null>(null);

  const [minCourse, setMinCourse] = useState<number | null>(null);
  const [maxCourse, setMaxCourse] = useState<number | null>(null);

  const [minBalance, setMinBalance] = useState<number | null>(null);
  const [maxBalance, setMaxBalance] = useState<number | null>(null);

  const [instrumentSent, setInstrumentSent] = useState<string>("");
  const [isUsingApp, setIsUsingApp] = useState<string>("");
  const [isUnsubscribed, setIsUnsubscribed] = useState<string>("");
  const [comment, setComment] = useState<string>("");
  const [country, setCountry] = useState<string>("");
  const [isChild, setIsChild] = useState<string>("");
  const [referral, setReferral] = useState<string>("");
  const [haveReferral, setHaveReferral] = useState<string>("");
  const [hasGoal, setHasGoal] = useState<string>("");

  const [enquiryStatus, setEnquiryStatus] = useState<string>("");

  const [addedOn, setAddedOn] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  const [lastModified, setLastModified] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
    },
    onOpen: () => {
      setIsOpen(true);
    },
  };

  const Update = {
    PostCode: (e: any) => {
      setPostCode(e.target.value);
    },

    Referral: (e: any) => {
      setReferral(e.target.value);
    },

    Instrument: (e: any) => {
      setInstrument(e.target.value);
    },
    Tutor: (e: any) => {
      setTutor(e.target.value);
    },
    Child: (e: any) => {
      setChild(e.target.value);
    },

    Country: (e: any) => {
      setCountry(e);
    },
    EnquiryStatus: (e: any) => {
      setEnquiryStatus(e);
    },

    IsChild: (e: any) => {
      setIsChild(e);
    },

    HaveReferral: (e: any) => {
      setHaveReferral(e);
    },

    HasGoal: (e: any) => {
      setHasGoal(e);
    },

    MinLesson: (e: any) => {
      setMinLesson(e.target.value);
    },
    MaxLesson: (e: any) => {
      setMaxLesson(e.target.value);
    },

    MinBalance: (e: any) => {
      setMinBalance(e.target.value);
    },
    MaxBalance: (e: any) => {
      setMaxBalance(e.target.value);
    },

    MinCourse: (e: any) => {
      setMinCourse(e.target.value);
    },
    MaxCourse: (e: any) => {
      setMaxCourse(e.target.value);
    },

    LastLesson: (e: any) => {
      setLastLesson(e.target.value);
    },

    UpcomingLesson: (e: any) => {
      setUpcomingLesson(e.target.value);
    },

    InstrumentSent: (e: any) => {
      setInstrumentSent(e);
    },

    UsingApp: (e: any) => {
      setIsUsingApp(e);
    },
    Unsubscribed: (e: any) => {
      setIsUnsubscribed(e);
    },

    Comment: (e: any) => {
      setComment(e.target.value);
    },
  };

  const Action = {
    generateQuery: () => {
      var newStat = status?.map((i) => i.value).toString();

      if (name && name.length > 2) {
        updateQuery(
          GenerateQuery({
            status: newStat,
            minLesson,
            maxLesson,
            postCode,
            instrument,
            tutor,
            child,
            name,
            referral,
            country,
            enquiryStatus,
            isChild,
            haveReferral,
            hasGoal,
            minBalance,
            maxBalance,
            minCourse,
            maxCourse,
            instrumentSent,
            isUsingApp,
            isUnsubscribed,
            lastLesson,
            upcomingLesson,
            comment,
            startAddedOn: addedOn?.from
              ? moment(addedOn?.from).format("YYYY-MM-DD")
              : null,
            endAddedOn: addedOn?.to
              ? moment(addedOn?.to).format("YYYY-MM-DD")
              : null,

            startLastModified: lastModified?.from
              ? moment(lastModified?.from).format("YYYY-MM-DD")
              : null,
            endLastModified: lastModified?.to
              ? moment(lastModified?.to).format("YYYY-MM-DD")
              : null,
          }),
        );
      } else {
        updateQuery(
          GenerateQuery({
            status: newStat,
            minLesson,
            maxLesson,
            postCode,
            instrument,
            tutor,
            child,
            referral,
            country,
            enquiryStatus,
            isChild,
            haveReferral,
            hasGoal,
            minBalance,
            maxBalance,
            minCourse,
            maxCourse,
            instrumentSent,
            isUsingApp,
            isUnsubscribed,
            lastLesson,
            upcomingLesson,
            comment,

            startAddedOn: addedOn?.from
              ? moment(addedOn?.from).format("YYYY-MM-DD")
              : null,
            endAddedOn: addedOn?.to
              ? moment(addedOn?.to).format("YYYY-MM-DD")
              : null,

            startLastModified: lastModified?.from
              ? moment(lastModified?.from).format("YYYY-MM-DD")
              : null,
            endLastModified: lastModified?.to
              ? moment(lastModified?.to).format("YYYY-MM-DD")
              : null,
          }),
        );
      }
    },

    Reset: () => {
      setStatus([]);

      setMinBalance(null);
      setMaxBalance(null);

      setMinLesson(null);
      setMaxLesson(null);

      setLastLesson(null);
      setUpcomingLesson(null);

      setMinCourse(null);
      setMaxCourse(null);

      setAddedOn({
        from: null,
        to: null,
      });

      setLastModified({
        from: null,
        to: null,
      });

      setIsUsingApp("");
      setInstrumentSent("");
      setIsUnsubscribed("");

      setPostCode("");
      setInstrument("");
      setTutor("");
      setChild("");
      setComment("");
      setCountry("");
      setEnquiryStatus("");
      setIsChild("");
      setReferral("");
      setHaveReferral("");
      setHasGoal("");

      // setIsPageActive(false);
      updateQuery("");
      pageReset("");
      setName("");
    },
  };

  const Status: Array<tSelector> = [
    { value: "Regular", label: "Regular" },
    { value: "New", label: "New" },
    { value: "Looking for tutor", label: "Looking for tutor" },
    { value: "Green", label: "Green" },
    { value: "Pending 1st lesson", label: "Pending 1st lesson" },
    { value: "Group course only", label: "Group course only" },
    { value: "Call much later", label: "Call much later" },
    { value: "Call later", label: "Call later" },
    { value: "Not active", label: "Not active" },
    { value: "Old new", label: "Old new" },
    { value: "Old green", label: "Old green" },
    { value: "Gave up", label: "Gave up" },
    { value: "Get to restart", label: "Get to restart" },
    { value: "Bus event", label: "Bus event" },
    { value: "Multiple Children", label: "Multiple Children" },
  ];

  useEffect(() => {
    if (isPageActive) {
      Action.generateQuery();
    }
  }, [name]);

  useEffect(() => {
    setIsPageActive(false);

    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("p")) {
      updatePage(Number(urlParams.get("p")));
    }
    urlParams.delete("p");

    if (urlParams.toString() !== "") {
      //set status
      const status = urlParams
        .get("status")
        ?.split(",")
        .map((x) => {
          return { value: x, label: x };
        });
      setStatus(status || []);

      //set min lesson
      setMinLesson(Number(urlParams.get("minLesson")));
      //set max lesson
      setMaxLesson(Number(urlParams.get("maxLesson")));
      //set last lesson
      setLastLesson(Number(urlParams.get("lastLesson")));
      //set upcoming lesson
      setUpcomingLesson(Number(urlParams.get("upcomingLesson")));
      //set min course
      setMinCourse(Number(urlParams.get("minCourse")));
      //set max course
      setMaxCourse(Number(urlParams.get("maxCourse")));
      //set min balance
      setMinBalance(Number(urlParams.get("minBalance")));
      //set max balance
      setMaxBalance(Number(urlParams.get("maxBalance")));
      //set post code
      setPostCode(urlParams.get("postCode") || "");
      //set instrument
      setInstrument(urlParams.get("instrument") || "");
      //set tutor
      setTutor(urlParams.get("tutor") || "");
      //set child
      setChild(urlParams.get("child") || "");
      //set comment
      setComment(urlParams.get("comment") || "");

      setReferral(urlParams.get("referral") || "");

      setCountry(urlParams.get("country") || "");

      setEnquiryStatus(urlParams.get("enquiryStatus") || "");

      setIsChild(urlParams.get("isChild") || "");

      setHaveReferral(urlParams.get("haveReferral") || "");

      setHasGoal(urlParams.get("hasGoal") || "");

      //set instrument sent selector
      setInstrumentSent(urlParams.get("instrumentSent") || "");
      //set is unsubscribed selector
      setIsUnsubscribed(urlParams.get("isUnsubscribed") || "");
      //set is using app selector
      setIsUsingApp(urlParams.get("isUsingApp") || "");

      setAddedOn({
        from: new Date(urlParams.get("startAddedOn")!) || null,

        to: new Date(urlParams.get("endAddedOn")!) || null,
      });

      setLastModified({
        from: new Date(urlParams.get("startLastModified")!) || null,

        to: new Date(urlParams.get("endLastModified")!) || null,
      });

      if (name) setName(urlParams.get("name") || "");
      if (updateQuery) updateQuery("&" + urlParams.toString());
    }

    setIsPageActive(true);
  }, []);

  return (
    <Sheet onOpenChange={ModalControl.reset} open={isOpen}>
      <SheetTrigger asChild onClick={ModalControl.onOpen}>
        <div className="grid grid-cols-9 items-center gap-x-4 py-1.5">
          <div className="col-span-2 space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Name
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Tutor
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Child
            </div>
          </div>

          <div className="col-span-2 space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-gray-500">
              Email
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Phone
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Postcode
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-gray-500">
              Instrument
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Age
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Lessons, Courses
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-gray-500">
              Status
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Gaveup Reason
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-gray-500">
              Added On
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Last Modified
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-gray-500">
              Upcoming Lessons
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Last Lessons
            </div>
          </div>

          <div className="cursor-pointer text-center text-sm font-medium text-gray-500">
            Balance
          </div>
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter Student Information</SheetTitle>
          {/* <SheetDescription>
              Make changes to your profile here. Click save when you're done.
            </SheetDescription> */}
        </SheetHeader>
        <div className="hide-scrollbar h-full w-full overflow-y-scroll px-1 pb-24 pt-4">
          <div className="text-xs font-semibold text-slate-700">
            Filter Status
          </div>
          <div className="mt-1">
            <Selects
              isMulti
              id="multistatus"
              name="Multi Status"
              options={Status}
              menuPosition="fixed"
              isSearchable
              placeholder="Select Status"
              value={status}
              onChange={(e: any) => setStatus(e)}
              className="rounded-md border-slate-200 text-xs font-normal text-gray-700 "
            />
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Postcode
          </div>
          <Input
            type="text"
            placeholder="Enter Postcode"
            value={postCode}
            onChange={Update.PostCode}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Referral
          </div>
          <Input
            type="text"
            placeholder="Enter Referral"
            value={referral}
            onChange={Update.Referral}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Instrument
          </div>
          <Input
            type="text"
            placeholder="Enter Instrument Name"
            value={instrument}
            onChange={Update.Instrument}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Tutor
          </div>
          <Input
            type="text"
            placeholder="Enter Tutor Name"
            value={tutor}
            onChange={Update.Tutor}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Child
          </div>
          <Input
            type="text"
            placeholder="Enter Child Name"
            value={child}
            onChange={Update.Child}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Country
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.Country} defaultValue={country}>
              <SelectTrigger className="w-full bg-white">
                {/* <SelectValue placeholder="Select a type" /> */}
                <div className="">{country ? country : "Both"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>Both</SelectItem>
                  <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                  <SelectItem value="United States">United States</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Is Enquiry ? (Good / Normal)
          </div>
          <div className="mt-1">
            <Select
              onValueChange={Update.EnquiryStatus}
              defaultValue={enquiryStatus}
            >
              <SelectTrigger className="w-full bg-white">
                {/* <SelectValue placeholder="Select a type" /> */}
                <div className="">{enquiryStatus ? enquiryStatus : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="Good">Good</SelectItem>
                  <SelectItem value="Normal">Normal</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Is Child?
          </div>

          <div className="mt-1">
            <Select onValueChange={Update.IsChild} defaultValue={isChild}>
              <SelectTrigger className="w-full  bg-white">
                {isChild ? isChild : "All"}
              </SelectTrigger>

              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Have Referral
          </div>
          <div className="mt-1">
            <Select
              onValueChange={Update.HaveReferral}
              defaultValue={haveReferral}
            >
              <SelectTrigger className="w-full  bg-white">
                <div className=" capitalize">
                  {haveReferral ? haveReferral : "All"}
                </div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Has Goal
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.HasGoal} defaultValue={hasGoal}>
              <SelectTrigger className="w-full bg-white">
                <div className=" capitalize">{hasGoal ? hasGoal : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {/* //Lesson  */}
          <div className=" flex w-full items-center space-x-2">
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Min Lesson
              </div>
              <Input
                type="number"
                // placeholder="Min Lesson"
                value={minLesson ?? ""}
                onChange={Update.MinLesson}
              />
            </div>
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Max Lesson
              </div>
              <Input
                type="number"
                // placeholder="Max Lesson"
                value={maxLesson ?? ""}
                onChange={Update.MaxLesson}
              />
            </div>
          </div>

          {/* //Balance */}
          <div className=" flex w-full items-center space-x-2">
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Min Balance
              </div>
              <Input
                type="number"
                // placeholder="Min Balance"
                value={minBalance ?? ""}
                onChange={Update.MinBalance}
              />
            </div>
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Max Balance
              </div>
              <Input
                type="number"
                // placeholder="Max Balance"
                value={maxBalance ?? ""}
                onChange={Update.MaxBalance}
              />
            </div>
          </div>

          {/* Course  */}
          <div className=" flex w-full items-center space-x-2">
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Min Course
              </div>
              <Input
                type="number"
                // placeholder="Min Course "
                value={minCourse ?? ""}
                onChange={Update.MinCourse}
              />
            </div>
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Max Course
              </div>
              <Input
                type="number"
                // placeholder="Max Course"
                value={maxCourse ?? ""}
                onChange={Update.MaxCourse}
              />
            </div>
          </div>

          {/* Last Or Upcoming  */}
          <div className=" flex w-full items-center space-x-2">
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Last Lesson
              </div>
              <Input
                type="number"
                // placeholder="Last Lesson "
                value={lastLesson ?? ""}
                onChange={Update.LastLesson}
              />
            </div>
            <div className=" w-1/2">
              <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
                Upcoming Lesson
              </div>
              <Input
                type="number"
                // placeholder="Upcoming Lesson"
                value={upcomingLesson ?? ""}
                onChange={Update.UpcomingLesson}
              />
            </div>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            InstrumentSent Sent
          </div>
          <div className="mt-1">
            <Select
              onValueChange={Update.InstrumentSent}
              defaultValue={instrumentSent}
            >
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">
                  {instrumentSent ? instrumentSent : "All"}
                </div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Using App
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.UsingApp} defaultValue={isUsingApp}>
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">
                  {isUsingApp ? isUsingApp : "All"}
                </div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Unsubscribed
          </div>
          <div className="mt-1">
            <Select
              onValueChange={Update.Unsubscribed}
              defaultValue={isUnsubscribed}
            >
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">
                  {isUnsubscribed ? isUnsubscribed : "All"}
                </div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">True</SelectItem>
                  <SelectItem value="false">False</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Comment
          </div>
          <Textarea value={comment} onChange={Update.Comment} />

          <div className="pt-4">
            <div className="pb-1 text-xs font-semibold text-slate-700">
              Added on
            </div>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="addedOn"
                  variant={"outline"}
                  className="flex w-full items-center justify-start bg-white px-2.5 font-normal"
                >
                  <SlCalender size={16} className="mr-2" />

                  {addedOn?.from ? (
                    addedOn.to ? (
                      <>
                        {moment(addedOn.from).format("MMM D, YYYY")} -{" "}
                        {moment(addedOn.to).format("MMM D, YYYY")}
                      </>
                    ) : (
                      moment(addedOn.from).format("MMM D, YYYY")
                    )
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={addedOn?.from}
                  selected={addedOn}
                  onSelect={setAddedOn}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="py-4">
            <div className="pb-1 text-xs font-semibold text-slate-700">
              Last modified
            </div>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="lastModified"
                  variant={"outline"}
                  className="flex w-full items-center justify-start bg-white px-2.5 font-normal"
                >
                  <SlCalender size={16} className="mr-2" />

                  {lastModified?.from ? (
                    lastModified.to ? (
                      <>
                        {moment(lastModified.from).format("MMM D, YYYY")} -{" "}
                        {moment(lastModified.to).format("MMM D, YYYY")}
                      </>
                    ) : (
                      moment(lastModified.from).format("MMM D, YYYY")
                    )
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={lastModified?.from}
                  selected={lastModified}
                  onSelect={setLastModified}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <SheetFooter>
          <SheetClose asChild>
            <div className="fixed bottom-1 flex  flex-row items-center justify-between space-x-4 py-3">
              <Button
                type="reset"
                onClick={Action.Reset}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>

              <Button
                type="submit"
                onClick={Action.generateQuery}
                className="border border-blue-600 bg-blue-600 hover:bg-white hover:text-blue-600"
              >
                Apply Filter
              </Button>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}

export function StudentListHeaderWOFilter() {
  return (
    <div className="grid grid-cols-9 gap-x-2 py-2">
      <div className="col-span-2">
        <div className="cursor-pointer text-sm font-medium text-gray-500">
          Name
        </div>
        <div className="cursor-pointer text-xs font-normal text-gray-500">
          Tutor
        </div>
        <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
          Child
        </div>
      </div>

      <div className="col-span-2">
        <div className="cursor-pointer  text-sm font-medium text-gray-500">
          Email
        </div>
        <div className="cursor-pointer text-xs font-normal text-gray-500">
          Phone
        </div>
        <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
          Postcode
        </div>
      </div>

      <div>
        <div className="cursor-pointer  text-sm font-medium text-gray-500">
          Instrument
        </div>
        <div className="cursor-pointer text-xs font-normal text-gray-500">
          Age
        </div>
        <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
          Lessons, Courses
        </div>
      </div>

      <div>
        <div className="cursor-pointer  text-sm font-medium text-gray-500">
          Status
        </div>
        <div className="cursor-pointer text-xs font-normal text-gray-500">
          Gaveup Reason
        </div>
      </div>

      <div>
        <div className="cursor-pointer  text-sm font-medium text-gray-500">
          Added On
        </div>
        <div className="cursor-pointer text-xs font-normal text-gray-500">
          Last Modified
        </div>
      </div>

      <div>
        <div className="cursor-pointer  text-sm font-medium text-gray-500">
          Upcoming Lessons
        </div>
        <div className="cursor-pointer text-xs font-normal text-gray-500">
          Last Lessons
        </div>
      </div>

      <div className="cursor-pointer text-center text-sm font-medium text-gray-500">
        Balance
      </div>
    </div>
  );
}
