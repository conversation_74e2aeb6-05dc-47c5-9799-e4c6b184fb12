"use client";

import { tTemplateL } from "types/template";
import React, { useCallback } from "react";
import Link from "next/link";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import { TbListDetails } from "react-icons/tb";
import { IoIosSettings } from "react-icons/io";
import { MdDelete, MdOutlineContentCopy } from "react-icons/md";
import Delete from "@components/global/Delete";
import toast from "react-hot-toast";

type Props = {
  template: tTemplateL;
  mutate: () => void;
};

export default function Template({ template, mutate }: Props) {
  const copyToClipboard = useCallback(async (id: string) => {
    try {
      await navigator.clipboard.writeText(id);
      toast.success("ID copied to clipboard!");
    } catch (error: any) {
      toast.error("Failed to copy ID to clipboard!");
    }
  }, []);

  return (
    <div className="mb-1.5 grid grid-cols-2 items-center gap-x-4 rounded-md border border-gray-200 bg-white px-2 py-2.5">
      <div className="relative">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute -top-1 left-1">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-1.5 p-1">
                <Link
                  href={`/templates/${template.id}`}
                  target="_blank"
                  className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <TbListDetails size={16} color="gray" />
                  <span className="ml-1 text-xs font-medium text-slate-600">
                    View
                  </span>
                </Link>

                <div
                  onClick={() => copyToClipboard(template.id)}
                  className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <MdOutlineContentCopy size={16} color="gray" />
                  <span className="ml-1 text-xs font-medium text-slate-600">
                    Copy Template ID
                  </span>
                </div>

                <div className="w-44 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`/template/${template.id}`}
                    mutate={mutate}
                    successMsg={`${template.title} has been successfully deleted!`}
                    erroMsg="Failed to delete template!"
                    headerTitle="Delete"
                    icon={<MdDelete size={16} color="gray" />}
                    des="template details"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-6">
          <Link
            href={`/templates/${template.id}`}
            target="_blank"
            className=" max-w-xs overflow-hidden truncate text-sm font-medium text-gray-800 hover:cursor-pointer hover:underline"
          >
            {template.title}
          </Link>
        </div>
      </div>

      <div className="max-w-xs overflow-hidden truncate text-xs font-medium text-gray-500">
        {template.category}
      </div>
    </div>
  );
}

export function TemplateListHeader() {
  return (
    <div className="grid grid-cols-2 items-center gap-x-4 py-2">
      <div className="text-sm font-medium text-gray-500">Title</div>
      <div className="text-sm font-medium text-gray-500">Category</div>
    </div>
  );
}
