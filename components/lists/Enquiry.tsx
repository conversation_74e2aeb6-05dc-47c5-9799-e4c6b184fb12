"use client";

import React, { useEffect, useState } from "react";
import Avatar from "react-avatar";
import moment from "moment";
import { FcPhoneAndroid } from "react-icons/fc";
import { TbListDetails } from "react-icons/tb";
import Link from "next/link";
import { But<PERSON> } from "components/ui/button";
import {
  Sheet,
  SheetClose,
  SheetContent,
  <PERSON>et<PERSON>ooter,
  <PERSON>etHeader,
  SheetTitle,
  SheetTrigger,
} from "components/ui/sheet";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from "components/ui/select";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import { Input } from "@components/ui/input";
import { GenerateQuery } from "core/Query";
import { Textarea } from "@components/ui/textarea";
import { IoIosSettings } from "react-icons/io";
import { MdDelete } from "react-icons/md";
import Delete from "@components/global/Delete";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { DateRange } from "react-day-picker";
import { Calendar } from "components/ui/calendar";
import { SlCalender } from "react-icons/sl";
import { FaCheckCircle } from "react-icons/fa";

import Image from "next/image";
import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";
import { tEnquiryL } from "types/rEnquiry";

type Props = {
  enquiry: tEnquiryL;
  mutate: () => void;
};

export default function Enquiry({ enquiry, mutate }: Props) {
  const GetStepColor = (step: string) => {
    switch (step) {
      case "Step1":
        return "bg-yellow-600 border-yellow-600 text-yellow-600";
      case "Step2":
        return "bg-blue-600 border-blue-600 text-blue-600";
      case "Step3":
        return "bg-purple-600 border-purple-600 text-purple-600";
      case "Step4":
        return "bg-green-600 border-green-600 text-green-600";
      default:
        return "bg-gray-600 border-gray-600 text-gray-600";
    }
  };

  // Calculate total journey time with seconds
  const calculateTotalJourneyTime = () => {
    const start = moment(enquiry.formSubmitStartDate);
    const end = moment(enquiry.formSubmitLastUpdate);
    const duration = moment.duration(end.diff(start));

    const days = duration.days();
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Calculate individual step time
  const calculateStepTime = (
    startDate: string | null,
    endDate: string | null,
  ) => {
    if (!startDate || !endDate) return "N/A";

    const start = moment(startDate);
    const end = moment(endDate);
    const duration = moment.duration(end.diff(start));

    const days = duration.days();
    const hours = duration.hours();
    const minutes = duration.minutes();
    const seconds = duration.seconds();

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Get individual step times
  const getStepTimes = () => {
    return {
      step1: calculateStepTime(
        enquiry.formSubmitStartDate,
        enquiry.formSubmitStep1CompleteDate,
      ),
      step2: calculateStepTime(
        enquiry.formSubmitStep1CompleteDate,
        enquiry.formSubmitStep2CompleteDate,
      ),
      step3: calculateStepTime(
        enquiry.formSubmitStep2CompleteDate,
        enquiry.formSubmitStep3CompleteDate,
      ),
      step4: calculateStepTime(
        enquiry.formSubmitStep3CompleteDate,
        enquiry.formSubmitStep4CompleteDate,
      ),
    };
  };

  const stepTimes = getStepTimes();

  return (
    <div className="mb-1.5 grid grid-cols-8 items-center gap-x-4 rounded-md border border-gray-200 bg-white py-2.5">
      <div className="relative col-span-2">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute left-1 top-0.5">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-2 p-1.5">
                <div className="w-48 cursor-pointer rounded-md border-0 border-input bg-transparent px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`/student/inquiry/${enquiry.id}`}
                    mutate={mutate}
                    successMsg={`Enquiry has been successfully deleted!`}
                    erroMsg="Failed to delete enquiry!"
                    headerTitle="Delete"
                    icon={<MdDelete size={16} color="gray" />}
                    des="enquiry"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-6 flex flex-row items-center">
          <Avatar name={enquiry.name} round={true} size="50" />
          <div className="ml-1.5 space-y-0.5">
            <div className="flex flex-row items-center">
              {enquiry?.country && (
                <Image
                  src={enquiry.country === "United Kingdom" ? UKFlag : USFlag}
                  alt={enquiry?.country}
                  className="mr-1 h-4 w-4"
                />
              )}

              {/* <Link
                href={`/enquiries/${enquiry.id}`}
                target="_blank"
                className="mr-1 max-w-36 overflow-hidden truncate text-sm font-semibold text-gray-800 hover:cursor-pointer hover:underline"
              >
                {enquiry.name}
              </Link> */}
              <div className="mr-1 max-w-36 overflow-hidden truncate text-sm font-semibold text-gray-800">
                {enquiry.name}
              </div>

              {/* {enquiry.isFlexibleFirstLesson && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <FaCheckCircle size={14} className="text-green-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">Flexible First Lesson</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )} */}
            </div>

            <div className="max-w-[180px] overflow-hidden truncate text-[11px] font-light text-gray-500">
              {enquiry.age || "Not specified"}
            </div>

            <div className="max-w-[180px] overflow-hidden truncate text-[10px] font-extralight text-gray-500">
              {enquiry.source || "Unknown source"}
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-0.5">
        <div className="max-w-[180px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {enquiry.email}
        </div>
        <div className="max-w-[180px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {enquiry.number || "-----"}
        </div>
        <div className="flex w-fit items-center justify-center rounded-md bg-gray-200 px-2 py-0.5">
          <div className="max-w-[175px] overflow-hidden truncate text-[8px] font-extralight text-gray-500">
            {enquiry.postCode ?? "...."}
          </div>
        </div>
      </div>

      <div className="space-y-1.5">
        <div
          className={`w-fit rounded-md border ${GetStepColor(
            enquiry.formSubmitStep,
          )} bg-opacity-10 px-2 py-0.5 text-xs font-medium`}
        >
          {enquiry.formSubmitStep}
        </div>

        <div className="max-w-[120px] overflow-hidden truncate text-[10px] font-extralight text-gray-500">
          GA: {enquiry.googleAnalyticsId?.slice(-8) || "N/A"}
        </div>
      </div>

      <div className="space-y-1">
        <div className="ml-1.5 w-fit rounded-md border border-cyan-600 bg-white px-2 py-0.5">
          <div className="text-xs font-semibold text-cyan-600">
            {calculateTotalJourneyTime()}
          </div>
        </div>

        <div className="space-y-0.5 bg-white pl-1.5 text-[10px]">
          <div className="flex items-center">
            <span className="font-medium text-gray-600">S1:</span>
            <span className="ml-1 text-gray-500">{stepTimes.step1}</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium text-gray-600">S2:</span>
            <span className="ml-1 text-gray-500">{stepTimes.step2}</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium text-gray-600">S3:</span>
            <span className="ml-1 text-gray-500">{stepTimes.step3}</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium text-gray-600">S4:</span>
            <span className="ml-1 text-gray-500">{stepTimes.step4}</span>
          </div>
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[120px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {moment(enquiry?.addedOn).format("DD MMM YYYY")}
        </div>
        <div className="max-w-[120px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {enquiry.isFlexibleFirstLesson
            ? "Flexible"
            : moment(enquiry.firstLessonAvailabilityDate).format(
                "DD MMM YYYY, h:mm A",
              )}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[100px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {enquiry.instrument || "Not specified"}
        </div>
        <div className="max-w-[100px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {enquiry.referral || "No referral"}
        </div>
        {enquiry.message ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <div className="max-w-[100px] overflow-hidden truncate text-[10px] font-extralight text-gray-500">
                  {enquiry.message}
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-sm break-words">
                <p className="text-xs">{enquiry.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <div className="text-[10px] font-extralight text-gray-400">
            No message
          </div>
        )}
      </div>
    </div>
  );
}

type headerProps = {
  updateQuery?: any;
  name?: string;
  setName?: any;
  updatePage?: any;
  pageReset: any;
};

export function EnquiryListHeader({
  updateQuery,
  name,
  setName,
  updatePage,
  pageReset,
}: headerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isPageActive, setIsPageActive] = useState<boolean>(false);

  const [postCode, setPostCode] = useState<string>("");
  const [instrument, setInstrument] = useState<string>("");
  const [referral, setReferral] = useState<string>("");
  const [country, setCountry] = useState<string>("");
  const [source, setSource] = useState<string>("");
  const [formStep, setFormStep] = useState<string>("");
  const [isFlexible, setIsFlexible] = useState<string>("");

  const [addedOn, setAddedOn] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  const [lastUpdate, setLastUpdate] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
    },
    onOpen: () => {
      setIsOpen(true);
    },
  };

  const Update = {
    PostCode: (e: any) => {
      setPostCode(e.target.value);
    },
    Referral: (e: any) => {
      setReferral(e.target.value);
    },
    Instrument: (e: any) => {
      setInstrument(e.target.value);
    },
    Source: (e: any) => {
      setSource(e.target.value);
    },
    Country: (e: any) => {
      setCountry(e);
    },
    FormStep: (e: any) => {
      setFormStep(e);
    },
    IsFlexible: (e: any) => {
      setIsFlexible(e);
    },
  };

  const Action = {
    generateQuery: () => {
      if (name && name.length > 2) {
        updateQuery(
          GenerateQuery({
            postCode,
            instrument,
            referral,
            name,
            country,
            source,
            formStep,
            isFlexible,
            startAddedOn: addedOn?.from
              ? moment(addedOn?.from).format("YYYY-MM-DD")
              : null,
            endAddedOn: addedOn?.to
              ? moment(addedOn?.to).format("YYYY-MM-DD")
              : null,
            startLastUpdate: lastUpdate?.from
              ? moment(lastUpdate?.from).format("YYYY-MM-DD")
              : null,
            endLastUpdate: lastUpdate?.to
              ? moment(lastUpdate?.to).format("YYYY-MM-DD")
              : null,
          }),
        );
      } else {
        updateQuery(
          GenerateQuery({
            postCode,
            instrument,
            referral,
            country,
            source,
            formStep,
            isFlexible,
            startAddedOn: addedOn?.from
              ? moment(addedOn?.from).format("YYYY-MM-DD")
              : null,
            endAddedOn: addedOn?.to
              ? moment(addedOn?.to).format("YYYY-MM-DD")
              : null,
            startLastUpdate: lastUpdate?.from
              ? moment(lastUpdate?.from).format("YYYY-MM-DD")
              : null,
            endLastUpdate: lastUpdate?.to
              ? moment(lastUpdate?.to).format("YYYY-MM-DD")
              : null,
          }),
        );
      }
    },

    Reset: () => {
      setPostCode("");
      setInstrument("");
      setReferral("");
      setCountry("");
      setSource("");
      setFormStep("");
      setIsFlexible("");
      setAddedOn({
        from: null,
        to: null,
      });
      setLastUpdate({
        from: null,
        to: null,
      });
      updateQuery("");
      pageReset("");
      setName("");
    },
  };

  useEffect(() => {
    if (isPageActive) {
      Action.generateQuery();
    }
  }, [name]);

  useEffect(() => {
    setIsPageActive(false);

    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("p")) {
      updatePage(Number(urlParams.get("p")));
    }
    urlParams.delete("p");

    if (urlParams.toString() !== "") {
      setPostCode(urlParams.get("postCode") || "");
      setInstrument(urlParams.get("instrument") || "");
      setReferral(urlParams.get("referral") || "");
      setCountry(urlParams.get("country") || "");
      setSource(urlParams.get("source") || "");
      setFormStep(urlParams.get("formStep") || "");
      setIsFlexible(urlParams.get("isFlexible") || "");

      setAddedOn({
        from: urlParams.get("startAddedOn")
          ? new Date(urlParams.get("startAddedOn")!)
          : null,
        to: urlParams.get("endAddedOn")
          ? new Date(urlParams.get("endAddedOn")!)
          : null,
      });

      setLastUpdate({
        from: urlParams.get("startLastUpdate")
          ? new Date(urlParams.get("startLastUpdate")!)
          : null,
        to: urlParams.get("endLastUpdate")
          ? new Date(urlParams.get("endLastUpdate")!)
          : null,
      });

      if (name) setName(urlParams.get("name") || "");
      if (updateQuery) updateQuery("&" + urlParams.toString());
    }

    setIsPageActive(true);
  }, []);

  return (
    <Sheet onOpenChange={ModalControl.reset} open={isOpen}>
      <SheetTrigger asChild onClick={ModalControl.onOpen}>
        <div className="grid grid-cols-8 items-center gap-x-4 py-1.5">
          <div className="col-span-2 space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Name
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Age
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Source
            </div>
          </div>

          <div className="col-span-2 space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Email
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Phone
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-gray-500">
              Postcode
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Form Submit Step
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              GA ID
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Journey Time
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Steps Journey
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Added On
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              First Lesson
            </div>
          </div>

          <div className="cursor-pointer space-y-0.5">
            <div className="text-sm font-medium text-gray-500">Details</div>
            <div className="text-xs font-normal text-gray-500"></div>
          </div>
        </div>
      </SheetTrigger>

      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter</SheetTitle>
        </SheetHeader>
        <div className="flex h-full items-center justify-center">
          <p className="text-lg font-medium text-gray-600">Upcoming</p>
        </div>
        {/* TEMPORARILY COMMENTED OUT FILTER CONTENT */}
        {/* <div className="hide-scrollbar h-full w-full overflow-y-scroll px-1 pb-24 pt-4">
          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Postcode
          </div>
          <Input
            type="text"
            placeholder="Enter Postcode"
            value={postCode}
            onChange={Update.PostCode}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Referral
          </div>
          <Input
            type="text"
            placeholder="Enter Referral"
            value={referral}
            onChange={Update.Referral}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Instrument
          </div>
          <Input
            type="text"
            placeholder="Enter Instrument Name"
            value={instrument}
            onChange={Update.Instrument}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Source
          </div>
          <Input
            type="text"
            placeholder="Enter Source"
            value={source}
            onChange={Update.Source}
          />

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Country
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.Country} defaultValue={country}>
              <SelectTrigger className="w-full bg-white">
                <div className="">{country ? country : "Both"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>Both</SelectItem>
                  <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                  <SelectItem value="United States">United States</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Form Step
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.FormStep} defaultValue={formStep}>
              <SelectTrigger className="w-full bg-white">
                <div className="">{formStep ? formStep : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="Step1">Step 1</SelectItem>
                  <SelectItem value="Step2">Step 2</SelectItem>
                  <SelectItem value="Step3">Step 3</SelectItem>
                  <SelectItem value="Step4">Step 4</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Flexible First Lesson
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.IsFlexible} defaultValue={isFlexible}>
              <SelectTrigger className="w-full bg-white">
                <div className=" capitalize">
                  {isFlexible ? isFlexible : "All"}
                </div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="true">Yes</SelectItem>
                  <SelectItem value="false">No</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="pt-4">
            <div className="pb-1 text-xs font-semibold text-slate-700">
              Added on
            </div>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="addedOn"
                  variant={"outline"}
                  className="flex w-full items-center justify-start bg-white px-2.5 font-normal"
                >
                  <SlCalender size={16} className="mr-2" />

                  {addedOn?.from ? (
                    addedOn.to ? (
                      <>
                        {moment(addedOn.from).format("MMM D, YYYY")} -{" "}
                        {moment(addedOn.to).format("MMM D, YYYY")}
                      </>
                    ) : (
                      moment(addedOn.from).format("MMM D, YYYY")
                    )
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={addedOn?.from}
                  selected={addedOn}
                  onSelect={setAddedOn}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="py-4">
            <div className="pb-1 text-xs font-semibold text-slate-700">
              Last Update
            </div>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="lastUpdate"
                  variant={"outline"}
                  className="flex w-full items-center justify-start bg-white px-2.5 font-normal"
                >
                  <SlCalender size={16} className="mr-2" />

                  {lastUpdate?.from ? (
                    lastUpdate.to ? (
                      <>
                        {moment(lastUpdate.from).format("MMM D, YYYY")} -{" "}
                        {moment(lastUpdate.to).format("MMM D, YYYY")}
                      </>
                    ) : (
                      moment(lastUpdate.from).format("MMM D, YYYY")
                    )
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>

              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={lastUpdate?.from}
                  selected={lastUpdate}
                  onSelect={setLastUpdate}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <SheetFooter>
          <SheetClose asChild>
            <div className="fixed bottom-1 flex flex-row items-center justify-between space-x-4 py-3">
              <Button
                type="reset"
                onClick={Action.Reset}
                className="border border-black bg-white text-black hover:bg-black hover:text-white"
              >
                Reset
              </Button>

              <Button
                type="submit"
                onClick={Action.generateQuery}
                className="border border-blue-600 bg-blue-600 hover:bg-white hover:text-blue-600"
              >
                Apply Filter
              </Button>
            </div>
          </SheetClose>
        </SheetFooter> */}
      </SheetContent>
    </Sheet>
  );
}
