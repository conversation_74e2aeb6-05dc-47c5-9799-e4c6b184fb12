import { tGoa<PERSON><PERSON><PERSON> } from "types/goal";
import React, { useState } from "react";

import { But<PERSON> } from "components/ui/button";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import Avatar from "react-avatar";
import moment from "moment";
// import { useRouter } from "next/router";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import api from "fetchers/BaseUrl";
import { Input } from "@components/ui/input";
import { FiEdit } from "react-icons/fi";
import { Textarea } from "@components/ui/textarea";
import { Dots } from "react-activity";
import { RiDeleteBin6Line } from "react-icons/ri";
import { getSession } from "next-auth/react";
import toast from "react-hot-toast";
import Link from "next/link";

type Props = {
  goal: tGoalList;
  mutate: () => void;
};

export default function Goal({ goal, mutate }: Props) {
  // const router: any = useRouter();
  // const handleOnClick = () => {
  //   router.push(`/tutors/${goal.tutor.id}`);
  // };
  return (
    <div className="mb-2 grid grid-cols-7 items-center gap-x-2 rounded-md border border-gray-200 bg-white py-2">
      <div className="col-span-2 flex flex-row items-center pl-1">
        <Avatar name={goal.tutor.fullname} round={true} size="40" />
        <div className=" ml-1 space-y-1">
          <div className="flex items-center">
            <Link
              href={`/tutors/${goal.tutor.id}`}
              target="_blank"
              className="block max-w-[180px] overflow-hidden truncate text-xs font-semibold text-slate-600 hover:cursor-pointer hover:underline"
            >
              {goal.tutor.fullname}
            </Link>
          </div>

          <div className=" max-w-[180px] overflow-hidden truncate  text-[10px] font-normal text-slate-600 ">
            {goal.child.name}
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-1">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className=" max-w-[180px] overflow-hidden truncate text-xs font-medium text-slate-600">
                {goal.title}
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm break-words">
              <div className="text-xs font-normal text-gray-100">
                {goal.title}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="max-w-[180px] overflow-hidden truncate whitespace-nowrap text-[10px] font-light text-slate-600">
                {goal.description}
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm break-words">
              <div className="max-w-[180px]  text-[10px] font-light  text-gray-100">
                {goal.description}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="space-y-1">
        <div className=" max-w-[128px] overflow-hidden truncate text-xs font-medium text-slate-600">
          {goal.status}
        </div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="max-w-[128px] overflow-hidden truncate text-[10px] font-light  text-slate-600">
                {goal.category}
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm break-words">
              <div className="text-[10px] font-light  text-gray-100">
                {goal.category}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="space-y-1">
        <div className="text-xs font-medium text-slate-600">
          {moment(goal.dueDate)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
        <div className="text-[10px] font-light  text-slate-600">
          {moment(goal.created)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
      </div>

      <div className="flex items-center justify-center space-x-1">
        <EditGoal goal={goal} mutate={mutate} />
        <DeleteGoal goal={goal} mutate={mutate} />
      </div>

      {/* <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <div className="cursor-pointer text-center font-bold text-gray-600">
            ...
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuGroup>
            <div className="cursor-pointer py-1 text-xs font-normal text-gray-600">
              Comming Soon ...
            </div>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu> */}
    </div>
  );
}

export function StuGoalListHeader() {
  return (
    <div className="grid grid-cols-7 gap-x-2 pb-1 pt-2">
      <div className="col-span-2">
        <div className="text-xs font-semibold text-slate-500">Tutor</div>
        <div className="text-[10px] font-normal text-slate-500">Child</div>
      </div>

      <div className="col-span-2">
        <div className="text-xs  font-semibold text-slate-500">Title</div>
        <div className="text-[10px] font-normal text-slate-500">
          Description
        </div>
      </div>

      <div className="">
        <div className="text-xs  font-semibold text-slate-500">Status</div>
        <div className="text-[10px] font-normal text-slate-500">Category</div>
      </div>

      <div>
        <div className="text-xs  font-semibold text-slate-500">Due Date</div>
        <div className="text-[10px] font-normal text-slate-500">Created</div>
      </div>

      <div className="flex  justify-center text-xs  font-semibold text-slate-500">
        Actions
      </div>
    </div>
  );
}

type tActionProps = {
  goal: tGoalList;
  mutate: () => void;
};

function EditGoal({ goal, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    title: yup.string().required("Goal name is required!"),
    category: yup.string().required("Category is required!"),
  });

  const defaultValues = {
    title: goal.title,
    category: goal.category,
    description: goal.description,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.put(
        `/goal/${goal.id}`,
        { ...data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        await mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Goal successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update goal!");
    }
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <FiEdit size={14} color="#0000b3" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Goal</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Goal Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter goal name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Category</FormLabel>
                  <FormDescription>What type of goal is it?</FormDescription>

                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("category")
                            ? form.getValues("category")
                            : "Select A Category"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Learn a piece">
                        Learn a piece
                      </SelectItem>
                      <SelectItem value="Play in a concert">
                        Play in a concert
                      </SelectItem>
                      <SelectItem value="Pass an exam">Pass an exam</SelectItem>
                      <SelectItem value="Do a competition">
                        Do a competition
                      </SelectItem>
                      <SelectItem value="Learn a skill">
                        Learn a skill
                      </SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Goal Description (optional)</FormLabel>
                  <FormDescription>
                    A bit more information about what is to be achieved
                  </FormDescription>
                  <FormControl>
                    <Textarea
                      placeholder="Type your goal description here."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submission Button .... */}

            <div className=" mt-5 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function DeleteGoal({ goal, mutate }: tActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDelte = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      let response = await api.delete(`/goal/${goal.id}`, {
        headers: {
          Authorization: "Bearer " + token,
        },
      });

      if (response.status === 200) {
        setIsLoading(false);
        await mutate();
        toast.success(
          `Goal with Tutor ${goal.tutor.fullname} has been successfully deleted!`,
        );
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to delete goal!");
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <div className=" cursor-pointer">
          <RiDeleteBin6Line size={16} color="#ff0000" />
        </div>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action is irreversible and will permanently delete the goal
            details from our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelte}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
