"use client";

import { tPaymentL } from "types/payments";
import React, { useState } from "react";
import moment from "moment";
import Avatar from "react-avatar";
import Link from "next/link";
import { BiEdit } from "react-icons/bi";
import { MdDelete } from "react-icons/md";

import { Button } from "components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import "react-datepicker/dist/react-datepicker.css";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import ReactDatePicker from "react-datepicker";
import { Textarea } from "@components/ui/textarea";
import { Input } from "@components/ui/input";
import { getSession } from "next-auth/react";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { Dots } from "react-activity";
import { IoIosSettings } from "react-icons/io";

type Props = {
  Payment: tPaymentL | undefined;
  mutate: () => void;
};

export default function Payment({ Payment, mutate }: Props) {
  const Action = {
    // GotoStripe: () => {
    //   router.push(
    //     "https://dashboard.stripe.com/payments/pi_3NqeBAI3cEl3Qs971NaPTWab",
    //     undefined,
    //     { target: "_blank" }
    //   );
    // },
    // GotoStuDetails: () => {
    //   router.push(`/students/${Payment?.student?.id}`);
    // },
  };

  return (
    <div className="mb-2 grid grid-cols-6 items-center gap-x-4 rounded-md border border-gray-200 bg-white py-2.5">
      <div className="relative">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute -top-1.5 left-1">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {Payment && (
              <DropdownMenuGroup className="space-y-2 p-1">
                <EditPayments payment={Payment} mutate={mutate} />
                <DeltePayment payment={Payment} mutate={mutate} />
              </DropdownMenuGroup>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-5 flex flex-row items-center">
          <Avatar name={Payment?.student?.fullname} round={true} size="42" />
          <Link
            href={`students/${Payment?.student?.id}`}
            target="_blank"
            className="ml-1.5 max-w-[120px] overflow-hidden truncate text-sm font-semibold text-gray-800 hover:cursor-pointer hover:underline"
          >
            {Payment?.student?.fullname}
          </Link>
        </div>
      </div>

      <div className="text-center text-xs font-medium text-gray-600">
        {moment(Payment?.date)
          .add(new Date().getTimezoneOffset() * -1, "minute")
          .format("DD MMM YYYY")}
      </div>

      {Payment?.stripeIntent ? (
        <Link
          href={`https://dashboard.stripe.com/payments/${Payment?.stripeIntent}`}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center"
        >
          <div
            // onClick={Action.GotoStripe}
            className="flex h-6 w-12 cursor-pointer items-center justify-center rounded bg-indigo-500 hover:bg-indigo-700"
          >
            <div className=" text-center text-xs font-medium text-white">
              Stripe
            </div>
          </div>
        </Link>
      ) : (
        <div className="text-center text-xs font-medium text-gray-600">
          Manual
        </div>
      )}

      <div className="space-y-0.5">
        <div className="max-w-[120px] overflow-hidden truncate text-xs font-medium text-gray-600">
          {Payment?.type ? Payment.type : "Unknown Type"}
        </div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <p className="max-w-[96px] overflow-hidden truncate text-[11px] font-light text-gray-600">
                {Payment?.comment}
              </p>
            </TooltipTrigger>
            <TooltipContent className=" max-w-sm break-words">
              <p>{Payment?.comment}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="text-center text-xs font-medium text-gray-600">
        {Number.isInteger(Payment?.discounts)
          ? Payment?.discounts
          : Payment?.discounts.toFixed(2)}
      </div>

      <div className="text-center text-xs font-medium text-gray-600">
        £
        {Number.isInteger(Payment?.amount)
          ? Payment?.amount
          : Payment?.amount.toFixed(2)}
      </div>
    </div>
  );
}

type ActionProps = {
  payment: tPaymentL;
  mutate: () => void;
};

function EditPayments({ payment, mutate }: ActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    amount: yup
      .number()
      .typeError("Enter a valid number for the amount. This field is required.")
      .required("Amount is required!"),
    date: yup.date().required("Date is required!"),
    type: yup.string().required("Type is required!"),
  });

  const defaultValues = {
    amount: payment?.amount,
    date: payment?.date ? new Date(payment.date) : new Date(),
    type: payment?.type,
    comment: payment?.comment,
  };

  const [isOpen, setIsOpen] = useState(false);
  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const res = await api.put(
        `/payment/${payment?.id}`,
        {
          ...data,
          date: moment(data.date).format("YYYY-MM-DD"),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (res.status === 200) {
        mutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Payment successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update payment!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <BiEdit size={16} color="gray" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Update Payment
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Payment</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Type </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className=" capitalize">
                          {form.getValues("type")
                            ? form.getValues("type")
                            : "Select A Type"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Online">Online</SelectItem>
                      <SelectItem value="In-Person">In-Person</SelectItem>
                      <SelectItem value="Stripe">Stripe</SelectItem>
                      <SelectItem value="Others">Others</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Payment Date</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                  <FormDescription>
                    Cange in date will not be reflected. Temporarily disabled
                    from the Server.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Comment</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Type your comment here."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className=" flex w-full flex-row justify-end space-x-1">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function DeltePayment({ payment, mutate }: ActionProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDelte = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      let res = await api.delete(`/payment/${payment?.id}`, {
        headers: {
          Authorization: "Bearer " + token,
        },
      });

      if (res.status === 200) {
        setIsLoading(false);
        mutate();
        toast.success(
          `Payment by ${payment?.student.fullname} has been successfully deleted!`,
        );
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to delete payment!");
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <MdDelete size={16} color="gray" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Delete
          </span>
        </div>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action is irreversible and will permanently delete the payment
            details from our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelte}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export function PaymentListHeader() {
  return (
    <div className=" grid grid-cols-6 gap-x-4 py-2">
      <div className=" text-sm font-medium text-gray-500 ">Name</div>
      <div className=" text-center text-sm font-medium text-gray-500">Date</div>
      <div className=" text-center text-sm font-medium text-gray-500">
        Payment
      </div>

      <div className="space-y-0.5">
        <div className="text-sm font-medium text-gray-500 ">Type</div>
        <div className="text-xs font-normal text-gray-500">Comment</div>
      </div>

      <div className=" text-center text-sm font-medium text-gray-500">
        Discount
      </div>

      <div className=" text-center text-sm font-medium text-gray-500 ">
        Amount
      </div>
    </div>
  );
}
