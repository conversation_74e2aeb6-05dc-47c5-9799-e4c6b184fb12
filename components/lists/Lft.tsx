"use client";

import { tLftL } from "types/lft";
import React, { useEffect, useState } from "react";
import Avatar from "react-avatar";
import Link from "next/link";
import { TiTick } from "react-icons/ti";

import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "components/ui/sheet";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";
import { Button } from "components/ui/button";
import { GenerateQuery } from "core/Query";

import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";
import Image from "next/image";
import moment from "moment";

type Props = {
  lft: tLftL;
  mutate: () => void;
};

export default function Lft({ lft, mutate }: Props) {
  return (
    <Link
      href={`/lft/${lft.id}`}
      target="_blank"
      className="relative mb-1.5 grid grid-cols-7 items-center gap-x-2 rounded-md  border border-gray-200 bg-white px-1.5
      py-2 hover:bg-gray-200"
    >
      <div className="col-span-2">
        <div className="flex flex-row items-center ">
          <Avatar name={lft.student.fullName} round={true} size="45" />

          <div className="ml-1.5">
            <div className=" flex flex-row items-center">
              <div className="max-w-[128px] overflow-hidden truncate text-sm font-semibold text-gray-800">
                {lft.student.fullName}
              </div>
              {lft.student.country && (
                <Image
                  src={
                    lft.student.country === "United Kingdom" ? UKFlag : USFlag
                  }
                  alt={lft.student.country}
                  className="ml-0.5 h-4 w-4"
                />
              )}
            </div>

            <div className="max-w-[128px]  overflow-hidden truncate text-[11px] font-light text-gray-500">
              {moment(lft.addedOn)
                .add(new Date().getTimezoneOffset() * -1, "minute")
                .format("DD MMM YYYY")}
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-0.5">
        <div className="max-w-[144px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {lft.admin.fullName}
        </div>
        <div className="max-w-[144px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {lft.admin.email ? lft.admin.email : "----"}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[112px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {lft.homeTutor?.fullName ? lft.homeTutor.fullName : "-----"}
        </div>
        <div className="max-w-[112px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {lft.onlineTutor?.fullName ? lft.onlineTutor.fullName : "-----"}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[112px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {lft.lessonType ? lft.lessonType : "-----"}
        </div>
        <div className="max-w-[112px] overflow-hidden truncate text-[11px] font-light text-gray-500">
          {lft.learnerType ? lft.learnerType : "-----"}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[96px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {lft.instrument ? lft.instrument : "-----"}
        </div>
      </div>

      {lft.isReady && (
        <TiTick size={18} className="absolute right-0 mr-1.5 text-green-600" />
      )}
    </Link>
  );
}

type headerProps = {
  updateQuery: any;
  name: string;
  setName: any;
  updatePage: any;
  pageReset: any;
};

export function LftListHeader({
  updateQuery,
  name,
  setName,
  updatePage,
  pageReset,
}: headerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isPageActive, setIsPageActive] = useState<boolean>(false);

  const [instrument, setInstrument] = useState<string>("");
  const [lessonType, setLessonTypet] = useState<string>("");

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
    },
    onOpen: () => {
      setIsOpen(true);
    },
  };

  const Update = {
    Instrument: (e: any) => {
      setInstrument(e);
    },

    LessonType: (e: any) => {
      setLessonTypet(e);
    },
  };

  const Action = {
    generateQuery: () => {
      if (name.length > 2) {
        updateQuery(
          GenerateQuery({
            student: name,
            lessonType,
            instrument,
          }),
        );
      } else {
        updateQuery(
          GenerateQuery({
            lessonType,
            instrument,
          }),
        );
      }
    },

    Reset: () => {
      setInstrument("");
      setLessonTypet("");

      setName("");
      updateQuery("");
      pageReset("");
    },
  };

  useEffect(() => {
    if (isPageActive) {
      Action.generateQuery();
    }
  }, [name]);

  useEffect(() => {
    setIsPageActive(false);

    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get("p")) {
      updatePage(Number(urlParams.get("p")));
    }
    urlParams.delete("p");

    if (urlParams.toString() !== "") {
      setLessonTypet(urlParams.get("lessonType") || "");
      setInstrument(urlParams.get("instrument") || "");

      if (name) setName(urlParams.get("name") || "");

      if (updateQuery) updateQuery("&" + urlParams.toString());
    }
    setIsPageActive(true);
  }, []);

  return (
    <Sheet onOpenChange={ModalControl.reset} open={isOpen}>
      <SheetTrigger asChild onClick={ModalControl.onOpen}>
        <div className="grid grid-cols-7 items-center gap-x-2 px-1.5 py-2">
          <div className="col-span-2">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Student Name
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Added On
            </div>
          </div>

          <div className="col-span-2">
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Admin Name
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Admin Email
            </div>
          </div>

          <div>
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Home Tutor
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Online Tutor
            </div>
          </div>

          <div>
            <div className="cursor-pointer text-sm font-medium text-gray-500">
              Lesson Type
            </div>
            <div className="cursor-pointer text-xs font-normal text-gray-500">
              Learner Type
            </div>
          </div>

          <div className="cursor-pointer text-center text-sm font-medium text-gray-500">
            Instrument
          </div>
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter Lft Information</SheetTitle>
        </SheetHeader>
        <div className="hide-scrollbar h-full w-full overflow-y-scroll px-1 pb-24 pt-4">
          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Lesson Type
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.LessonType} defaultValue={lessonType}>
              <SelectTrigger className="w-full bg-white">
                <div>{lessonType ? lessonType : "All"}</div>
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="Unsure">Unsure</SelectItem>
                  <SelectItem value="Online">Online</SelectItem>
                  <SelectItem value="In-Person">In-Person</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="mb-1 mt-4 text-xs font-semibold text-slate-700">
            Instrument
          </div>
          <div className="mt-1">
            <Select onValueChange={Update.Instrument} defaultValue={instrument}>
              <SelectTrigger className="w-full bg-white">
                <div>{instrument ? instrument : "All"}</div>
              </SelectTrigger>
              <SelectContent className="hide-scrollbar max-h-52 overflow-y-scroll py-2 text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value={null as any}>All</SelectItem>
                  <SelectItem value="ABRSM Music Theory Grades">
                    ABRSM Music Theory Grades
                  </SelectItem>
                  <SelectItem value="Bass Guitar">Bass Guitar</SelectItem>
                  <SelectItem value="Cello">Cello</SelectItem>
                  <SelectItem value="Clarinet">Clarinet</SelectItem>
                  <SelectItem value="Double Bass">Double Bass</SelectItem>
                  <SelectItem value="Drums">Drums</SelectItem>
                  <SelectItem value="Flute">Flute</SelectItem>
                  <SelectItem value="French Horn">French Horn</SelectItem>
                  <SelectItem value="Guitar">Guitar</SelectItem>
                  <SelectItem value="Harp">Harp</SelectItem>
                  <SelectItem value="Jazz Piano">Jazz Piano</SelectItem>

                  <SelectItem value="Oboe">Oboe</SelectItem>
                  <SelectItem value="Piano - grades 1 to 5">
                    Piano - grades 1 to 5
                  </SelectItem>
                  <SelectItem value="Piano - grades 5 and above">
                    Piano - grades 5 and above
                  </SelectItem>
                  <SelectItem value="Sax">Sax</SelectItem>
                  <SelectItem value="Singing">Singing</SelectItem>
                  <SelectItem value="Trombone">Trombone</SelectItem>
                  <SelectItem value="Trumpet">Trumpet</SelectItem>
                  <SelectItem value="Viola">Viola</SelectItem>
                  <SelectItem value="Violin">Violin</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <div className="fixed bottom-1 flex  flex-row items-center justify-between space-x-4 py-3">
              <Button
                type="reset"
                onClick={Action.Reset}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={Action.generateQuery}
                className="border border-blue-600 bg-blue-600 hover:bg-white hover:text-blue-600"
              >
                Apply Filter
              </Button>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
