"use client";

import { tSurvey<PERSON> } from "types/surveys";
import React from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import Avatar from "react-avatar";
import { Badge } from "components/ui/badge";
import Delete from "components/global/Delete";

import { TbListDetails } from "react-icons/tb";
import { IoIosSettings } from "react-icons/io";
import { FiUsers } from "react-icons/fi";
import { MdOutlinePoll } from "react-icons/md";
import { RiDeleteBin7Line } from "react-icons/ri";
import Link from "next/link";

type Props = {
  survey: tSurveyL;
  mutate: () => void;
};

export default function Survey({ survey, mutate }: Props) {
  const handleViewDetails = () => {
    // Store survey data in localStorage for details page
    localStorage.setItem(`survey_${survey.studentId}`, JSON.stringify(survey));
    // Open in new tab
    window.open(`/surveys/${survey.studentId}`, "_blank");
  };

  return (
    <div className="mb-2 grid grid-cols-1 items-center gap-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md md:grid-cols-2 lg:grid-cols-4">
      {/* Column 1: Avatar + Name */}
      <div className="relative flex min-w-0 items-center space-x-3">
        <Avatar name={survey.name} round={true} size="45" />

        <div className="min-w-0 flex-1">
          <Link
            href={`/students/${survey.studentId}`}
            target="_blank"
            className="block truncate text-left text-sm font-semibold text-gray-800 hover:text-cyan-600 hover:underline"
          >
            {survey.name}
          </Link>
        </div>
      </div>

      {/* Column 2: Survey Option */}
      <div className="flex flex-col space-y-1">
        <div className="flex items-center space-x-2 text-sm font-medium text-gray-700">
          <MdOutlinePoll size={14} className="text-gray-500" />
          <span>{survey.option}</span>
        </div>
      </div>

      {/* Column 3: Total Referrals */}
      <div className="min-w-0">
        <div className="flex items-center space-x-2">
          <FiUsers size={14} className="text-gray-500" />
          <Badge
            variant={survey.totalReferred > 0 ? "default" : "secondary"}
            className="text-xs font-bold"
            style={{
              backgroundColor: survey.totalReferred > 0 ? "#0094ba" : undefined,
            }}
          >
            {survey.totalReferred} Referrals
          </Badge>
        </div>
      </div>

      {/* Column 4: Actions */}
      <div className="flex flex-col items-start space-y-2 lg:items-end">
        <button
          onClick={handleViewDetails}
          className="flex items-center space-x-2 rounded-md border border-cyan-300 bg-cyan-50 px-3 py-2 text-xs text-cyan-600 transition-colors hover:border-cyan-200 hover:bg-white"
        >
          <TbListDetails size={16} />
          <span className="hidden sm:inline">View Details</span>
          <span className="sm:hidden">Details</span>
        </button>

        <div className="flex items-center space-x-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 text-xs text-gray-600 shadow-sm transition-all duration-200 hover:border-red-200 hover:bg-red-50 hover:text-red-600 hover:shadow-md">
          <Delete
            apiUrl={`/survey/${survey.studentId}`}
            mutate={mutate}
            successMsg={`Survey has been successfully deleted!`}
            erroMsg="Failed to delete survey!"
            headerTitle="Delete"
            icon={<RiDeleteBin7Line size={14} color="currentColor" />}
            des="Survey"
          />
        </div>
      </div>
    </div>
  );
}

export function SurveyListHeader() {
  return (
    <div className="hidden items-center gap-4 rounded-t-lg border-b border-gray-200 bg-gray-50 px-4 py-3 lg:grid lg:grid-cols-4">
      <div className="text-sm font-semibold text-gray-600">Name</div>
      <div className="text-sm font-semibold text-gray-600">Survey Option</div>
      <div className="text-sm font-semibold text-gray-600">Total Referrals</div>
      <div className="text-right text-sm font-semibold text-gray-600">
        Actions
      </div>
    </div>
  );
}
