"use client";

import React from "react";
import Link from "next/link";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import { TbListDetails } from "react-icons/tb";
import { IoIosSettings } from "react-icons/io";
import { MdDelete } from "react-icons/md";
import Delete from "@components/global/Delete";
import { tNotificationL } from "types/notifications";
import moment from "moment";

import Image from "next/image";
import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";

type Props = {
  template: tNotificationL;
  mutate: () => void;
};

export default function Notification({ template, mutate }: Props) {
  // Helper function to format date ranges without country prefix
  const formatDateRange = (
    startDate: Date | string | null,
    endDate: Date | string | null,
  ) => {
    // Helper to format a single date
    const formatSingleDate = (date: Date | string | null) => {
      if (!date) return "Not set yet";

      try {
        const momentDate = moment(date);
        if (!momentDate.isValid()) return "Invalid date";

        return momentDate
          .add(new Date().getTimezoneOffset() * -1, "minute")
          .format("D MMM YYYY");
      } catch {
        return "Invalid date";
      }
    };

    // Format both dates
    const formattedStart = formatSingleDate(startDate);
    const formattedEnd = formatSingleDate(endDate);

    return `${formattedStart} - ${formattedEnd}`;
  };

  return (
    <div className="mb-1.5 grid grid-cols-4 items-center gap-x-4 rounded-md border border-gray-200 bg-white py-2.5">
      <div className="relative col-span-2">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute -top-1.5 left-1.5">
              <IoIosSettings
                size={22}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-1.5 p-1">
                <Link
                  href={`/notifications/${template.id}`}
                  target="_blank"
                  className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <TbListDetails size={16} color="gray" />
                  <span className="ml-1 text-xs font-medium text-slate-600">
                    View
                  </span>
                </Link>

                <div className="w-44 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`/popup/${template.id}`}
                    mutate={mutate}
                    successMsg={`Template has been successfully deleted!`}
                    erroMsg="Failed to delete template!"
                    headerTitle="Delete"
                    icon={<MdDelete size={16} color="gray" />}
                    des="template details"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-8 space-y-2">
          <div className="flex flex-row items-center space-x-1.5">
            <Image src={UKFlag} alt="UK" className="ml-[2px] h-4 w-4" />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={`/notifications/${template.id}?tab=uk`}
                    target="_blank"
                    className="max-w-md overflow-hidden truncate text-sm font-medium text-gray-800 hover:cursor-pointer hover:underline"
                  >
                    {template.ukTitle || "-------"}
                  </Link>
                </TooltipTrigger>

                {template.ukTitle && (
                  <TooltipContent className="max-w-xs break-words rounded border bg-slate-400 px-2.5 py-1 text-[10px] font-medium text-slate-50">
                    {template.ukTitle}
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="flex flex-row items-center space-x-1.5">
            <Image src={USFlag} alt="UK" className="ml-[2px] h-4 w-4" />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={`/notifications/${template.id}?tab=us`}
                    target="_blank"
                    className="max-w-md overflow-hidden truncate text-sm font-medium text-gray-800 hover:cursor-pointer hover:underline"
                  >
                    {template.usTitle || "-------"}
                  </Link>
                </TooltipTrigger>
                {template.usTitle && (
                  <TooltipContent className=" max-w-xs break-words rounded border  bg-slate-400 px-2.5 py-1 text-[10px]  font-medium text-slate-50">
                    {template.usTitle}
                  </TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      <div className="text-center text-xs font-medium text-gray-500">
        {template.userType}
      </div>

      <div className="mr-4 space-y-2">
        {/* UK Date Range */}
        <div className="flex flex-row items-center space-x-1">
          <Image src={UKFlag} alt="UK" className="ml-[2px] h-4 w-4" />

          <div className="text-xs font-medium text-gray-600">
            ({formatDateRange(template.ukStartDate, template.ukEndDate)})
          </div>
        </div>

        {/* US Date Range */}
        <div className="flex flex-row items-center space-x-1">
          <Image src={USFlag} alt="UK" className="ml-[2px] h-4 w-4" />
          <div className="text-xs font-medium text-gray-600">
            ({formatDateRange(template.usStartDate, template.usEndDate)})
          </div>
        </div>
      </div>
    </div>
  );
}

export function NotificationListHeader() {
  return (
    <div className="grid grid-cols-4 items-center gap-x-4 py-2.5">
      <div className="col-span-2 text-sm font-medium text-gray-600">Title</div>

      <div className="text-center text-sm font-medium text-gray-500">
        User type
      </div>

      <div className="text-sm font-medium text-gray-600">Duration</div>
    </div>
  );
}
