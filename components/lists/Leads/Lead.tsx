import React from "react";

type Props = {};

export default function Lead({}: Props) {
  return (
    <div className="relative mb-1.5 grid grid-cols-4 items-center gap-x-2.5 rounded-md border border-gray-200 bg-white px-1.5 py-2.5">
      <div></div>
      <div className="col-span-2"></div>
      <div className="flex items-center justify-center">
        <div className="text-xs font-medium text-gray-600">12 June 2024</div>
      </div>
    </div>
  );
}

export function LeadListHeader() {
  return (
    <div className="mb-1.5 grid grid-cols-4 gap-2.5 px-1.5 py-2">
      <div className="text-sm font-medium text-gray-500">Name</div>

      <div className="col-span-2">
        <div className="text-sm font-medium text-gray-500">Registered Page</div>
      </div>

      <div className="flex flex-col items-center justify-center">
        <div className="text-sm font-medium text-gray-500">Registered Date</div>
      </div>
    </div>
  );
}
