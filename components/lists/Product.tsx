"use client";

import { tProductL } from "types/product";
import React from "react";
import moment from "moment";
import Avatar from "react-avatar";

import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";
import { IoIosSettings } from "react-icons/io";
import Delete from "@components/global/Delete";
import Link from "next/link";
import { TbShoppingBagEdit } from "react-icons/tb";
import { RiDeleteBin7Line } from "react-icons/ri";
import { BsBagCheck } from "react-icons/bs";

type Props = {
  product: tProductL;
  mutate: () => void;
};
export default function Product({ product, mutate }: Props) {
  return (
    <div className="relative mb-1.5 grid grid-cols-6 items-center gap-x-2 rounded-md  border border-gray-200 bg-white px-1.5 py-2.5">
      <div className="col-span-2">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute left-1 top-0.5">
              <IoIosSettings
                size={20}
                className="cursor-pointer text-blue-500"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-2 p-1.5">
                <Link
                  href={`/products/edit?id=${product.id}`}
                  target="_blank"
                  className="flex w-48 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <TbShoppingBagEdit size={18} color="gray" />
                  <span className="ml-1 text-xs font-medium text-slate-600">
                    Update
                  </span>
                </Link>

                <Link
                  href={`/products/confirmed-bookings?id=${product.id}`}
                  target="_blank"
                  className="flex w-48 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <BsBagCheck size={16} color="gray" />

                  <span className="ml-1 text-xs font-medium text-slate-600">
                    Confirmed Bookings
                  </span>
                </Link>

                <div className="w-48 cursor-pointer rounded-md border-0 border-input bg-transparent  px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`/product/${product.id}`}
                    mutate={mutate}
                    successMsg={`Product has been successfully deleted!`}
                    erroMsg="Faild to delete product!"
                    headerTitle="Delete"
                    icon={<RiDeleteBin7Line size={16} color="gray" />}
                    des="Product"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <div className="ml-2.5 flex flex-row items-center ">
          {product.thumbnail ? (
            <img
              src={product.thumbnail}
              alt="Image"
              className="h-16 w-16 rounded-full"
            />
          ) : (
            <Avatar name={product.title} round={true} size="64" />
          )}

          {/* <Image
          src={product.thumbnail}
          alt="Thumbnail"
          width={500}
          height={500}
        /> */}

          <div className="ml-2.5 flex flex-col space-y-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={`/products/edit?id=${product.id}`}
                    target="_blank"
                    className="block max-w-40 overflow-hidden truncate text-sm font-semibold text-gray-800 hover:underline"
                  >
                    {product.title}
                  </Link>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm break-words rounded bg-slate-400 px-2 py-1 text-[10px] font-medium text-slate-50">
                  {product.title}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div className="max-w-40 overflow-hidden truncate text-[11px] font-light text-gray-500">
              {product.country}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-1">
        <div className="max-w-36 overflow-hidden truncate text-xs font-medium text-gray-800">
          {moment(product.start)
            .add(new Date().getTimezoneOffset() * -1, "minute")
            .format("DD MMM YYYY")}
        </div>
        <div className="max-w-36 overflow-hidden truncate text-[11px] font-light text-gray-500">
          {product.end
            ? moment(product.end)
                .add(new Date().getTimezoneOffset() * -1, "minute")
                .format("DD MMM YYYY")
            : "Not Given"}
        </div>
      </div>

      <div className="space-y-1">
        <div className="max-w-36 overflow-hidden truncate text-xs font-medium text-gray-800">
          {product.type}
        </div>

        <div className="max-w-36 overflow-hidden truncate text-[11px] font-light text-gray-500">
          {product.category}
        </div>
      </div>

      <div className="flex items-center justify-center">
        <div className="max-w-[96px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {product.tickets}
        </div>
      </div>
      <div className="flex items-center justify-center">
        <div className="max-w-[96px] overflow-hidden truncate text-xs font-medium text-gray-800">
          {product.price}
        </div>
      </div>
    </div>
  );
}

export function ProductListHeader() {
  return (
    <div className="grid grid-cols-6 items-start gap-x-2 px-1.5 py-2">
      <div className="col-span-2 space-y-1">
        <div className="text-sm font-medium text-gray-500">Title</div>
        <div className="text-xs font-normal text-gray-500">Country</div>
      </div>

      <div className="space-y-1">
        <div className="text-sm font-medium text-gray-500">Start Date</div>
        <div className="text-xs font-normal text-gray-500">End Date</div>
      </div>

      <div className="space-y-1">
        <div className="text-sm font-medium text-gray-500">Type</div>
        <div className="text-xs font-normal text-gray-500">Category</div>
      </div>

      <div className="text-center text-sm font-medium text-gray-500">
        Tickets
      </div>

      <div className="text-center text-sm font-medium text-gray-500">Price</div>
    </div>
  );
}
