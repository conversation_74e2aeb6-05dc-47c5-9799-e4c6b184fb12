"use client";

import { tConcertL } from "types/concerts";
import React, { useCallback, useState } from "react";
import Link from "next/link";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "components/ui/dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

import Avatar from "react-avatar";
import { Button } from "components/ui/button";
import { Input } from "components/ui/input";
import { Textarea } from "components/ui/textarea";
import { QRCodeSVG } from "qrcode.react";

import { TbListDetails, TbQrcode } from "react-icons/tb";
import { IoIosSettings } from "react-icons/io";
import { MdDelete, MdDownload } from "react-icons/md";
import { FiCalendar, FiClock, FiEdit } from "react-icons/fi";
import Delete from "@components/global/Delete";
import toast from "react-hot-toast";
import Image from "next/image";
import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";

import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { getSession } from "next-auth/react";
import api from "fetchers/BaseUrl";
import { Dots } from "react-activity";

type Props = {
  concert: tConcertL;
  mutate: () => void;
};

export default function Concert({ concert, mutate }: Props) {
  const downloadQRCode = useCallback(async (concert: tConcertL) => {
    try {
      const qrData = concert.url;

      // Create a temporary container for the QR code
      const tempContainer = document.createElement("div");
      tempContainer.style.position = "absolute";
      tempContainer.style.left = "-9999px";
      document.body.appendChild(tempContainer);

      // Create QR code SVG element
      const qrElement = document.createElement("div");
      tempContainer.appendChild(qrElement);

      // Import React and ReactDOM for rendering
      const { createRoot } = await import("react-dom/client");
      const React = await import("react");

      // Create root and render QR code
      const root = createRoot(qrElement);
      root.render(
        React.createElement(QRCodeSVG, {
          value: qrData,
          size: 400,
          fgColor: "#0094ba",
          bgColor: "#ffffff",
          level: "L",
          includeMargin: true,
        }),
      );

      // Wait for rendering to complete
      setTimeout(() => {
        const svgElement = qrElement.querySelector("svg");
        if (svgElement) {
          // Convert SVG to canvas
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          const img = new globalThis.Image(); // Use native Image constructor

          canvas.width = 400;
          canvas.height = 400;

          // Convert SVG to data URL
          const svgData = new XMLSerializer().serializeToString(svgElement);
          const svgBlob = new Blob([svgData], {
            type: "image/svg+xml;charset=utf-8",
          });
          const url = URL.createObjectURL(svgBlob);

          img.onload = () => {
            if (ctx) {
              // Draw white background
              ctx.fillStyle = "#ffffff";
              ctx.fillRect(0, 0, 400, 400);

              // Draw the QR code
              ctx.drawImage(img, 0, 0, 400, 400);

              // Convert canvas to blob and download
              canvas.toBlob((blob) => {
                if (blob) {
                  const link = document.createElement("a");
                  link.href = URL.createObjectURL(blob);
                  link.download = `${concert.concertTitle.replace(/\s+/g, "_")}_QR.png`;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  URL.revokeObjectURL(link.href);
                }
              }, "image/png");
            }
            URL.revokeObjectURL(url);
          };

          img.src = url;
        }

        // Cleanup
        root.unmount();
        document.body.removeChild(tempContainer);
      }, 100);

      toast.success("QR Code downloaded successfully!");
    } catch (error: any) {
      console.error("QR Download Error:", error);
      toast.error("Failed to download QR Code!");
    }
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      }),
      time: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
    };
  };

  const { date, time } = formatDate(concert.sessionDate);

  // Generate avatar initials from concert title
  const getInitials = (title: string) => {
    return title
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="mb-2 grid grid-cols-1 items-center gap-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md md:grid-cols-2 lg:grid-cols-4">
      {/* Column 1: Avatar + Concert Title */}
      <div className="relative flex min-w-0 items-center space-x-3">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="absolute -left-2 -top-2 z-10">
              <IoIosSettings
                size={18}
                className="cursor-pointer text-blue-500 hover:text-blue-600"
              />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuGroup>
              <div className="flex flex-col space-y-1.5 p-1">
                <Link
                  href={`/concerts/${concert.id}`}
                  target="_blank"
                  className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground"
                >
                  <TbListDetails size={16} color="gray" />
                  <span className="ml-2 text-xs font-medium text-slate-600">
                    View Details
                  </span>
                </Link>

                <UpdateConcert concert={concert} mutate={mutate} />

                <div className="w-44 cursor-pointer rounded-md border-0 border-input bg-transparent px-4 py-2 text-xs font-medium text-slate-600 shadow-sm hover:bg-accent hover:text-accent-foreground">
                  <Delete
                    apiUrl={`/concert/${concert.id}`}
                    mutate={mutate}
                    successMsg={`${concert.concertTitle} has been successfully deleted!`}
                    erroMsg="Failed to delete concert!"
                    headerTitle="Delete Concert"
                    icon={<MdDelete size={16} color="gray" />}
                    des="concert details"
                  />
                </div>
              </div>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>

        <Avatar name={concert.concertTitle} round={true} size="45" />

        <div className="min-w-0 flex-1">
          <div className="flex items-center space-x-2">
            <Link
              href={`/concerts/${concert.id}`}
              target="_blank"
              className="block truncate text-sm font-semibold text-gray-800 hover:text-blue-600 hover:underline"
            >
              {concert.concertTitle}
            </Link>
            {concert.country && (
              <Image
                src={concert.country === "United Kingdom" ? UKFlag : USFlag}
                alt={concert.country}
                className="h-4 w-4 flex-shrink-0"
              />
            )}
          </div>
        </div>
      </div>

      {/* Column 2: Session Date & Time */}
      <div className="flex flex-col space-y-1">
        <div className="flex items-center space-x-2 text-sm font-medium text-gray-700">
          <FiCalendar size={14} className="text-gray-500" />
          <span>{date}</span>
        </div>
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <FiClock size={12} className="text-gray-400" />
          <span>{time}</span>
        </div>
      </div>

      {/* Column 3: Details with Tooltip */}
      <div className="min-w-0">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="cursor-help truncate text-sm text-gray-600">
                {concert.details}
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p className="text-xs">{concert.details}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Column 4: QR Code Download Button */}
      <div className="flex justify-start lg:justify-end">
        <Button
          onClick={() => downloadQRCode(concert)}
          variant="outline"
          size="sm"
          className="flex items-center space-x-2 border-blue-200 text-xs text-blue-600 hover:border-blue-300 hover:bg-blue-50"
        >
          <TbQrcode size={16} />
          <MdDownload size={14} />
          <span className="hidden sm:inline">Download QR</span>
          <span className="sm:hidden">QR</span>
        </Button>
      </div>
    </div>
  );
}

type UpdateConcertProps = {
  concert: tConcertL;
  mutate: () => void;
};

function UpdateConcert({ concert, mutate }: UpdateConcertProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    concertTitle: yup.string().required("Concert title is required!"),
    country: yup.string().required("Country is required!"),
    sessionDate: yup.string().required("Session date is required!"),
    details: yup.string().required("Details are required!"),
  });

  const defaultValues = {
    concertTitle: concert.concertTitle || "",
    country: concert.country || "",
    sessionDate: concert.sessionDate ? concert.sessionDate.slice(0, 16) : "",
    details: concert.details || "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      // Convert date to ISO format
      const formattedData = {
        ...data,
        sessionDate: new Date(data.sessionDate).toISOString(),
      };

      const response = await api.put(`/concert/${concert.id}`, formattedData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success("Concert Updated Successfully!");
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to Update Concert! Please Try Again.");
    }
  };

  const reset = (value?: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
    // Reset form with current concert values
    form.setValue("concertTitle", concert.concertTitle || "");
    form.setValue("country", concert.country || "");
    form.setValue(
      "sessionDate",
      concert.sessionDate ? concert.sessionDate.slice(0, 16) : "",
    );
    form.setValue("details", concert.details || "");
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal} className="w-full">
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <FiEdit size={16} color="gray" />
          <span className="ml-2 text-xs font-medium text-slate-600">
            Update Concert
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-full overflow-y-scroll sm:w-full sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Concert</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="concertTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Concert Title</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter concert title"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white text-xs font-normal text-slate-600">
                        {field.value ? field.value : "Select Country"}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-slate-600">
                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sessionDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Session Date & Time</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter concert details"
                      {...field}
                      className="min-h-20 text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex w-full flex-row justify-end space-x-2 pt-4">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white"
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export function ConcertListHeader() {
  return (
    <div className="hidden items-center gap-4 rounded-t-lg border-b border-gray-200 bg-gray-50 px-4 py-3 lg:grid lg:grid-cols-4">
      <div className="text-sm font-semibold text-gray-600">Concert Title</div>
      <div className="text-sm font-semibold text-gray-600">Session Date</div>
      <div className="text-sm font-semibold text-gray-600">Details</div>
      <div className="text-right text-sm font-semibold text-gray-600">
        Actions
      </div>
    </div>
  );
}
