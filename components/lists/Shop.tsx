"use client";

import React from "react";
import { tShopL } from "types/shop";
import Avatar from "react-avatar";
import Link from "next/link";
import { TbListDetails } from "react-icons/tb";
import { FiMail, FiUser, FiDollarSign, FiUsers, FiEdit } from "react-icons/fi";
import { IoIosSettings } from "react-icons/io";
import { MdDelete } from "react-icons/md";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";

type Props = {
  shop: tShopL;
  mutate: () => void;
};

export default function Shop({ shop, mutate }: Props) {
  return (
    <div className="group relative mt-5 rounded-md bg-white p-4 transition-all duration-200 hover:bg-white/95 hover:shadow-sm">
      <div className="grid grid-cols-1 items-center gap-4 lg:grid-cols-12">
        {/* Shop Info Section - 6 columns */}
        <div className="lg:col-span-6">
          <div className="relative flex items-center space-x-4">
            {/* Settings Dropdown */}
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="absolute -left-2 -top-2 z-10">
                  <IoIosSettings
                    size={18}
                    className="cursor-pointer text-blue-500 hover:text-blue-600"
                  />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48">
                <DropdownMenuGroup>
                  <div className="flex flex-col space-y-1 p-1">
                    <Link
                      href={`/shop-registration/${shop.id}`}
                      target="_blank"
                      className="flex w-full cursor-pointer flex-row items-center justify-start rounded-md border-0 bg-transparent px-3 py-2 text-sm transition-colors hover:bg-[#0094ba]/5 hover:text-[#0094ba]"
                    >
                      <TbListDetails size={16} className="text-[#0094ba]" />
                      <span className="ml-3 font-medium">View Details</span>
                    </Link>
                  </div>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Avatar with soft styling */}
            <div className="relative">
              <Avatar
                src={shop.shopLogo}
                name={shop.shopName}
                round={true}
                size="55"
                className="border-2 border-white shadow-sm"
              />
            </div>

            <div className="min-w-0 flex-1">
              <div className="mb-1 flex items-center space-x-2">
                <Link
                  href={`/shop-registration/${shop.id}`}
                  target="_blank"
                  className="truncate text-base font-semibold text-gray-800 transition-colors hover:text-[#0094ba]"
                >
                  {shop.shopName}
                </Link>
                <span className="text-lg">
                  {shop.country === "uk" ? "🇬🇧" : "🇺🇸"}
                </span>
              </div>

              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <div className="flex h-4 w-4 items-center justify-center rounded-sm bg-gradient-to-r from-blue-500/10 to-indigo-500/10">
                    <FiUser size={10} className="text-blue-600" />
                  </div>
                  <span className="truncate text-xs font-medium text-gray-600">
                    {shop.fullName}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex h-4 w-4 items-center justify-center rounded-sm bg-gradient-to-r from-emerald-500/10 to-teal-500/10">
                    <FiMail size={10} className="text-emerald-600" />
                  </div>
                  <span className="truncate text-xs font-medium text-gray-600">
                    {shop.email}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section - 5 columns */}
        <div className="lg:col-span-5">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="mb-1 flex items-center justify-center">
                <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-r from-[#0094ba]/10 to-[#0077a3]/10">
                  <FiUsers size={12} className="text-[#0094ba]" />
                </div>
              </div>
              <div className="text-sm font-bold text-[#0094ba]">
                {shop.referralCount}
              </div>
              <div className="text-xs font-medium text-gray-500">Referrals</div>
            </div>

            <div className="text-center">
              <div className="mb-1 flex items-center justify-center">
                <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-r from-emerald-500/10 to-teal-500/10">
                  <FiDollarSign size={12} className="text-emerald-600" />
                </div>
              </div>
              <div className="text-sm font-bold text-emerald-600">
                ${shop.referralAmount.toFixed(0)}
              </div>
              <div className="text-xs font-medium text-gray-500">Revenue</div>
            </div>

            <div className="text-center">
              <div className="mb-1 flex items-center justify-center">
                <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-r from-purple-500/10 to-pink-500/10">
                  <FiDollarSign size={12} className="text-purple-600" />
                </div>
              </div>
              <div className="text-sm font-bold text-purple-600">
                $
                {shop.referralCount > 0
                  ? (shop.referralAmount / shop.referralCount).toFixed(0)
                  : "0"}
              </div>
              <div className="text-xs font-medium text-gray-500">Avg/Ref</div>
            </div>
          </div>
        </div>

        {/* Actions Section - 1 column */}
        <div className="lg:col-span-1">
          <div className="flex items-center justify-center space-x-2">
            {/* Status Badge */}
            <div className="flex items-center gap-1 rounded-full bg-emerald-50 px-2 py-1">
              <div className="h-2 w-2 animate-pulse rounded-full bg-emerald-500"></div>
              <span className="text-xs font-medium text-emerald-700">
                Active
              </span>
            </div>

            {/* View Details Button */}
            <Link
              href={`/shop-registration/${shop.id}`}
              target="_blank"
              className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-[#0094ba] to-[#0077a3] text-white shadow-sm transition-all hover:from-[#0077a3] hover:to-[#005f85] hover:shadow-md"
              title="View Details"
            >
              <TbListDetails size={16} />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export function ShopListHeader() {
  return (
    <div className="hidden border-b border-gray-100/50 bg-gradient-to-r from-[#0094ba]/5 to-[#0077a3]/5 px-4 py-4 lg:block">
      <div className="grid grid-cols-12 items-center gap-4">
        <div className="col-span-6">
          <div className="text-sm font-bold uppercase tracking-wide text-gray-700">
            Shop Information
          </div>
          <div className="text-xs font-medium text-gray-500">
            Name, Owner & Contact Details
          </div>
        </div>

        <div className="col-span-5">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-sm font-bold uppercase tracking-wide text-gray-700">
                Referrals
              </div>
              <div className="text-xs font-medium text-gray-500">Count</div>
            </div>
            <div>
              <div className="text-sm font-bold uppercase tracking-wide text-gray-700">
                Revenue
              </div>
              <div className="text-xs font-medium text-gray-500">Total</div>
            </div>
            <div>
              <div className="text-sm font-bold uppercase tracking-wide text-gray-700">
                Average
              </div>
              <div className="text-xs font-medium text-gray-500">
                Per Referral
              </div>
            </div>
          </div>
        </div>

        <div className="col-span-1 text-center">
          <div className="text-sm font-bold uppercase tracking-wide text-gray-700">
            Actions
          </div>
          <div className="text-xs font-medium text-gray-500">Status & View</div>
        </div>
      </div>
    </div>
  );
}
