import React, { useEffect, useState } from "react";

import {
  Sheet,
  She<PERSON><PERSON>lose,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "components/ui/sheet";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "components/ui/dropdown-menu";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import { Calendar } from "@components/ui/calendar";
import { Checkbox } from "@components/ui/checkbox";
import { GenerateQuery } from "core/Query";

import InputSelectorForModal from "@components/modals/InputSelectorForModal";
import useSWR from "swr";
import { tChildF, tLesson } from "types/dashboard";
import { fetcher } from "@fetchers/fetcher";
import { FaRegCalendarAlt } from "react-icons/fa";
import moment from "moment";
import { cn } from " lib/utils";

import Avatar from "react-avatar";
import toast from "react-hot-toast";

type Props = {
  lesson: tLesson;
  mutate: () => void;
};

const ColorPicker = (status: string) => {
  if (status === "Confirmed") {
    return "#009959";
  } else if (status === "Pending") {
    return "#0094ba";
  } else if (status === "Late Cancelled By Student") {
    return "#b30000";
  } else if (status === "Cancelled By Student") {
    return "#cc9900";
  } else if (status === "Cancelled By Tutor") {
    return "#8600b3";
  } else return "#003399";
};

export default function Lesson({ lesson, mutate }: Props) {
  return (
    <div
      // className={isSelected ? styles.SInfoContainer : styles.infoContainer}
      className={`mb-2.5 grid grid-cols-8 items-center gap-x-5 rounded-md border border-slate-200 bg-white py-2.5`}
    >
      <div className="col-span-2 flex flex-row items-center pl-2">
        <Avatar name={lesson.student} round={true} size="50" />

        <div className="ml-1.5">
          <div className="flex flex-row items-center">
            <div className="max-w-28 truncate text-sm font-semibold text-slate-800 hover:cursor-pointer hover:underline xl:max-w-32">
              {lesson.student}
            </div>
          </div>

          <div className="max-w-28 truncate pb-1 pt-0.5 text-[11px] font-light text-slate-500">
            {lesson.child ? lesson.child : "No child found!"}
          </div>

          <div className="flex h-4 w-fit flex-col items-center justify-center rounded-lg bg-slate-200 px-1.5 pt-0.5">
            <div className="max-w-24 truncate text-[8px] font-extralight text-slate-500 xl:max-w-28">
              {lesson.postCode ?? "...."}
            </div>
          </div>
        </div>
      </div>

      <div className="col-span-2 space-y-0.5 pl-2.5">
        <div className="flex flex-row items-center">
          <div className="max-w-28 truncate text-xs font-medium text-slate-800 xl:max-w-32">
            {lesson.tutor}
          </div>
        </div>
        <div className="text-[11px] font-light text-slate-500">
          {lesson.type}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="text-xs font-medium text-slate-800">{lesson.price}</div>
        <div className="text-[11px] font-light text-slate-500">
          {lesson.commission}
        </div>

        <div className="text-[10px] font-extralight text-slate-500">
          {lesson.discount}
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[120px] truncate text-xs font-medium text-slate-800">
          {lesson.instrument}
        </div>
        <div className="max-w-[120px]  truncate text-[11px] font-light text-slate-500">
          {lesson.length} Min
        </div>
      </div>

      <div className="space-y-0.5">
        <div className="max-w-[120px]  truncate text-xs font-medium text-slate-800">
          {moment(lesson.dateTime).format("DD MMM YYYY")}
        </div>
        <div className="max-w-[120px]  truncate text-[11px] font-light text-slate-500">
          {moment(lesson.dateTime).format("h:mm a")}
        </div>
      </div>

      <div className="flex items-center justify-center">
        {/* <div
          style={{
            backgroundColor: ColorPicker(lesson.status) + "12",
            borderColor: ColorPicker(lesson.status),
          }}
          className={`rounded border bg-opacity-10 px-2 py-0.5 text-xs font-medium`}
        >
          {lesson.status}
        </div> */}

        <div
          className={`mr-2.5 flex h-8 items-center justify-center rounded-lg border px-2.5`}
          style={{
            backgroundColor: ColorPicker(lesson.status) + "12",
            borderColor: ColorPicker(lesson.status),
          }}
        >
          <div
            className="max-w-28 truncate text-xs font-medium text-slate-600 xl:max-w-32 2xl:max-w-40"
            style={{ color: ColorPicker(lesson.status) }}
          >
            {lesson.status}
          </div>
        </div>
      </div>
    </div>
  );
}

type headerProps = {
  updateQuery?: any;
  name?: string;
  setName?: any;
  updatePage?: any;
  pageReset: any;
};

type tSelector = {
  value: any;
  label: string;
};

type tUserS = {
  id: string;
  fullname: string;
  fullName: string;
};

export function LessonListHeader({
  updateQuery,
  name,
  setName,
  updatePage,
  pageReset,
}: headerProps) {
  const [isPageActive, setIsPageActive] = useState<boolean>(false);

  const [isOpen, setIsOpen] = useState(false);

  const [status, setStatus] = useState<string>("");

  // State for tutor
  const [tutor, setTutor] = useState<string>("All");

  const [seletedTutorId, setSeletedTutorId] = useState<string>("");
  const [isTFocused, setIsTFocused] = useState<Boolean>(false);
  const [isTutorNotFound, setIsTutorNotFound] = useState<boolean>(false);

  // State For Student
  const [student, setStudent] = useState<string>("All");
  const [seletedStuId, setSeletedStuId] = useState<string>("");
  const [isSFocused, setIsSFocused] = useState<Boolean>(false);

  const [selectedChild, setSelectedChild] = useState<string>("");
  const [isStuNotFound, setIsStuNotFound] = useState<boolean>(false);

  //State for date filter ...
  const [isPopoverOpenSDate, setIsPopoverOpenSDate] = useState(false);
  const [isPopoverOpenEDate, setIsPopoverOpenEDate] = useState(false);

  const [isCheckedSDate, setIsCheckedSDate] = useState<boolean>(false);
  const [isCheckedEDate, setIsCheckedEDate] = useState<boolean>(false);

  const [sDate, setSDate] = useState<Date>(new Date());
  const [eDate, setEDate] = useState<Date>(
    new Date(new Date().setDate(new Date().getDate() + 1)),
  );

  // data fetching.....
  const {
    data: Children,
    mutate: childMutate,
    isLoading: chilLoading,
  } = useSWR<Array<tChildF>>(
    seletedStuId ? `/student/${seletedStuId}/children` : null,
    fetcher,
  );

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
    },
    onOpen: () => {
      setIsOpen(true);
    },
  };

  const Update = {
    TutorSearch: (e: any) => {
      setTutor(e.target.value);
    },
    TutorSelect: (tutor: tUserS) => {
      setIsTFocused(false);

      setSeletedTutorId(tutor.id);

      setTutor(tutor.fullName);
    },
    TutorFocus: () => {
      setIsTFocused(true);
      if (tutor === "All") setTutor("");
    },

    StudentSearch: (e: any) => {
      setStudent(e.target.value);
    },
    StudentSelect: (student: tUserS) => {
      setIsSFocused(false);
      // setSelectedStudent(student.id);
      setSeletedStuId(student.id);
      setStudent(student.fullname);
      setSelectedChild("");
    },
    StudentFocus: () => {
      setIsSFocused(true);
      if (student === "All") setStudent("");
    },
    ChildSelect: (e: any) => {
      setSelectedChild(e);
    },

    AddedDate: (e: Date | any) => {
      setSDate(e);
      if (e >= eDate) setEDate(e);
    },
    LastModifiedDate: (e: Date | any) => {
      setEDate(e);
    },
  };

  const Action = {
    generateQuery: () => {
      updateQuery(
        GenerateQuery({
          status,
          tutor: seletedTutorId,
          student: seletedStuId,
          child: selectedChild,

          start: isCheckedSDate ? moment(sDate).format("YYYY-MM-DD") : "",
          end: isCheckedEDate ? moment(eDate).format("YYYY-MM-DD") : "",
        }),
      );

      toast.success("Filter applied.");
    },

    Reset: () => {
      setStatus("");

      setTutor("All");
      setSeletedTutorId("");
      setIsTFocused(false);

      setStudent("All");
      setSeletedStuId("");
      setIsSFocused(false);

      setSelectedChild("");

      setIsPopoverOpenSDate(false);
      setIsPopoverOpenEDate(false);
      setIsCheckedEDate(false);
      setIsCheckedSDate(false);

      setSDate(new Date());
      setEDate(new Date(new Date().setDate(new Date().getDate() + 1)));

      updateQuery("");
      pageReset("");
    },
  };

  console.log("isCheckedEDate", isCheckedEDate);

  console.log("isCheckedSDate", isCheckedSDate);

  useEffect(() => {
    setIsPageActive(false);

    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("p")) {
      updatePage(Number(urlParams.get("p")));
    }
    urlParams.delete("p");

    if (urlParams.toString() !== "") {
      setStatus(urlParams.get("status") || "");
      setSeletedTutorId(urlParams.get("tutor") || "");
      setSeletedStuId(urlParams.get("student") || "");
      setSelectedChild(urlParams.get("child") || "");

      setIsCheckedSDate(urlParams.get("isCheckedSDate") ? true : false);
      setIsCheckedEDate(urlParams.get("isCheckedEDate") ? true : false);

      setSDate(new Date(urlParams.get("sDate") || new Date()));
      setEDate(
        new Date(
          urlParams.get("eDate") ||
            new Date(new Date().setDate(new Date().getDate() + 1)),
        ),
      );

      if (updateQuery) updateQuery("&" + urlParams.toString());
    }
  }, []);

  return (
    <Sheet onOpenChange={ModalControl.reset} open={isOpen}>
      <SheetTrigger asChild onClick={ModalControl.onOpen}>
        <div className="grid grid-cols-8 items-center gap-x-4 py-1.5">
          <div className="col-span-2 space-y-0.5">
            <div className="cursor-pointer text-sm font-medium text-slate-500">
              Student
            </div>
            <div className="cursor-pointer text-xs font-normal text-slate-500">
              Child
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-slate-500">
              Postcode
            </div>
          </div>

          <div className="col-span-2 space-y-0.5 pl-2.5">
            <div className="cursor-pointer  text-sm font-medium text-slate-500">
              Tutor
            </div>
            <div className="cursor-pointer text-xs font-normal text-slate-500">
              Type
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-slate-500"></div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-slate-500">
              Price
            </div>
            <div className="cursor-pointer text-xs font-normal text-slate-500">
              Commission
            </div>
            <div className="cursor-pointer text-[10px] font-extralight text-slate-500">
              Discount
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-slate-500">
              Instrument
            </div>
            <div className="cursor-pointer text-xs font-normal text-slate-500">
              Duration
            </div>
          </div>

          <div className="space-y-0.5">
            <div className="cursor-pointer  text-sm font-medium text-slate-500">
              Date
            </div>
            <div className="cursor-pointer text-xs font-normal text-slate-500">
              Time
            </div>
            {/* <div className="cursor-pointer text-[10px] font-extralight text-slate-500">
              PaymentDate
            </div> */}
          </div>

          <div className="mr-2.5 cursor-pointer text-center text-sm font-medium text-slate-500">
            Status
          </div>
        </div>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter Lesson Information</SheetTitle>
          {/* <SheetDescription>
          Make changes to your profile here. Click save when you're done.
        </SheetDescription> */}
        </SheetHeader>
        <div className="hide-scrollbar h-full w-full overflow-y-scroll px-1 pb-24 pt-4">
          <div className="pb-1 text-sm font-medium text-slate-800">
            Filter status
          </div>

          <Select
            onValueChange={(e: any) => setStatus(e)}
            defaultValue={status}
          >
            <SelectTrigger className="w-full bg-white">
              <div className="text-xs font-medium text-slate-600">
                {status ? status : "All"}
              </div>
            </SelectTrigger>
            <SelectContent className="text-xs font-normal text-slate-600">
              <SelectGroup>
                <SelectItem value={null as any}>All</SelectItem>
                <SelectItem value="Confirmed">Confirmed</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Late Cancelled By Student">
                  Late Cancelled By Student
                </SelectItem>
                <SelectItem value="Cancelled By Student">
                  Cancelled By Student
                </SelectItem>
                <SelectItem value="Cancelled By Tutor">
                  Cancelled By Tutor
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          <div className="my-5">
            <div
              className={`pb-1 text-sm font-medium  ${
                isTutorNotFound ? "text-destructive" : "text-slate-800"
              }`}
            >
              Tutor
            </div>

            <Input
              type="search"
              value={tutor}
              placeholder="Select a Tutor"
              onChange={Update.TutorSearch}
              onFocus={Update.TutorFocus}
              className="text-xs font-medium text-slate-600 placeholder:text-xs placeholder:font-normal placeholder:text-slate-500"
            />

            {tutor && (
              <InputSelectorForModal
                isOpen={isTFocused}
                searchValue={tutor}
                onChange={Update.TutorSelect}
                apiRoute="/tutor/search"
              />
            )}

            {isTutorNotFound && (
              <div className="mt-1.5 text-xs font-medium text-destructive">
                No tutor found!
              </div>
            )}
          </div>

          <div className="pb-5">
            <div
              className={`pb-1 text-sm font-medium  ${
                isStuNotFound ? "text-destructive" : "text-slate-800"
              }`}
            >
              Student
            </div>

            <Input
              type="search"
              value={student}
              placeholder="Select a Student"
              onChange={Update.StudentSearch}
              onFocus={Update.StudentFocus}
              className="text-xs font-medium text-slate-600 placeholder:text-xs placeholder:font-normal placeholder:text-slate-500"
            />

            {student && (
              <InputSelectorForModal
                isOpen={isSFocused}
                searchValue={student}
                onChange={Update.StudentSelect}
                apiRoute="/student/search"
              />
            )}

            {isStuNotFound && (
              <div className="mt-1.5 text-xs font-medium text-destructive">
                No student found!
              </div>
            )}
          </div>

          {seletedStuId && (
            <div>
              <div className="pb-1 text-sm font-medium text-slate-800">
                Select child
              </div>

              <Select
                onValueChange={Update.ChildSelect}
                defaultValue={selectedChild}
              >
                <SelectTrigger className="mb-5 w-full bg-white">
                  <div className="text-xs font-medium text-slate-600">
                    {(Children &&
                      Children.find((c) => c.id === selectedChild)?.name) ||
                      "Select a child"}
                  </div>
                </SelectTrigger>

                <SelectContent>
                  <SelectGroup>
                    {Children && Children.length === 0 && (
                      <div className="p-1 text-[10px] font-medium text-blue-600">
                        No children found. Please add a child first!
                      </div>
                    )}
                    {Children &&
                      Children.map((child: any) => {
                        return (
                          <SelectItem key={child.id} value={child.id}>
                            {child?.name}
                          </SelectItem>
                        );
                      })}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="">
            <div className="mb-1 flex flex-row items-center space-x-2">
              <div>
                <Checkbox
                  checked={isCheckedSDate}
                  onCheckedChange={(e: any) => setIsCheckedSDate(e)}
                  className={
                    "h-4 w-4 rounded border border-slate-300 bg-white shadow-none data-[state=checked]:bg-slate-800"
                  }
                />
              </div>

              <div className="-mt-1 text-sm font-medium text-slate-800">
                Start from
              </div>
            </div>

            <div style={{ opacity: isCheckedSDate ? 1 : 0.3 }}>
              <DropdownMenu
                open={isPopoverOpenSDate}
                onOpenChange={setIsPopoverOpenSDate}
              >
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start rounded-lg border border-slate-300 bg-white px-2.5 text-left font-normal",
                      !sDate && "text-muted-foreground",
                    )}
                    onClick={() => setIsPopoverOpenSDate(true)}
                  >
                    <FaRegCalendarAlt size={16} className="text-slate-600" />

                    {sDate ? (
                      <div className="ml-1.5 text-xs font-medium text-slate-600">
                        {moment(sDate).format("MMMM D, YYYY")}
                      </div>
                    ) : (
                      <div className="ml-1.5 text-xs font-medium text-slate-600">
                        Pick a date
                      </div>
                    )}
                  </Button>
                </DropdownMenuTrigger>

                {/* Popover Content */}
                <DropdownMenuContent
                  className="w-64"
                  //onPointerDownOutside={(e) => e.preventDefault()} // Prevent popover from closing on outside clicks
                >
                  <Calendar
                    mode="single"
                    selected={sDate}
                    onSelect={(selectedDate) => {
                      //console.log("✅ Selected date triggered:", selectedDate); // Debugging log
                      if (selectedDate) {
                        setSDate(selectedDate); // Update date state
                        if (selectedDate >= eDate) setEDate(selectedDate);
                        setIsPopoverOpenSDate(false); // Close popover after selection
                      }
                    }}
                    initialFocus
                  />
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="mt-5">
            <div className="mb-1 flex flex-row items-center space-x-2">
              <div>
                <Checkbox
                  checked={isCheckedEDate}
                  onCheckedChange={(e: any) => setIsCheckedEDate(e)}
                  className={
                    "h-4 w-4 rounded border border-slate-300 bg-white shadow-none data-[state=checked]:bg-slate-800"
                  }
                />
              </div>

              <div className="-mt-1 text-sm font-medium text-slate-800">
                End at
              </div>
            </div>

            <div style={{ opacity: isCheckedEDate ? 1 : 0.3 }}>
              <DropdownMenu
                open={isPopoverOpenEDate}
                onOpenChange={setIsPopoverOpenEDate}
              >
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start rounded-lg border border-slate-300 bg-white px-2.5 text-left font-normal",
                      !eDate && "text-muted-foreground",
                    )}
                    onClick={() => setIsPopoverOpenEDate(true)}
                  >
                    <FaRegCalendarAlt size={16} className="text-gary-600" />

                    {eDate ? (
                      <div className="ml-1.5 text-xs font-medium text-slate-600">
                        {moment(eDate).format("MMMM D, YYYY")}
                      </div>
                    ) : (
                      <div className="ml-1.5 text-xs font-medium text-slate-600">
                        Pick a date
                      </div>
                    )}
                  </Button>
                </DropdownMenuTrigger>

                {/* Popover Content */}
                <DropdownMenuContent
                  className="w-64"
                  //onPointerDownOutside={(e) => e.preventDefault()} // Prevent popover from closing on outside clicks
                >
                  <Calendar
                    mode="single"
                    selected={eDate}
                    onSelect={(selectedDate) => {
                      //console.log("✅ Selected date triggered:", selectedDate); // Debugging log
                      if (selectedDate) {
                        setEDate(selectedDate); // Update date state
                        setIsPopoverOpenEDate(false); // Close popover after selection
                      }
                    }}
                    initialFocus
                  />
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        <SheetFooter>
          <SheetClose asChild>
            <div className="fixed bottom-1 flex  flex-row items-center justify-between space-x-4 py-3">
              <Button
                type="reset"
                onClick={Action.Reset}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                onClick={Action.generateQuery}
                className="border border-cyan-600 bg-cyan-600 hover:bg-white hover:text-cyan-600"
              >
                Apply Filter
              </Button>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
