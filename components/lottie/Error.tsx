import React, { useRef, useEffect } from 'react'
import <PERSON><PERSON> from 'lottie-web'
import animationData from 'assets/lottie/error.json'

export default function Error(props: any) {
    const animation = useRef<any>(undefined)

    useEffect(() => {
        Lottie.loadAnimation({
            container: animation.current,
            renderer: "svg",
            loop: true,
            autoplay: true,
            animationData: animationData,
        });
        return () => Lottie.stop()
    }, []);

    return (
        <div style={{ display: 'flex', height: '90vh', width: '100%', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
            <div>
                <div ref={animation} style={{ height: 400, width: 400 }}></div>
                <h4 style={{ textAlign: 'center', marginTop: -20 }}>
                    {props.message}
                </h4>
            </div>
        </div>
    )
}