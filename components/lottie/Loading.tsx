import React, { useRef, useEffect } from "react";
import <PERSON><PERSON> from "lottie-web";
import animationData from "assets/lottie/loading.json";

export default function Loading() {
  const animation = useRef<any>(undefined);

  useEffect(() => {
    Lottie.loadAnimation({
      container: animation.current,
      renderer: "svg",
      loop: true,
      autoplay: true,
      animationData: animationData,
    });
    return () => Lottie.stop();
  }, []);

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="flex h-96 w-96 items-center justify-center">
        <div ref={animation} className="h-80 w-80"></div>
      </div>
    </div>
  );
}
