import React, { useEffect, useRef } from "react";
import <PERSON><PERSON> from "lottie-web";

type Props = {
  icon: any;
  height: any;
  width: any;
};

export default function LottieAnimation({ height, width, icon }: Props) {
  const animation = useRef<any>(undefined);

  useEffect(() => {
    const animationInstance = Lottie.loadAnimation({
      container: animation.current,
      renderer: "svg",
      loop: true,
      autoplay: true,
      animationData: icon,
    });

    return () => animationInstance.destroy();
  }, [icon]);

  return (
    <div className="flex items-start justify-center">
      <div style={{ height, width }}>
        <div ref={animation}></div>
      </div>
    </div>
  );
}
