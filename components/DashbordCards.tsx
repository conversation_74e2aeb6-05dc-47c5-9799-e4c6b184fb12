import React, { useState } from "react";
import useS<PERSON> from "swr";
import DashboardCard from "./cards/DashboardCard";
import { fetcher } from "fetchers/fetcher";
import { tDashboard } from "types/dashboard";
import { MdLeaderboard } from "react-icons/md";
import { GrForwardTen } from "react-icons/gr";
import { MdOutlineTransferWithinAStation, MdPlayLesson } from "react-icons/md";
import { BsGraphUpArrow } from "react-icons/bs";
import styles from "styles/Home.module.css";
import { FiPhoneCall } from "react-icons/fi";
import { TbRosetteNumber5 } from "react-icons/tb";
import { HiUserGroup } from "react-icons/hi";
import { FaChalkboardTeacher, FaUserAltSlash } from "react-icons/fa";
import { GiMusicalNotes } from "react-icons/gi";

type Props = {};

export default function DashbordCards({}: Props) {
  const [compare, setCompare] = useState<number>(7);
  const {
    data: Dashboard,
    error: DashboardError,
    isLoading,
    mutate,
  } = useSWR<tDashboard>(
    `/dashboard?date=${new Date().toDateString()}&compare=${compare}`,
    fetcher,
  );

  return (
    <>
      <div className={styles.compareContainer}>
        <BsGraphUpArrow color="#585757" size={14} />
        <select
          value={compare}
          onChange={(e: any) => setCompare(e.target.value)}
          className={styles.compare}
        >
          <option value={1}>Today vs Previous Day</option>
          <option value={7}>This Week vs Previous Week</option>
          <option value={30}>This Month vs Previous Month</option>
        </select>
      </div>

      <div className={styles.cardContainer}>
        <DashboardCard
          data={Dashboard?.leadsNow}
          statistic={Dashboard?.leadsStat}
          icon={<MdLeaderboard className={styles.Icon} />}
          rangeTitle="Leads"
          range={compare}
          isLoading={isLoading}
          isOnClick
          routeLink={`incoming-leads`}
          notSendQuery
        />
        <DashboardCard
          data={Dashboard?.callsNow}
          statistic={Dashboard?.callsStat}
          icon={<FiPhoneCall className={styles.Icon} />}
          range={compare}
          rangeTitle="Greens"
          isLoading={isLoading}
          isOnClick
          routeLink={`green-students`}
          notSendQuery
        />
        <DashboardCard
          data={Dashboard?.conversionsNow}
          statistic={Dashboard?.conversionsStat}
          icon={<MdOutlineTransferWithinAStation className={styles.Icon} />}
          range={compare}
          rangeTitle="Conversions"
          isLoading={isLoading}
          isOnClick
          routeLink={`conversions`}
        />
        <DashboardCard
          data={Dashboard?.fourNow}
          statistic={Dashboard?.fourStat}
          icon={<TbRosetteNumber5 className={styles.Icon} />}
          range={compare}
          rangeTitle="4th Lesson"
          isLoading={isLoading}
          isOnClick
          routeLink={`fourth-lesson`}
        />
        <DashboardCard
          data={Dashboard?.nineNow}
          statistic={Dashboard?.nineStat}
          icon={<GrForwardTen className={styles.Icon} />}
          range={compare}
          rangeTitle="10th Lesson"
          isLoading={isLoading}
          isOnClick
          routeLink={`tenth-lesson`}
        />
        <DashboardCard
          data={Dashboard?.gaveUpsNow}
          statistic={Dashboard?.gaveUpsStat}
          icon={<FaUserAltSlash className={styles.Icon} />}
          range={compare}
          rangeTitle="Gave Ups"
          isLoading={isLoading}
          isOnClick
          routeLink={`gave-up`}
        />
        <DashboardCard
          data={Dashboard?.students}
          icon={<HiUserGroup className={styles.Icon} />}
          title="Total Students"
          isLoading={isLoading}
        />

        <DashboardCard
          data={Dashboard?.tutors}
          icon={<FaChalkboardTeacher className={styles.Icon} />}
          title="Total Tutors"
          isLoading={isLoading}
        />
        <DashboardCard
          data={Dashboard?.lessons}
          icon={<MdPlayLesson className={styles.Icon} />}
          title="Total Lessons"
          isLoading={isLoading}
        />
        <DashboardCard
          data={Dashboard?.lessonToday}
          icon={<GiMusicalNotes className={styles.Icon} />}
          title="Lessons Today"
          isLoading={isLoading}
        />
      </div>
    </>
  );
}
