import React from "react";
import { PgObject } from "types/dashboard";
import { AiOutlineArrowLeft, AiOutlineArrowRight } from "react-icons/ai";

type Props = {
  handler: any;
  page: PgObject;
  itemName?: string;
};

export default function PageList({ handler, page, itemName }: Props) {
  const handleNext = () => {
    handler(page.currentPage + 1);
  };

  const handlePrev = () => {
    if (page.currentPage > 1) handler(page.currentPage - 1);
  };

  if (page)
    return (
      <div className="flex w-full list-none flex-row items-center justify-between py-2.5">
        <div className="flex flex-row items-center justify-start">
          <div className="flex h-9 list-none flex-col items-center justify-center rounded border border-slate-200 bg-white px-3  text-sm font-normal text-slate-600">
            Page: {page.currentPage} / {page.totalPages}
          </div>

          <div className="ml-1.5 flex h-9 list-none flex-col items-center justify-center rounded border border-slate-200 bg-white px-3 text-sm font-normal text-slate-600">
            {itemName ? itemName : "Lessons"}: {page.itemsPerPage} /{" "}
            {page.totalItems}
          </div>
        </div>

        <div className="flex flex-row items-center justify-end space-x-1.5">
          <div
            className="flex h-8 w-8 cursor-pointer flex-row items-center justify-center rounded border border-slate-200 bg-white text-xs text-slate-600 hover:bg-slate-950  hover:text-white"
            onClick={handlePrev}
          >
            <AiOutlineArrowLeft />
          </div>
          <div className="flex h-8 w-8 select-none flex-row items-center justify-center rounded border border-slate-200 bg-white text-xs text-slate-600">
            {page.currentPage}
          </div>
          <div
            className="flex h-8 w-8 cursor-pointer flex-row items-center justify-center rounded border border-slate-200 bg-white text-xs text-slate-600 hover:bg-slate-950  hover:text-white"
            onClick={handleNext}
          >
            <AiOutlineArrowRight />
          </div>
        </div>
      </div>
    );
  else return null;
}
