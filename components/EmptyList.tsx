"use client";

import React from "react";
import Image, { StaticImageData } from "next/image";
import { Button } from "@components/ui/button";
import { FaRotate } from "react-icons/fa6";

import MicDrop from "@assets/brokenViolin02.jpg";

type Props = {
  title: string;
  des?: string;
  image?: StaticImageData | string; // Accept both imported images and string URLs
  imageAlt?: string;
  imageHeight?: number;
  imageWidth?: number;
  // trigger?: () => void;
};

export default function EmptyList({
  title,
  des,
  image,
  imageAlt,
  imageHeight,
  imageWidth,
}: Props) {
  // Use the provided image or fall back to the default MicDrop image
  const displayImage = image || MicDrop;

  return (
    <div className="mt-10 flex w-full flex-col items-center justify-center">
      <div>
        <Image
          src={displayImage}
          alt={imageAlt || "MuseCool"}
          height={imageHeight || 200}
          width={imageWidth || 350}
          className="rounded-md"
        />
      </div>

      <div className="mb-4 text-sm font-semibold text-slate-800 md:mt-10 md:text-base">
        {title}
      </div>

      {des && (
        <p className="mb-10 text-center text-xs font-medium text-slate-500 md:text-sm lg:text-base">
          {des}
        </p>
      )}

      {/*
      {trigger && (
        <Button
          onClick={trigger}
          className="h-10 w-32 rounded-2xl bg-cyan-600 text-xs font-semibold text-white hover:bg-cyan-800 md:text-sm"
        >
          <FaRotate size={16} className="mr-2.5" />
          Reset
        </Button>
      )}
      */}
    </div>
  );
}
