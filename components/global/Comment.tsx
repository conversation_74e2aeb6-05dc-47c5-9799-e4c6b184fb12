import React, { useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { getSession } from "next-auth/react";
import { Dots } from "react-activity";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import { Textarea } from "@components/ui/textarea";
import { Button } from "@components/ui/button";
import { tCommentL } from "types/comment";
import moment from "moment";
import Avatar from "react-avatar";

type Props = {
  comment: tCommentL;
  mutate: () => void;
};

export default function Comment({ comment, mutate }: Props) {
  return (
    <div className="my-4  flex h-fit w-full flex-row">
      <Avatar name={comment.adminName} round={true} size="40" />
      <div className="ml-2">
        <div className="text-xs font-semibold text-slate-600">
          {comment.adminName}
        </div>

        {/* //Body Section  */}
        <div className="mb-1 mt-1 h-fit w-44 rounded-md border border-slate-200 bg-white px-2 py-2 ">
          <div className="max-w-[185px]  break-words  text-[10px] font-normal text-slate-600">
            {comment.body ? comment.body : "........... "}
          </div>
        </div>

        {comment.isOwner ? (
          <div className=" flex w-full flex-row items-center justify-between">
            <div className=" flex flex-row items-center">
              <EditComment comment={comment} mutate={mutate} />
              <DeleteComment comment={comment} mutate={mutate} />
            </div>
            <div className=" flex flex-col items-center justify-center">
              <div className="text-[8px] font-light text-slate-600">
                {moment(comment?.dateTime)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .format("DD MMM YYYY")}
              </div>
              <div className="text-[8px] font-light text-slate-600">
                {moment(comment.dateTime)
                  .add(new Date().getTimezoneOffset() * -1, "minute")
                  .format("h:mm A")}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex w-full justify-end text-[8px] font-light text-slate-600">
            {moment(comment?.dateTime)
              .add(new Date().getTimezoneOffset() * -1, "minute")
              .format("DD MMM YYYY")}
            ,{" "}
            {moment(comment.dateTime)
              .add(new Date().getTimezoneOffset() * -1, "minute")
              .format("h:mm A")}
          </div>
        )}
      </div>
    </div>
  );
}

type tAddCmntProps = {
  id: string;
  mutate: () => void;
};

export function AddComment({ id, mutate }: tAddCmntProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  var resolver = yup.object().shape({
    // body: yup.string().required("Comment is required!"),
  });

  const defaultValues = {
    body: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    setIsError(false);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `comment/${id}`,
        { ...data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        form.reset(defaultValues);
        toast.success("Comment successfully added!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      setIsError(true);
      toast.error("Failed to add a comment!");
    }
  };
  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            control={form.control}
            name="body"
            render={({ field }) => (
              <FormItem>
                {/* <FormLabel>Description</FormLabel> */}
                <FormControl>
                  <Textarea
                    placeholder="Comment here..."
                    className="min-h-[80px] w-full bg-white text-xs font-normal text-slate-700"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* //Button Section... */}
          <Button
            className={`mt-3  w-full hover:border hover:bg-white  ${
              isError
                ? "bg-red-500 text-red-50 hover:border-red-600 hover:text-red-600"
                : "bg-blue-500 text-blue-50 hover:border-blue-500  hover:text-blue-500"
            }`}
          >
            {isLoading ? <Dots /> : "Comment"}
          </Button>
        </form>
      </Form>
    </div>
  );
}

function EditComment({ comment, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    body: yup.string().required("Comment is required!"),
  });

  const defaultValues = {
    body: comment.body,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/comment/${comment.id}`,
        { ...data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        setIsLoading(false);
        mutate();
        setIsOpen(false);
        toast.success("Comment successfully updated!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to update comment!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={reset}>
      <DialogTrigger onClick={onOpen}>
        <div className="cursor-pointer text-[10px] font-medium text-blue-500 text-opacity-90">
          Edit
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Comment</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="body"
              render={({ field }) => (
                <FormItem>
                  {/* <FormLabel>Comment</FormLabel> */}
                  <FormControl>
                    <Textarea
                      placeholder="Enter comment"
                      className="min-h-[96px] text-xs font-normal text-slate-700"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-6 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function DeleteComment({ comment, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onDelete = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      let res = await api.delete(`/comment/${comment.id}`, {
        headers: {
          Authorization: "Bearer " + token,
        },
      });

      if (res.status === 200) {
        setIsLoading(false);
        mutate();
        toast.success("Comment has been successfully Deleted!");
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to Delete Comment!");
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <div className="ml-2 cursor-pointer bg-opacity-90 text-[10px] font-medium text-red-500">
          Delete
        </div>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action is irreversible and will permanently delete the comment
            from our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
