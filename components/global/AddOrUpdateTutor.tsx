import { getSession } from "next-auth/react";

import React, { useEffect, useState } from "react";
import api from "fetchers/BaseUrl";
import { useForm } from "react-hook-form";
import useSWR from "swr";
import { yupResolver } from "@hookform/resolvers/yup";
import { Dots } from "react-activity";
import * as yup from "yup";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import { Input } from "@components/ui/input";
import { Button } from "@components/ui/button";

import InputSelectorForModal from "@components/modals/InputSelectorForModal";

import { BsPersonPlusFill } from "react-icons/bs";
import { FaUserEdit } from "react-icons/fa";
import { tUserS } from "types/tutor";

type AddLFTProps = {
  mutate?: () => void;
  purpose: string;
  header: string;
  icon?: boolean;
};

export default function AddOrUpdateTutor({
  mutate,
  purpose,
  icon,
  header,
}: AddLFTProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  // State for tutor
  const [tutor, setTutor] = useState<string>("All");

  const [seletedTutorId, setSeletedTutorId] = useState<string>("");
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [isTutorNotFound, setIsTutorNotFound] = useState<boolean>(false);

  var resolver = yup.object().shape({
    childId: yup.string().required("Child field is required!"),
  });

  const defaultValues = {
    childId: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    if (!seletedTutorId) {
      setIsTutorNotFound(true);
      return;
    }

    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `API`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        if (mutate) mutate();

        setIsLoading(false);
        setIsOpen(false);
        toast.success(``);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("");
    }
  };

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);

      setIsTutorNotFound(false);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const Update = {
    TutorSearch: (e: any) => {
      setTutor(e.target.value);
    },
    TutorSelect: (tutor: tUserS) => {
      setIsFocused(false);

      setSeletedTutorId(tutor.id);

      setTutor(tutor.fullName);
    },
    TutorFocus: () => {
      setIsFocused(true);
      if (tutor === "All") setTutor("");
    },
  };

  useEffect(() => {
    if (!tutor) {
      setSeletedTutorId("");
    }
  }, [tutor]);

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <div className="flex flex-row items-center ">
          {icon && (
            <>
              {purpose.toLowerCase().includes("add") && (
                <BsPersonPlusFill size={16} className="mr-1.5 text-gray-500" />
              )}

              {(purpose.toLowerCase().includes("edit") ||
                purpose.toLowerCase().includes("update")) && (
                <FaUserEdit size={16} className="mr-1 text-gray-500" />
              )}
            </>
          )}

          <span>{header}</span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{header}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <div className="my-5">
                <div
                  className={`pb-1 text-sm font-medium  ${
                    isTutorNotFound ? "text-destructive" : "text-slate-800"
                  }`}
                >
                  Tutor
                </div>

                <Input
                  type="search"
                  value={tutor}
                  placeholder="Select a Tutor"
                  onChange={Update.TutorSearch}
                  onFocus={Update.TutorFocus}
                  className="text-xs font-medium text-slate-600 placeholder:text-xs placeholder:font-normal placeholder:text-slate-500"
                />

                {tutor && (
                  <InputSelectorForModal
                    isOpen={isFocused}
                    searchValue={tutor}
                    onChange={Update.TutorSelect}
                    apiRoute="/tutor/search"
                  />
                )}

                {isTutorNotFound && (
                  <div className="mt-1.5 text-xs font-medium text-destructive">
                    No tutor found!
                  </div>
                )}
              </div>
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-cyan-600 bg-cyan-600 hover:bg-white hover:text-cyan-600"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
