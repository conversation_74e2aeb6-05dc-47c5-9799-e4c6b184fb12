import React, { useEffect, useState } from "react";

import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import { Button } from "@components/ui/button";
import { Dots } from "react-activity";

import { MdEditDocument } from "react-icons/md";
import { tStudentD, tStudentL } from "types/student";
import useSWR from "swr";
import { tChildF } from "types/dashboard";
import { fetcher } from "@fetchers/fetcher";
import { Checkbox } from "@components/ui/checkbox";
import { Textarea } from "@components/ui/textarea";
import { getSession } from "next-auth/react";

type props = {
  student: tStudentL | tStudentD;
  mutate: () => void;
  title: string;
  icon?: boolean;
};
export default function UpdateStatus({ student, mutate, title, icon }: props) {
  const {
    data: Children,
    error,
    mutate: ChildMutate,
  } = useSWR<Array<tChildF>>(
    student?.id ? `/student/${student.id}/children` : null,
    fetcher,
  );

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  //   const [status, setStatus] = useState<tSelector>();
  const [status, setStatus] = useState<string>();
  const [cid, setCid] = useState<string>("");
  const [isChangeP1L, setIsChangeP1L] = useState<boolean>(false);
  const [whyGiveUp, setWhyGiveUp] = useState<Array<string>>([]);
  const [gupComment, setGupComment] = useState<string>("");
  const [isUnsubscribed, setIsUnsubscribed] = useState<boolean>(false);
  const [p1LError, setP1LError] = useState<boolean>(false);

  const [childNotFound, setChildNotFound] = useState<boolean>(false);

  const defaultValues = () => {
    if (student.status) {
      setStatus(student.status);
    }
    if (student.gupReasons) setWhyGiveUp(student.gupReasons.split(","));
    if (student.gupComment) setGupComment(student.gupComment);
    if (student.isUnsubscribed) setIsUnsubscribed(student.isUnsubscribed);

    setIsChangeP1L(false);
    setP1LError(false);
  };

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      defaultValues();
    },
    onOpen: () => {
      setIsOpen(true);
    },
  };

  const Update = {
    GiveUpComment: (e: any) => {
      setGupComment(e.target.value);
    },
    Status: (e: any) => {
      setStatus(e);
    },
  };

  const Action = {
    UpdateWhyGaveUp: (value: string) => {
      if (whyGiveUp.includes(value)) {
        setWhyGiveUp(whyGiveUp.filter((option: any) => option !== value));
      } else {
        setWhyGiveUp([...whyGiveUp, value]);
      }
    },

    Submit: async () => {
      // console.log(status?.value, whyGiveUp, gupComment, isUnsubscribed,);

      if (!status) return;

      if (
        status &&
        (status === "Pending 1st lesson" || status === "Looking for tutor") &&
        !cid
      ) {
        setChildNotFound(true);
        return;
      }

      if (
        status &&
        status !== "Pending 1st lesson" &&
        student.status === "Pending 1st lesson" &&
        !isChangeP1L
      ) {
        setP1LError(true);
        return;
      }

      setIsLoading(true);

      try {
        const Session = await getSession();
        const token = Session?.token;
        const response = await api.put(
          `/student/status/${student.id}`,
          {
            status: status,
            reasons: whyGiveUp,
            comment: gupComment,
            unsubscribed: isUnsubscribed,
            cid: cid ? cid : undefined,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        );
        if (response.status === 200) {
          mutate();
          ChildMutate();

          toast.success(
            `${
              (student as tStudentL).fullname || (student as tStudentD).fullName
            }'s status has been updated successfully!`,
          );
          setIsLoading(false);
          setIsOpen(false);
        } else {
          setIsLoading(false);
          toast.error("Unexpected response from the server!");
        }
      } catch {
        setIsLoading(false);
        toast.error("Failed to update status. Please try again!");
      }
    },
  };

  useEffect(() => {
    if (cid) setChildNotFound(false);
  }, [cid]);

  //   const sts: Array<tSelector> = [
  //     { value: "Regular", label: "Regular" },
  //     { value: "New", label: "New" },
  //     { value: "Looking for tutor", label: "Looking for tutor" },
  //     { value: "Green", label: "Green" },
  //     { value: "Pending 1st lesson", label: "Pending 1st lesson" },
  //     { value: "Group course only", label: "Group course only" },
  //     { value: "Call much later", label: "Call much later" },
  //     { value: "Call later", label: "Call later" },
  //     { value: "Not active", label: "Not active" },
  //     { value: "Old new", label: "Old new" },
  //     { value: "Old green", label: "Old green" },
  //     { value: "Gave up", label: "Gave up" },
  //     { value: "Get to restart", label: "Get to restart" },
  //     { value: "Bus event", label: "Bus event" },
  //     { value: "Multiple Children", label: "Multiple Children" },
  //   ];

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="flex flex-row items-center">
          {icon && <MdEditDocument size={16} color="gray" className="mr-1" />}
          {title}
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Status</DialogTitle>
        </DialogHeader>
        <div className="">
          <div className=" text-sm font-medium text-gray-800">Status</div>

          <div className="mt-1">
            <Select onValueChange={Update.Status} defaultValue={status}>
              <SelectTrigger className="mb-3 w-full rounded-sm border border-slate-300 bg-white">
                <div className=" capitalize">
                  {status ? status : "Select Status"}
                </div>
              </SelectTrigger>
              <SelectContent className="hide-scrollbar max-h-48 overflow-y-scroll text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value="Regular">Regular</SelectItem>
                  <SelectItem value="New">New</SelectItem>
                  <SelectItem value="Looking for tutor">
                    Looking for tutor
                  </SelectItem>
                  <SelectItem value="Green">Green</SelectItem>
                  <SelectItem value="Pending 1st lesson">
                    Pending 1st lesson
                  </SelectItem>
                  <SelectItem value="Group course only">
                    Group course only
                  </SelectItem>

                  <SelectItem value="Call much later">
                    Call much later
                  </SelectItem>
                  <SelectItem value="Call later">Call later</SelectItem>
                  <SelectItem value="Not active">Not active</SelectItem>
                  <SelectItem value="Old new">Old new</SelectItem>
                  <SelectItem value="Old green">Old green</SelectItem>
                  <SelectItem value="Gave up">Give Up</SelectItem>
                  <SelectItem value="Get to restart">Get to restart</SelectItem>
                  <SelectItem value="Bus event">Bus event</SelectItem>
                  <SelectItem value="Multiple Children">
                    Multiple Children
                  </SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          {status &&
            (status === "Pending 1st lesson" ||
              status === "Looking for tutor") && (
              <>
                <div
                  className={`mt-2 pb-1 text-sm font-medium  ${
                    childNotFound ? "text-destructive" : "text-gray-800"
                  }`}
                >
                  Select A Child
                </div>
                <div className="mb-1">
                  <Select
                    onValueChange={(e: any) => setCid(e)}
                    defaultValue={cid}
                  >
                    <SelectTrigger className="w-full  bg-white">
                      <div className="text-xs font-normal text-gray-700">
                        {cid
                          ? Children &&
                            Children?.find((temp: tChildF) => temp.id === cid)
                              ?.name
                          : "Select Child"}
                      </div>
                    </SelectTrigger>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        {Children && Children?.length === 0 && (
                          <div className="p-1 text-[10px] font-medium text-blue-600">
                            No children found. Please add a child first!
                          </div>
                        )}
                        {Children?.map((child: any) => {
                          return (
                            <SelectItem key={child.id} value={child.id}>
                              {child?.name}
                            </SelectItem>
                          );
                        })}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  {childNotFound && (
                    <div className="mt-1.5 text-xs font-medium text-destructive">
                      Child is required!
                    </div>
                  )}
                </div>
              </>
            )}

          {status &&
            status !== "Pending 1st lesson" &&
            student.status === "Pending 1st lesson" && (
              <div className="mb-1 mt-2 flex items-center space-x-1.5">
                <Checkbox
                  checked={isChangeP1L}
                  onCheckedChange={() => setIsChangeP1L(!isChangeP1L)}
                />
                <label className="text-xs font-semibold text-slate-700">
                  Confirm changing status (PFL record will be removed).
                </label>
              </div>
            )}

          {status &&
            status !== "Pending 1st lesson" &&
            student.status === "Pending 1st lesson" &&
            !isChangeP1L &&
            p1LError && (
              <div className="mt-1 flex flex-row justify-start text-justify text-[10px] font-medium text-red-600">
                Warning: Please confirm before updating!
              </div>
            )}

          {status && status === "Gave up" && (
            <>
              <div className="mb-1 mt-2.5 text-xs font-semibold text-slate-700">
                Tutor
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Tutor cannot keep teaching due to moving address",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Tutor cannot keep teaching due to moving address",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Tutor cannot keep teaching due to moving address
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Tutor want to move on from teaching",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Tutor want to move on from teaching",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Tutor want to move on from teaching
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("tutor took the student private")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("tutor took the student private")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Tutor took the student private
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Timetable issues with tutor")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Timetable issues with tutor")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Timetable issues with tutor
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Tutor kept canceling")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Tutor kept canceling")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Tutor kept canceling
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Not happy with online learning")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Not happy with online learning")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Not happy with online learning
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Tutor not reliable")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Tutor not reliable")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Tutor not reliable
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Child didn't progress")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Child didn't progress")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Child didn't progress
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Didn't like 1st lesson")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Didn't like 1st lesson")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Didn't like 1st lesson
                </label>
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Tutor doesn't want to work with them anymore",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Tutor doesn't want to work with them anymore",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Tutor doesn't want to work with them anymore
                </label>
              </div>
              {/* //Office */}
              <div className=" mb-1 mt-4 text-xs font-semibold text-slate-700">
                Office
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Not able to find tutor for home lessons",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Not able to find tutor for home lessons",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Not able to find tutor for home lessons
                </label>
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Not happy with customer service",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Not happy with customer service")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Not happy with customer service
                </label>
              </div>
              {/* //Money */}
              <div className="mb-1 mt-4  text-xs font-semibold text-slate-700">
                Money
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Financial difficulties")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Financial difficulties")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Financial difficulties
                </label>
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Balance expired")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Balance expired")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Balance expired
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Too expensive compared to competition",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Too expensive compared to competition",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Too expensive compared to competition
                </label>
              </div>

              {/* //Personal issue*/}
              <div className="mb-1 mt-4  text-xs font-semibold text-slate-700">
                Personal issue
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Musical goals achieved; no need for lessons anymore",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Musical goals achieved; no need for lessons anymore",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Musical goals achieved; no need for lessons anymore
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Want to have a break due to school exams",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Want to have a break due to school exams",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Want to have a break due to school exams
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Lost motivation")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Lost motivation")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Lost motivation
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Moved to another country")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Moved to another country")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Moved to another country
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Health issues")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Health issues")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Health issues
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Child too young - the concentration level is low",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Child too young - the concentration level is low",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Child too young - the concentration level is low
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Busy with work")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Busy with work")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Busy with work
                </label>
              </div>
              {/* //Competition */}
              <div className="mb-1 mt-4  text-xs font-semibold text-slate-700">
                Competition
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Started lessons at their school",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Started lessons at their school")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Started lessons at their school
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Found another tutor")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Found another tutor")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Found another tutor
                </label>
              </div>

              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes(
                    "Competing alternative activities - child overwhelmed",
                  )}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp(
                      "Competing alternative activities - child overwhelmed",
                    )
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Competing alternative activities - child overwhelmed
                </label>
              </div>

              {/* //Other */}
              <div className="mb-1 mt-4  text-xs font-semibold text-slate-700">
                Other
              </div>
              <div className="mb-1 flex items-center space-x-1.5">
                <Checkbox
                  checked={whyGiveUp.includes("Didn't answer messages anymore")}
                  onCheckedChange={() =>
                    Action.UpdateWhyGaveUp("Didn't answer messages anymore")
                  }
                />
                <label className="text-xs font-normal text-slate-700">
                  Didn't answer messages anymore
                </label>
              </div>
              {/* //Comment */}
              <div className="mb-1 mt-4  text-xs font-semibold text-slate-700">
                Comment
              </div>
              <Textarea
                placeholder="Comment here"
                value={gupComment}
                onChange={Update.GiveUpComment}
              />
              {/* //Unsubscribe From Email */}
              <div className="mb-1 mt-4  flex items-center space-x-1.5">
                <Checkbox
                  checked={isUnsubscribed}
                  onCheckedChange={() => setIsUnsubscribed(!isUnsubscribed)}
                />
                <label className="text-xs font-semibold text-slate-700">
                  Unsubscribe From Email
                </label>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          {/* //Button Section... */}
          <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
            <Button
              type="reset"
              onClick={() => defaultValues()}
              className="border border-black bg-white text-black hover:bg-black hover:text-white "
            >
              Reset
            </Button>
            <Button
              type="submit"
              onClick={() => Action.Submit()}
              className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
            >
              {isLoading ? <Dots /> : "Submit"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
