import { getSession } from "next-auth/react";

import React, { useEffect, useState } from "react";
import api from "fetchers/BaseUrl";
import { useForm } from "react-hook-form";
import useSWR from "swr";
import { yupResolver } from "@hookform/resolvers/yup";
import { Dots } from "react-activity";
import * as yup from "yup";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Input } from "@components/ui/input";
import { Button } from "@components/ui/button";
import { tStudentL } from "types/student";
import InputSelectorForModal from "@components/modals/InputSelectorForModal";
import { tChildF } from "types/dashboard";
import { fetcher } from "@fetchers/fetcher";

import { BsPersonPlusFill } from "react-icons/bs";
import { FaUserEdit } from "react-icons/fa";

type AddLFTProps = {
  mutate?: () => void;
  purpose: string;
  header: string;
  icon?: boolean;
};

export default function AddOrUpdateStudent({
  mutate,
  purpose,
  icon,
  header,
}: AddLFTProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [isStuNotFound, setIsStuNotFound] = useState<boolean>(false);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [student, setStudent] = useState<string>("");
  const [seletedStuId, setSeletedStuId] = useState<string>("");

  const {
    data: Children,
    mutate: childMutate,
    isLoading: chilLoading,
  } = useSWR<Array<tChildF>>(
    seletedStuId ? `/student/${seletedStuId}/children` : null,
    fetcher,
  );

  var resolver = yup.object().shape({
    childId: yup.string().required("Child field is required!"),
  });

  const defaultValues = {
    childId: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    if (!seletedStuId) {
      setIsStuNotFound(true);
      return;
    }

    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `API`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        if (mutate) mutate();

        childMutate();

        setIsLoading(false);
        setIsOpen(false);
        toast.success(``);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("");
    }
  };

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);

      setIsStuNotFound(false);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const SearchHandelar = {
    SelectStu: (student: tStudentL) => {
      setStudent(student.fullname);
      setSeletedStuId(student.id);
      setIsFocused(false);
      setIsStuNotFound(false);
    },
    SearchStu: (e: any) => {
      setStudent(e.target.value);
    },
  };

  useEffect(() => {
    if (!student) {
      setSeletedStuId("");
    }
  }, [student]);

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <div className="flex flex-row items-center ">
          {icon && (
            <>
              {purpose.toLowerCase().includes("add") && (
                <BsPersonPlusFill size={16} className="mr-1.5 text-gray-500" />
              )}

              {(purpose.toLowerCase().includes("edit") ||
                purpose.toLowerCase().includes("update")) && (
                <FaUserEdit size={16} className="mr-1 text-gray-500" />
              )}
            </>
          )}

          <span>{header}</span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{header}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <div>
                <div
                  className={`pb-1 text-sm font-medium  ${
                    isStuNotFound ? "text-destructive" : "text-gray-800"
                  }`}
                >
                  Find A Student
                </div>
                <Input
                  type="search"
                  value={student}
                  placeholder="Select A Student"
                  onChange={SearchHandelar.SearchStu}
                  onFocus={() => setIsFocused(true)}
                  className="text-xs font-normal text-gray-800 placeholder:text-xs placeholder:font-medium placeholder:text-gray-700"
                />
                {student && (
                  <InputSelectorForModal
                    isOpen={isFocused}
                    searchValue={student}
                    onChange={SearchHandelar.SelectStu}
                    apiRoute="/student/search"
                  />
                )}

                {isStuNotFound && (
                  <div className="mt-1.5 text-xs font-medium text-destructive">
                    No student found!
                  </div>
                )}
              </div>

              {seletedStuId && (
                <FormField
                  control={form.control}
                  name="childId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Select A Child</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full bg-white text-xs font-medium text-gray-700">
                            {(Children &&
                              Children.find((c) => c.id === field.value)
                                ?.name) ||
                              "Select A Child"}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectGroup>
                            {Children && Children.length === 0 && (
                              <div className="p-1 text-[10px] font-medium text-blue-600">
                                No children found. Please add a child first!
                              </div>
                            )}
                            {Children &&
                              Children.map((child: any) => {
                                return (
                                  <SelectItem key={child.id} value={child.id}>
                                    {child?.name}
                                  </SelectItem>
                                );
                              })}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-cyan-600 bg-cyan-600 hover:bg-white hover:text-cyan-600"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
