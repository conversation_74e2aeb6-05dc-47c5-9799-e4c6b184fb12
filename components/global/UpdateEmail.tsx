import React, { useState } from "react";
import { MdEmail } from "react-icons/md";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { Dots } from "react-activity";
import { Update } from "@fetchers/updater";

import { Input } from "@components/ui/input";
import { Button } from "components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import { checkEmailAddress } from "core/Checker";

type Props = {
  email?: string;
  operationName: string;
  mutate: () => void;
};

export default function UpdateEmail({ email, operationName, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    newEmail: yup
      .string()
      .required("Email is required!")
      .email(
        "Invalid Email Address. Please check if there is any extra space added.",
      )
      .test(
        "",
        "This email is already connected to an account. Please Login or reset your password.",
        async (value, values) => checkEmailAddress(value),
      ),
  });

  const defaultValues = {
    newEmail: email,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    await Update(
      "/auth/update-email",
      { ...data, email: email },
      operationName,
    );
    mutate();
    setIsLoading(false);
    setIsOpen(false);
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={reset}>
      <DialogTrigger onClick={onOpen}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <MdEmail size={16} color="#64748B" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Update Email
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-base text-slate-600">
            {email}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="newEmail"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>New Email</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter email"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
