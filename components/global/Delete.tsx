import { getSession } from "next-auth/react";
import React, { useState } from "react";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "components/ui/alert-dialog";

import { Dots } from "react-activity";
import { Button } from "@components/ui/button";
import { useRouter } from "next/navigation";

type tDelProps = {
  apiUrl: string;
  mutate: () => void;
  successMsg: string;
  erroMsg: string;
  headerTitle?: string;
  icon?: any;
  des?: string;
  useBtnUI?: boolean;
  goBackUrl?: string;
};

export default function Delete({
  mutate,
  apiUrl,
  successMsg,
  erroMsg,
  headerTitle,
  icon,
  des,
  useBtnUI,
  goBackUrl,
}: tDelProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();

  const onDelete = async () => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      let response = await api.delete(apiUrl, {
        headers: {
          Authorization: "Bearer " + token,
        },
      });

      if (response.status === 200) {
        mutate();
        setIsLoading(false);

        if (goBackUrl) {
          router.push(goBackUrl);
        }

        toast.success(successMsg);
      } else {
        setIsLoading(false);
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);

      toast.error(erroMsg);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        {useBtnUI ? (
          <Button
            type="button"
            className="border border-destructive bg-destructive text-white hover:bg-white hover:text-destructive"
          >
            {headerTitle}
          </Button>
        ) : (
          <div className="flex cursor-pointer flex-row items-center">
            {icon}
            {headerTitle && (
              <span className="ml-1 text-xs font-medium text-slate-600">
                {headerTitle}
              </span>
            )}
          </div>
        )}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            {`This action is irreversible and will permanently delete the ${des} from our server.`}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onDelete}
            className="border bg-red-600 hover:border-red-700 hover:bg-white hover:text-red-700"
          >
            {isLoading ? <Dots /> : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
