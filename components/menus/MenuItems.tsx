import React from "react";
import styles from "styles/MenuItems.module.css";

type Props = {
  title: string;
  icon: any;
  dataId?: any;
  action: any;
};

export default function MenuItems({ title, icon, dataId, action }: Props) {
  return (
    <div
      className={styles.Main}
      onClick={dataId ? () => action(dataId) : action}
    >
      <div className={styles.Icon}>{icon}</div>
      <div className={styles.Title}>{title}</div>
    </div>
  );
}
