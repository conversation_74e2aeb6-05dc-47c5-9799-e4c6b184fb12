import moment from "moment";
import React from "react";
import { tLesson } from "types/dashboard";
import MenuItems from "./MenuItems";
import { FiEdit } from "react-icons/fi";
import { IoTrashBinOutline } from "react-icons/io5";
import {
  MdOutlinePersonSearch,
  MdOutlinePersonPin,
  MdTab,
} from "react-icons/md";
import styles from "styles/Menu.module.css";

type Props = {
  lesson: tLesson | undefined;
  close: VoidFunction;
  openDeleteModal: any;
  openEditModal: any;
  openUpdateTutor: any;
  openUpdateStudent: any;
};

export default function SingleMenu({
  lesson,
  close,
  openDeleteModal,
  openEditModal,
  openUpdateTutor,
  openUpdateStudent,
}: Props) {
  return (
    <div className={styles.Main}>
      <div className={styles.Header}>{lesson?.student}</div>
      <div className={styles.rightSubHeader}>
        {moment(lesson?.dateTime).format("DD MMM YY")}
      </div>
      <MenuItems title="View" icon={<MdTab size={13} />} action={() => {}} />

      <MenuItems
        title="Edit"
        icon={<FiEdit size={13} />}
        action={openEditModal}
      />
      <MenuItems
        title="Delete"
        icon={<IoTrashBinOutline size={13} />}
        action={openDeleteModal}
      />

      <MenuItems
        title="Update Tutor"
        icon={<MdOutlinePersonSearch size={13} />}
        action={openUpdateTutor}
      />

      <MenuItems
        title="Update Student"
        icon={<MdOutlinePersonPin size={13} />}
        action={openUpdateStudent}
      />
    </div>
  );
}
