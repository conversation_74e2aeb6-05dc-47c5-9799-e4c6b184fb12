import React from "react";
import MenuItems from "./MenuItems";
import {
  MdOutlinePersonSearch,
  MdOutlinePersonPin,
  MdOutlinePayment,
} from "react-icons/md";
import { IoTrashBinOutline } from "react-icons/io5";
import styles from "styles/Menu.module.css";

type Props = {
  close: VoidFunction;
  openUpdateTutorForMS: any;
  openUpateStudentForMS: any;
  openDeleteModal: any;
  openPayment: any;
};

export default function MultiMenu({
  close,
  openUpdateTutorForMS,
  openUpateStudentForMS,
  openPayment,
  openDeleteModal,
}: Props) {
  return (
    <div className={styles.Main}>
      <div className={styles.Header}>Handle MultiMenu</div>
      <MenuItems
        title="Update Tutor"
        icon={<MdOutlinePersonSearch size={13} />}
        action={openUpdateTutorForMS}
      />

      <MenuItems
        title="Update Student"
        icon={<MdOutlinePersonPin size={13} />}
        action={openUpateStudentForMS}
      />
      <MenuItems
        title="Delete"
        icon={<IoTrashBinOutline size={13} />}
        action={openDeleteModal}
      />
      <MenuItems
        title="Update Payment"
        icon={<MdOutlinePayment size={13} />}
        action={openPayment}
      />
    </div>
  );
}
