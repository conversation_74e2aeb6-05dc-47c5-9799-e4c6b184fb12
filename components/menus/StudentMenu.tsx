import React from "react";
import { IoTrashBinOutline } from "react-icons/io5";
import { MdTab, MdEmail, MdCardMembership } from "react-icons/md";
import { tStudentL } from "types/student";
import MenuItems from "./MenuItems";
import styles from "styles/Menu.module.css";
import { FiEdit } from "react-icons/fi";

type Props = {
  student: tStudentL | undefined;
  close: VoidFunction;
  openDeleteModal: any;
  openEditModal?: any;
  openUpdateEmail: any;
  openUpdateStatus: any;
};

export default function StudentMenu({
  student,
  close,
  openDeleteModal,
  openEditModal,
  openUpdateEmail,
  openUpdateStatus,
}: Props) {
  return (
    <div className={styles.Main}>
      <div className={styles.Header}>{student?.email} </div>
      <div className={styles.rightSubHeader}>{student?.fullname} </div>

      <MenuItems
        title="View"
        icon={<MdTab size={13} />}
        dataId={student?.id}
        action={() => {}}
      />
      <MenuItems
        title="Update Email"
        icon={<MdEmail size={13} />}
        dataId={student?.id}
        action={openUpdateEmail}
      />
      <MenuItems
        title="Update Status"
        icon={<MdCardMembership size={13} />}
        dataId={student?.id}
        action={openUpdateStatus}
      />
      {/* <MenuItems
        title="Edit"
        icon={<FiEdit size={13} />}
        dataId={student?.id}
        action={openEditModal}
      /> */}
      <MenuItems
        title="Delete"
        icon={<IoTrashBinOutline size={13} />}
        dataId={student?.id}
        action={openDeleteModal}
      />
    </div>
  );
}
