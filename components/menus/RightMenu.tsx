import React from 'react'

type Props = {
    show: boolean,
    position: { x: number, y: number },
    children: any
}

export default function RightMenu({ show, position, children }: Props) {
    return (
        <div
            style={{
                height: "auto",
                width: 200,
                position: "absolute",
                borderRadius: 2,
                background: "black",
                left: position.x,
                top: position.y,
                display: show ? "block" : "none",
                zIndex: 100,
                paddingBottom: 10
            }}
        >
            {children}
        </div>
    )
}