import { fetcher } from "fetchers/fetcher";
import React, { useEffect, useState } from "react";
import useS<PERSON> from "swr";
import { tUserS } from "types/tutor";
import { FcSearch } from "react-icons/fc";
import inputStyles from "styles/InputField.module.css";
type Props = {
  isOpen: Boolean;
  onChange: any;
  searchValue: string;
  apiRoute: string;
};

export default function InputSelectorForModal({
  isOpen,
  onChange,
  searchValue,
  apiRoute,
}: Props) {
  const [isSearching, setIsSearching] = useState<Boolean>(false);
  const { data, error } = useSWR<Array<tUserS>>(
    `${apiRoute}?name=${searchValue}`,
    fetcher,
  );

  useEffect(() => {
    if (!data && !error) setIsSearching(true);
    else setIsSearching(false);
  }, [data, error]);

  if (isOpen)
    return (
      <div className="relative ">
        <SearchLoading isSearching={isSearching} />
        <div className="hide-scrollbar z-20 mt-0.5 max-h-52 w-full overflow-hidden overflow-y-scroll rounded-md border border-gray-200 bg-white py-0.5">
          {data?.map((user: any) => {
            return (
              <div
                key={user.id}
                className="cursor-pointer px-2 py-1.5 text-xs font-medium text-slate-700 hover:border-l  hover:border-r hover:border-l-gray-800  hover:border-r-gray-800 hover:bg-gray-100"
                onClick={() => onChange(user)}
              >
                {user.fullName ? user.fullName : user.fullname}
              </div>
            );
          })}
        </div>
      </div>
    );
  else
    return (
      <div className="relative">
        <SearchLoading isSearching={isSearching} />
      </div>
    );
}

function SearchLoading(props: any) {
  if (props.isSearching)
    return (
      // <div className="absolute right-12 top-16 mr-1 mt-2">
      // <div className=" -mt-[22px] mr-7 flex flex-row justify-end bg-white">
      <div className="absolute -top-9 right-6 mr-1 mt-3">
        <FcSearch size={12} />
      </div>
    );
  else return null;
}
