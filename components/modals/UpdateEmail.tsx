import { Update } from "fetchers/updater";
import React, { useEffect, useState } from "react";
import inputStyles from "styles/InputField.module.css";
import { ModalButtons, Button, CustomModal } from "./CustomModal";

type Props = {
  isOpen: boolean;
  onClose: any;
  mutate: any;
  email: string | undefined;
};

export default function UpdateEmail({ isOpen, onClose, email, mutate }: Props) {
  const [newEmail, setNewEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const Action = {
    Submit: async () => {
      setIsLoading(true);
      const data = {
        email: email,
        newEmail: newEmail,
      };
      await Update("/auth/update-email", data, "Student Email");
      setIsLoading(false);
      onClose();
      await mutate();
    },
  };

  useEffect(() => {
    if (email) setNewEmail(email);
  }, [email]);

  if (isOpen)
    return (
      <CustomModal title={email} centerHeader onClose={onClose} isOpen={isOpen}>
        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>New Email</div>
          <input
            type="email"
            className={inputStyles.Field}
            value={newEmail}
            onChange={(e) => setNewEmail(e.target.value)}
          />
        </div>
        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={Action.Submit}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}

// <div className={styles.mainContainer}>
//   <div className={styles.Overlay}></div>
//   <div className={isOpen ? styles.updateContent : styles.close}>
//     <div className={styles.Deltitle}>{email}</div>
//     <div className={styles.inputContainer}>
//       <div className={styles.inputHeader}>New Email</div>
//       <input
//         type="email"
//         className={styles.InputField}
//         value={newEmail}
//         onChange={(e) => setNewEmail(e.target.value)}
//       />
//     </div>
//     <div className={styles.buttonContainer}>
//       <div className={styles.cancelButton} onClick={onClose}>
//         cancel
//       </div>
//       <div className={styles.submitButton} onClick={handleSubmit}>submit</div>
//     </div>
//   </div>
// </div>
