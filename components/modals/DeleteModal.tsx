import React, { useState } from "react";
import { Dots } from "react-activity";
import { FiAlertCircle } from "react-icons/fi";
import styles from "styles/Modal.module.css";

type Props = {
  title: string;
  action: any;
  isOpen: boolean;
  onClose: any;
  isLoading?: any;
};

export default function DeleteModal({
  title,
  action,
  isOpen,
  onClose,
  isLoading,
}: Props) {
  const handleYes = async () => {
    await action();
    onClose();
  };
  if (isOpen)
    return (
      <div className={styles.Main}>
        <div className={styles.Overlay}></div>
        <div className={isOpen ? styles.Content : styles.Hidden}>
          <div className={styles.Header}>
            <div className={styles.Icon}>
              <FiAlertCircle size={19} color="red" />
            </div>
            <div className={styles.Title}> {title}</div>
          </div>
          <div className={styles.Buttons}>
            <div className={styles.cButton} onClick={onClose}>
              NO
            </div>
            <div className={styles.sButton} onClick={handleYes}>
              {isLoading ? <Dots /> : "YES"}
            </div>
          </div>
        </div>
      </div>
    );
  else return null;
}
