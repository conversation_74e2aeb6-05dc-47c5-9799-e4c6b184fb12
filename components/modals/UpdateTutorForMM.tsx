import React, { useEffect, useState } from "react";
import { tL<PERSON>on } from "types/dashboard";
import { tUserS } from "types/tutor";
import InputSelectorForModal from "./InputSelectorForModal";
import inputStyles from "styles/InputField.module.css";
import { Button, CustomModal, ModalButtons } from "./CustomModal";

type Props = {
  isOpen: boolean;
  onClose: any;
  mutate: any;
  selectedList: tLesson | undefined;
};

export default function UpdateTutorMForMS({
  isOpen,
  onClose,
  mutate,
  selectedList,
}: Props) {
  const [tutor, setTutor] = useState<string>("");
  const [selectedTutor, setSelectedTutor] = useState<string>("");
  const [isTFocused, setIsTFocused] = useState<Boolean>(false);

  const handleTutorSearch = (e: any) => {
    setTutor(e.target.value);
  };

  const handleTutorFocus = (e: any) => {
    setIsTFocused(true);
  };

  const handleTutorSelect = (tutor: tUserS) => {
    setIsTFocused(false);
    setSelectedTutor(tutor.id);
    setTutor(tutor.fullName);
  };
  if (isOpen)
    return (
      <CustomModal
        title="Update Tutor"
        isOpen={isOpen}
        onClose={onClose}
        centerHeader
      >
        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>Tutor</div>
          <input
            type="text"
            className={inputStyles.Field}
            value={tutor}
            onChange={handleTutorSearch}
            onFocus={handleTutorFocus}
          />

          <InputSelectorForModal
            isOpen={isTFocused}
            onChange={handleTutorSelect}
            searchValue={tutor}
            apiRoute="/tutor/search"
          />
        </div>
        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={() => {}}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}
