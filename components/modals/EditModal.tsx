import { fetcher } from "fetchers/fetcher";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { tChildF, tLesson } from "types/dashboard";
import { tUserS } from "types/tutor";
import InputSelectorForModal from "./InputSelectorForModal";
import inputStyles from "styles/InputField.module.css";
import { ModalButtons, Button, CustomModal } from "./CustomModal";

type Props = {
  isOpen: boolean;
  onClose: any;
  mutate: any;
  lesson: tLesson | undefined;
};

export default function EditModal({ isOpen, onClose, lesson, mutate }: Props) {
  const [userInterface, setUserInterface] = useState<boolean>(false);

  //For Tutor
  const [tutor, setTutor] = useState<string>("");
  const [selectedTutor, setSelectedTutor] = useState<string>("");
  const [isTFocused, setIsTFocused] = useState<Boolean>(false);
  // For Student
  const [student, setStudent] = useState<string>("");
  const [selectedStudent, setSelectedStudent] = useState<string>("");
  const [isSFocused, setIsSFocused] = useState<Boolean>(false);
  // For Child...
  const [seletecdChild, setSeletecdChild] = useState<string>("");
  const { data: children, error } = useSWR<Array<tChildF>>(
    selectedStudent ? `/student/${selectedStudent}/children` : null,
    fetcher
  );

  // For Sub Item...
  const [status, setStatus] = useState<string>("");
  const [price, setPrice] = useState<number>();
  const [commission, setCommission] = useState<number>();
  const [duration, setDuration] = useState<number>();

  const handleUserInterface = () => {
    setUserInterface(!userInterface);
  };

  //For Tutor Search ...
  const handleTutorSearch = (e: any) => {
    setTutor(e.target.value);
  };

  const handleTutorFocus = (e: any) => {
    setIsTFocused(true);
  };

  const handleTutorSelect = (tutor: tUserS) => {
    setIsTFocused(false);
    setSelectedTutor(tutor.id);
    setTutor(tutor.fullName);
  };

  // For Student Search....
  const handleStudentSearch = (e: any) => {
    setStudent(e.target.value);
  };

  const handleStudentFocus = () => {
    setIsSFocused(true);
  };

  const handleStudentSelect = (student: tUserS) => {
    setIsSFocused(false);
    setSelectedStudent(student.id);
    setStudent(student.fullname);
  };

  // For Child Select ...
  const handleChildSelect = (e: any) => {
    e.preventDefault();
    setSeletecdChild(e.target.value);
  };

  // For SubItem Search .....
  const handleStatus = (e: any) => {
    setStatus(e.target.value);
  };

  const handlePriceSearch = (e: any) => {
    setPrice(e.target.value);
  };

  const handleCommissionSearch = (e: any) => {
    setCommission(e.target.value);
  };
  const handleDurationSearch = (e: any) => {
    setDuration(e.target.value);
  };

  useEffect(() => {
    if (lesson?.id) {
      setStudent(lesson.student);
      setTutor(lesson.tutor);
      setSelectedStudent(lesson.studentId);
      setSelectedTutor(lesson.tutorId);
      setSeletecdChild(lesson.childId);
      setPrice(lesson.price);
      setCommission(lesson.commission);
      setDuration(lesson.length);
      setStatus(lesson.status);
    }
  }, [lesson]);

  useEffect(() => {
    setIsSFocused(false);
    setIsTFocused(false);
    setUserInterface(false);
  }, [isOpen]);
  if (isOpen)
    return (
      <CustomModal title="Edit Lesson" isOpen={isOpen} onClose={onClose}>
        <div className={inputStyles.flexInputfield}>
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Price</div>
            <input
              type="text"
              className={inputStyles.Field}
              value={price}
              onChange={handlePriceSearch}
            />
          </div>

          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Commission</div>
            <input
              type="text"
              className={inputStyles.Field}
              value={commission}
              onChange={handleCommissionSearch}
            />
          </div>

          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Duration</div>
            <input
              type="text"
              className={inputStyles.Field}
              value={duration}
              onChange={handleDurationSearch}
            />
          </div>
        </div>

        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>Status</div>
          <select
            className={inputStyles.Field}
            value={status}
            onChange={handleStatus}
          >
            <option value="Pending">Pending</option>
            <option value="Confirmed">Confirmed</option>
            <option value="Late Cancelled By Student">
              Late Cancelled By Student
            </option>
            <option value="Cancelled By Tutor">Cancelled By Tutor</option>
            <option value="Cancelled By Student">Cancelled By Student</option>
          </select>
        </div>

        <div className={inputStyles.flexInputfield}>
          <input
            type="checkbox"
            className={inputStyles.Box}
            checked={userInterface}
            onChange={handleUserInterface}
          />
          <div className={inputStyles.Text}>Update User References</div>
        </div>

        {userInterface && (
          <>
            <div className={inputStyles.inputContainer}>
              <div className={inputStyles.Title}>Tutor</div>
              <input
                type="text"
                className={inputStyles.Field}
                value={tutor}
                onChange={handleTutorSearch}
                onFocus={handleTutorFocus}
              />
              <InputSelectorForModal
                isOpen={isTFocused}
                onChange={handleTutorSelect}
                searchValue={tutor}
                apiRoute="/tutor/search"
              />
            </div>
            <div className={inputStyles.inputContainer}>
              <div className={inputStyles.Title}>Student</div>
              <input
                type="text"
                className={inputStyles.Field}
                value={student}
                onChange={handleStudentSearch}
                onFocus={handleStudentFocus}
              />
              <InputSelectorForModal
                isOpen={isSFocused}
                onChange={handleStudentSelect}
                searchValue={student}
                apiRoute="/student/search"
              />
            </div>
            {selectedStudent && (
              <div className={inputStyles.inputContainer}>
                <div className={inputStyles.Title}>Child</div>
                <select
                  className={inputStyles.Field}
                  value={seletecdChild}
                  onChange={handleChildSelect}
                >
                  {children?.map((child: tChildF) => {
                    return (
                      <option key={child.id} value={child.id}>
                        {child.name}({child.postCode})
                      </option>
                    );
                  })}
                </select>
              </div>
            )}
          </>
        )}

        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={() => {}}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}
