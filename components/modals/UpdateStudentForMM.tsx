import { fetcher } from "fetchers/fetcher";
import React, { useEffect, useState } from "react";
import { FaSleigh } from "react-icons/fa";
import useSWR from "swr";
import { tChildF, tLesson } from "types/dashboard";
import { tUserS } from "types/tutor";
import InputSelectorForModal from "./InputSelectorForModal";
import inputStyles from "styles/InputField.module.css";
import { Button, CustomModal, ModalButtons } from "./CustomModal";

type Props = {
  isOpen: boolean;
  onClose: any;
  mutate: any;
};

export default function UpdateStudent({ isOpen, onClose, mutate }: Props) {
  const [student, setStudent] = useState<string>("");
  const [selectedStudent, setSlectedStudent] = useState<string>("");
  const [isSFocused, setIsSFocused] = useState<boolean>(false);
  // For Child..
  const [child, setChild] = useState<string>("");
  const { data: children, error } = useSWR<Array<tChildF>>(
    selectedStudent ? `/student/${selectedStudent}/children` : null,
    fetcher
  );

  const handleStudentSearch = (e: any) => {
    setStudent(e.target.value);
  };
  const handleStudentFocus = () => {
    setIsSFocused(true);
  };

  const handleStudentSelect = (student: tUserS) => {
    setIsSFocused(false);
    setSlectedStudent(student.id);
    setStudent(student.fullname);
  };

  const handleChildSelect = (e: any) => {
    e.preventDefault();
    setChild(e.target.value);
  };

  if (isOpen)
    return (
      <CustomModal title="Update Student" isOpen={isOpen} onClose={onClose}>
        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>Student</div>
          <input
            type="text"
            className={inputStyles.Field}
            value={student}
            onChange={handleStudentSearch}
            onFocus={handleStudentFocus}
          />
          <InputSelectorForModal
            isOpen={isSFocused}
            onChange={handleStudentSelect}
            searchValue={student}
            apiRoute="/student/search"
          />
        </div>

        {selectedStudent && (
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Child</div>
            <select
              className={inputStyles.Field}
              value={child}
              onChange={handleChildSelect}
            >
              {children?.map((child: tChildF) => {
                return (
                  <option key={child.id} value={child.id}>
                    {child.name}({child.postCode})
                  </option>
                );
              })}
            </select>
          </div>
        )}

        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={() => {}}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}

//  <div className={styles.mainContainer}>
//         <div className={styles.Overlay}></div>
//         <div className={isOpen ? styles.updateContent : styles.close}>
//           <div className={styles.Deltitle}>{title}</div>
//           <div className={styles.inputContainer}>
//             <div className={styles.inputHeader}>Student</div>
//             <input
//               type="text"
//               className={styles.InputField}
//               value={student}
//               onChange={handleStudentSearch}
//               onFocus={handleStudentFocus}
//             />
//             <InputSelectorForModal
//               isOpen={isSFocused}
//               onChange={handleStudentSelect}
//               searchValue={student}
//               apiRoute="/student/search"
//             />
//           </div>
//           {selectedStudent && (
//             <div className={styles.inputContainer}>
//               <div className={styles.inputHeader}>Child</div>
//               <select
//                 className={styles.InputField}
//                 value={child}
//                 onChange={handleChildSelect}
//               >
//                 {children?.map((child: tChildF) => {
//                   return (
//                     <option key={child.id} value={child.id}>
//                       {child.name}({child.postCode})
//                     </option>
//                   );
//                 })}
//               </select>
//             </div>
//           )}
//           <div className={styles.buttonContainer}>
//             <div className={styles.cancelButton} onClick={onClose}>
//               cancel
//             </div>
//             <div className={styles.submitButton}>submit</div>
//           </div>
//         </div>
//       </div>
