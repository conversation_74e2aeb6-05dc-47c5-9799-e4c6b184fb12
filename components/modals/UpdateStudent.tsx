import { fetcher } from "fetchers/fetcher";
import React, { useEffect, useState } from "react";
import { FaSleigh } from "react-icons/fa";
import useSWR from "swr";
import { tChildF, tLesson } from "types/dashboard";
import { tUserS } from "types/tutor";
import InputSelectorForModal from "./InputSelectorForModal";
import { Button, CustomModal, ModalButtons } from "./CustomModal";
import inputStyles from "styles/InputField.module.css";

type Props = {
  isOpen: boolean;
  onClose: any;
  mutate: any;
  lesson: tLesson | undefined;
};

export default function UpdateStudent({
  isOpen,
  onClose,
  mutate,
  lesson,
}: Props) {
  const [student, setStudent] = useState<string>("");
  const [selectedStudent, setSlectedStudent] = useState<string>("");
  const [isSFocused, setIsSFocused] = useState<boolean>(false);
  // For Child..
  const [child, setChild] = useState<string>("");
  const { data: children, error } = useSWR<Array<tChildF>>(
    selectedStudent ? `/student/${selectedStudent}/children` : null,
    fetcher
  );

  const handleStudentSearch = (e: any) => {
    setStudent(e.target.value);
  };
  const handleStudentFocus = (e: any) => {
    setIsSFocused(e.target.value);
  };

  const handleStudentSelect = (student: tUserS) => {
    setIsSFocused(false);
    setSlectedStudent(student.id);
    setStudent(student.fullname);
  };

  useEffect(() => {
    if (lesson?.id) {
      setStudent(lesson.student);
      setSlectedStudent(lesson.studentId);
    }
  }, [lesson]);

  const handleChildSelect = (e: any) => {
    e.preventDefault();
    setChild(e.target.value);
  };

  if (isOpen)
    return (
      <CustomModal
        title="Update Student"
        onClose={onClose}
        isOpen={isOpen}
        centerHeader
      >
        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>Full Name</div>
          <input
            type="text"
            className={inputStyles.Field}
            value={student}
            onChange={handleStudentSearch}
            onFocus={handleStudentFocus}
          />
          <InputSelectorForModal
            isOpen={isSFocused}
            onChange={handleStudentSelect}
            searchValue={student}
            apiRoute="/student/search"
          />
        </div>
        {selectedStudent && (
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Child</div>
            <select
              className={inputStyles.Field}
              value={child}
              onChange={handleChildSelect}
            >
              {children?.map((child: tChildF) => {
                return (
                  <option key={child.id} value={child.id}>
                    {child.name}({child.postCode})
                  </option>
                );
              })}
            </select>
          </div>
        )}
        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={() => {}}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}
