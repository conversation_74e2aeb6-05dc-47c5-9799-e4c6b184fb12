import React, { useEffect, useState } from "react";
import { tLesson } from "types/dashboard";
import { tUserS } from "types/tutor";
import InputSelectorForModal from "./InputSelectorForModal";
import inputStyles from "styles/InputField.module.css";
import { Button, CustomModal, ModalButtons } from "./CustomModal";

type Props = {
  isOpen: boolean;
  onClose: any;
  Data: tLesson | undefined;
  mutate: any;
};

export default function UpdateTutor({ isOpen, onClose, Data }: Props) {
  const [tutor, setTutor] = useState<string>("");
  const [selectedTutor, setSelectedTutor] = useState<string>("");
  const [isTFocused, setIsTFocused] = useState<Boolean>(false);

  const handleTutorSearch = (e: any) => {
    setTutor(e.target.value);
  };

  const handleTutorFocus = (e: any) => {
    setIsTFocused(true);
  };

  const handleTutorSelect = (tutor: tUserS) => {
    setIsTFocused(false);
    setSelectedTutor(tutor.id);
    setTutor(tutor.fullName);
  };

  useEffect(() => {
    if (Data?.id) {
      setTutor(Data.tutor);
      setSelectedTutor(Data.tutorId);
    }
  }, [Data]);
  if (isOpen)
    return (
      <CustomModal
        title="Update Tutor"
        onClose={onClose}
        isOpen={isOpen}
        centerHeader
      >
        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>Tutor Name</div>
          <input
            type="text"
            value={tutor}
            onChange={handleTutorSearch}
            onFocus={handleTutorFocus}
            className={inputStyles.Field}
          />
          <InputSelectorForModal
            isOpen={isTFocused}
            onChange={handleTutorSelect}
            searchValue={tutor}
            apiRoute="/tutor/search"
          />
        </div>

        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={() => {}}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}
