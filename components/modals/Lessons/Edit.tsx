import { useEffect, useState } from "react";
import api from "fetchers/BaseUrl";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Dots } from "react-activity";
import * as yup from "yup";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@components/ui/form";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectGroup,
} from "@components/ui/select";
import { Button } from "@components/ui/button";

import useSWR from "swr";
import { fetcher } from "fetchers/fetcher";
import { tChildF, tLesson } from "types/dashboard";
import { tUserS } from "types/tutor";

import { getSession } from "next-auth/react";
import { Input } from "@components/ui/input";
import InputSelectorForModal from "../InputSelectorForModal";
import { MdEdit } from "react-icons/md";
import { Checkbox } from "@components/ui/checkbox";
import { FaEdit, FaRegEdit } from "react-icons/fa";

type EditLessonProps = {
  lesson: tLesson | undefined;
  mutate: () => void;
};

export default function EditLesson({ lesson, mutate }: EditLessonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [selectedStuId, setSelectedStuId] = useState<string>("");
  const [selectedTutorId, setSelectedTutorId] = useState<string>("");

  // State for tutor
  const [tutor, setTutor] = useState<string>("All");

  const [seletedTutorId, setSeletedTutorId] = useState<string>("");
  const [isTFocused, setIsTFocused] = useState<Boolean>(false);
  const [isTutorNotFound, setIsTutorNotFound] = useState<boolean>(false);

  // State For Student
  const [student, setStudent] = useState<string>("All");
  const [seletedStuId, setSeletedStuId] = useState<string>("");
  const [isSFocused, setIsSFocused] = useState<Boolean>(false);

  const [selectedChild, setSelectedChild] = useState<string>("");
  const [isStuNotFound, setIsStuNotFound] = useState<boolean>(false);

  // Fetch Children Data Based on Selected Student

  const {
    data: Children,
    mutate: childMutate,
    isLoading: chilLoading,
  } = useSWR<Array<tChildF>>(
    seletedStuId ? `/student/${seletedStuId}/children` : null,
    fetcher,
  );

  const resolver = yup.object().shape({
    price: yup.number().required("Price is required"),
    commission: yup.number().required("Commission is required"),
    duration: yup.number().required("Duration is required"),
    status: yup.string().required("Status is required"),

    // ✅ Define userInterface as a boolean field
    userInterface: yup.boolean(),

    // ✅ Fix the when condition by using a function callback
    tutorId: yup
      .string()
      .when("userInterface", (userInterface, schema) =>
        userInterface ? schema.required("Tutor is required") : schema,
      ),

    studentId: yup
      .string()
      .when("userInterface", (userInterface, schema) =>
        userInterface ? schema.required("Student is required") : schema,
      ),

    childId: yup
      .string()
      .when("userInterface", (userInterface, schema) =>
        userInterface ? schema.required("Child is required") : schema,
      ),
  });

  const defaultValues = {
    price: lesson?.price || "",
    commission: lesson?.commission || "",
    duration: lesson?.length || "",
    status: lesson?.status || "Pending",
    tutorId: lesson?.tutorId || "",
    studentId: lesson?.studentId || "",
    childId: lesson?.childId || "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  useEffect(() => {
    if (lesson) {
      setStudent(lesson.student);
      setTutor(lesson.tutor);
      setSelectedStuId(lesson.studentId);
      setSelectedTutorId(lesson.tutorId);
      setSelectedChild(lesson.childId);
    }
  }, [lesson]);

  const Update = {
    TutorSearch: (e: any) => {
      setTutor(e.target.value);
    },
    TutorSelect: (tutor: tUserS) => {
      setIsTFocused(false);

      setSeletedTutorId(tutor.id);

      setTutor(tutor.fullName);
    },
    TutorFocus: () => {
      setIsTFocused(true);
      if (tutor === "All") setTutor("");
    },

    StudentSearch: (e: any) => {
      setStudent(e.target.value);
    },
    StudentSelect: (student: tUserS) => {
      setIsSFocused(false);
      // setSelectedStudent(student.id);
      setSeletedStuId(student.id);
      setStudent(student.fullname);
      setSelectedChild("");
    },
    StudentFocus: () => {
      setIsSFocused(true);
      if (student === "All") setStudent("");
    },
    ChildSelect: (e: any) => {
      setSelectedChild(e);
    },
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const session = await getSession();
      const token = session?.token;

      const response = await api.put(
        `/lesson/${lesson?.id}`,
        {
          ...data,
          studentId: selectedStuId,
          tutorId: selectedTutorId,
          childId: selectedChild,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsOpen(false);
        toast.success("Lesson updated successfully!");
      } else {
        toast.error("Unexpected server error!");
      }
    } catch (error) {
      toast.error("Failed to update lesson!");
    }
    setIsLoading(false);
  };

  const ModalControl = {
    reset: (value: boolean) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => setIsOpen(true),
  };

  const watchUI = form.watch("userInterface");

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <div className="flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
          <FaRegEdit size={16} color="#64748B" />
          <span className="ml-1 text-xs font-medium text-slate-600">
            Update Lesson
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-full overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Lesson</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2.5">
            {/* Price */}

            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter price"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Commission */}
            <FormField
              control={form.control}
              name="commission"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Commission</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter commission"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Duration */}
            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duration</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter duration"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>{field.value}</SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Confirmed">Confirmed</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* ✅ Update User References Section */}

            <FormField
              control={form.control}
              name="canTeachAtHome"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-2 pt-2">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className={
                        "h-4 w-4 rounded border border-slate-300 bg-white shadow-none data-[state=checked]:bg-slate-800"
                      }
                    />
                  </FormControl>
                  <FormLabel>Update User References</FormLabel>

                  <FormMessage />
                </FormItem>
              )}
            />

            {watchUI && (
              <>
                {/* Tutor Selection */}
                <div className="my-5">
                  <div
                    className={`pb-1 text-sm font-medium  ${
                      isTutorNotFound ? "text-destructive" : "text-slate-800"
                    }`}
                  >
                    Tutor
                  </div>

                  <Input
                    type="search"
                    value={tutor}
                    placeholder="Select a Tutor"
                    onChange={Update.TutorSearch}
                    onFocus={Update.TutorFocus}
                    className="text-xs font-medium text-slate-600 placeholder:text-xs placeholder:font-normal placeholder:text-slate-500"
                  />

                  {tutor && (
                    <InputSelectorForModal
                      isOpen={isTFocused}
                      searchValue={tutor}
                      onChange={Update.TutorSelect}
                      apiRoute="/tutor/search"
                    />
                  )}

                  {isTutorNotFound && (
                    <div className="mt-1.5 text-xs font-medium text-destructive">
                      No tutor found!
                    </div>
                  )}
                </div>

                {/* Student Selection */}
                <div className="pb-5">
                  <div
                    className={`pb-1 text-sm font-medium  ${
                      isStuNotFound ? "text-destructive" : "text-slate-800"
                    }`}
                  >
                    Student
                  </div>

                  <Input
                    type="search"
                    value={student}
                    placeholder="Select a Student"
                    onChange={Update.StudentSearch}
                    onFocus={Update.StudentFocus}
                    className="text-xs font-medium text-slate-600 placeholder:text-xs placeholder:font-normal placeholder:text-slate-500"
                  />

                  {student && (
                    <InputSelectorForModal
                      isOpen={isSFocused}
                      searchValue={student}
                      onChange={Update.StudentSelect}
                      apiRoute="/student/search"
                    />
                  )}

                  {isStuNotFound && (
                    <div className="mt-1.5 text-xs font-medium text-destructive">
                      No student found!
                    </div>
                  )}
                </div>
                {/* Children Selection */}
                {seletedStuId && (
                  <div>
                    <div className="pb-1 text-sm font-medium text-slate-800">
                      Select child
                    </div>

                    <Select
                      onValueChange={Update.ChildSelect}
                      defaultValue={selectedChild}
                    >
                      <SelectTrigger className="mb-5 w-full bg-white">
                        <div className="text-xs font-medium text-slate-600">
                          {(Children &&
                            Children.find((c) => c.id === selectedChild)
                              ?.name) ||
                            "Select a child"}
                        </div>
                      </SelectTrigger>

                      <SelectContent>
                        <SelectGroup>
                          {Children && Children.length === 0 && (
                            <div className="p-1 text-[10px] font-medium text-blue-600">
                              No children found. Please add a child first!
                            </div>
                          )}
                          {Children &&
                            Children.map((child: any) => {
                              return (
                                <SelectItem key={child.id} value={child.id}>
                                  {child?.name}
                                </SelectItem>
                              );
                            })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </>
            )}

            {/* Action Buttons */}
            <div className="mt-6 flex w-full justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-cyan-600 bg-cyan-600 hover:bg-white hover:text-cyan-600"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
