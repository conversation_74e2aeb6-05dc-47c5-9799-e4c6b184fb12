import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import inputStyles from "styles/InputField.module.css";
import { Button, CustomModal, ModalButtons } from "./CustomModal";

type Props = {
  isOpen: boolean;
  onClose: any;
  mutate: any;
};

export default function UpdatePaymentMForMs({
  isOpen,
  onClose,
  mutate,
}: Props) {
  const [payment, setPayment] = useState<string>("");
  const [paymentDate, setPaymentDate] = useState<Date>(new Date());
  const [isOpenDatePicker, setIsOpenDatePicker] = useState<boolean>(false);

  // handelar

  const handlePaidProcess = (e: any) => {
    setPayment(e.target.value);
  };
  const handlePaymentDate = (e: Date | any) => {
    setIsOpenDatePicker(false);
    setPaymentDate(e);
  };
  if (isOpen)
    return (
      <CustomModal
        title="Update Payment"
        isOpen={isOpen}
        onClose={onClose}
        centerHeader
      >
        <div className={inputStyles.flexInputfield}>
          <div className={inputStyles.Title}>Paid to Tutor :</div>
          <select
            className={inputStyles.SelectField}
            value={payment}
            onChange={handlePaidProcess}
          >
            <option value="Yes">Yes</option>
            <option value="No">No</option>
          </select>
        </div>

        <div className={inputStyles.inputContainer}>
          <div className={inputStyles.Title}>Payment Date</div>
          <div
            className={isOpenDatePicker ? inputStyles.DatePickerContainer : ""}
          >
            <DatePicker
              className={inputStyles.Field}
              onChange={handlePaymentDate}
              selected={paymentDate}
              dateFormat="dd/MM/yyyy"
              onFocus={() => setIsOpenDatePicker(true)}
              onClickOutside={() => setIsOpenDatePicker(false)}
            />
          </div>
        </div>

        <ModalButtons>
          <Button type="Cancel" handleClick={onClose}>
            Cancel
          </Button>
          <Button type="Submit" handleClick={() => {}}>
            Submit
          </Button>
        </ModalButtons>
      </CustomModal>
    );
  else return null;
}

/*

<div className={styles.mainContainer}>
        <div className={styles.Overlay}></div>
        <div className={isOpen ? styles.updateContent : styles.close}>
          <div className={styles.Deltitle}>{title}</div>
          <div className={styles.inputContainerFlex}>
            <div className={styles.inputHeader}>Paid to Tutor :</div>
            <select
              className={styles.Inpufieldflex}
              value={payment}
              onChange={handlePaidProcess}
            >
              <option value="Yes">Yes</option>
              <option value="No">No</option>
            </select>
          </div>
          <div className={styles.inputContainer}>
            <div className={styles.inputHeader}>Payment Date</div>
            <div className={styles.DatePickerContainer}>
              <DatePicker
                className={styles.DatePickerDesign}
                onChange={handlePaymentDate}
                selected={paymentDate}
                dateFormat="dd/MM/yyyy"
              />
            </div>
          </div>
          <div className={styles.buttonContainer}>
            <div className={styles.cancelButton} onClick={onClose}>
              cancel
            </div>
            <div className={styles.submitButton}>submit</div>
          </div>
        </div>
      </div>

*/
