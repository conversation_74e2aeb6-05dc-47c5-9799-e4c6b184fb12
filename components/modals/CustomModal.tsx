import React from "react";
import styles from "styles/CustomModal.module.css";

import { MdCancelPresentation } from "react-icons/md";
import { FaRegEdit } from "react-icons/fa";

type PropsEChild = {
  children?: any;
  title: string | undefined;
  onClose: any;
  isOpen: boolean;
  WidthSize?: string;
  centerHeader?: boolean;
  focusClose?: boolean;
  EditIconBtn?: boolean;
  OpenEditProfile?: any;
};

export function CustomModal({
  children,
  title,
  onClose,
  isOpen,
  WidthSize,
  centerHeader,
  focusClose,
  EditIconBtn,
  OpenEditProfile,
}: PropsEChild) {
  return (
    <div className={isOpen ? styles.Modal : styles.hidden}>
      <div
        style={{
          position: "relative",
          display: "flex",
          flexDirection: "column",
          backgroundColor: "#fefefe",
          paddingLeft: 20,
          paddingRight: 20,
          overflow: "auto",
          height: "auto",
          width: WidthSize ? WidthSize : "25%",
          maxWidth: "70%",
          maxHeight: "100%",
          borderRadius: "1%",
          zIndex: 100,
        }}
      >
        {EditIconBtn && (
          <div className={styles.EditIcon} onClick={OpenEditProfile}>
            <FaRegEdit size={21} />
          </div>
        )}
        <div className={styles.Close} onClick={onClose}>
          <MdCancelPresentation size={23} />
        </div>
        <div className={centerHeader ? styles.centerHeader : styles.header}>
          {title}
        </div>
        <div className={styles.items}>{children}</div>
      </div>
    </div>
  );
}

type bsProps = {
  children?: any;
};

export function ModalButtons({ children }: bsProps) {
  return <div className={styles.footer}>{children}</div>;
}

type bProps = {
  children?: any;
  type: "Submit" | "Cancel" | "Reset";
  handleClick: any;
};

export function Button({ children, type, handleClick }: bProps) {
  return (
    <div
      className={
        type === "Submit"
          ? styles.sButton
          : type === "Reset"
          ? styles.rButton
          : styles.cButton
      }
      onClick={handleClick}
    >
      {children}
    </div>
  );
}
