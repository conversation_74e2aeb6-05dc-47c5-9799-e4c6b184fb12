import Link from "next/link";
import React from "react";
import styles from "styles/Sidebar.module.css"

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "components/ui/tooltip";

type Props = {
  icon: any,
  name: string,
  link: string,
  isCollapsed?: boolean
}

export default function SidebarItem({ icon, name, link, isCollapsed }: Props) {
  return (
    <Link href={link}>
      <div className={isCollapsed ? "w-full h-auto flex items-center content-center p-3 text-[var(--font-color-bright)] hover:bg-[var(--background-color-bright)] hover:cursor-pointer hover:rounded-md hover:text-gray-900 " : "w-full  h-auto flex items-center p-3 text-[var(--font-color-bright)]  hover:bg-[var(--background-color-bright)] hover:cursor-pointer hover:rounded-tl-md hover:rounded-bl-md  hover:text-gray-900"}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className={isCollapsed ? "h-full" : "mr-2.5 flex justify-center"}>{icon}</div>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm break-words bg-gray-200 text-gray-900">
              <p>{name}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className={isCollapsed ? " hidden" : "text-base font-medium "}>
          <div className={isCollapsed ? " hidden" : " "}>{name}</div>
        </div>
      </div>
    </Link>
  );
}
