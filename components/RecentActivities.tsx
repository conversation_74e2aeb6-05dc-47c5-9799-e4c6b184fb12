import React from "react";
import { AiOutlineUserAdd } from "react-icons/ai";

type Props = {};

export default function RecentActivities({}: Props) {
  return (
    <div className="mb-1.5 flex h-16 w-full items-center rounded-md border border-slate-200 bg-white px-2.5">
      <div className="flex w-full flex-row items-center">
        {/* Left Section */}
        <div className="flex w-10 items-center justify-center">
          <div className="h-8 w-8">
            <AiOutlineUserAdd fontSize={26} />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex w-full flex-col justify-center">
          <div className="line-clamp-2 truncate text-[12px] text-gray-500">
            <b>Mo<PERSON><PERSON><PERSON> Islam</b> added a new student via Excel.
          </div>

          <div className="mt-1 text-[9px] font-light text-gray-700">
            20 minutes ago
          </div>
        </div>
      </div>
    </div>
  );
}

// import React from "react";
// import { AiOutlineUserAdd } from "react-icons/ai";
// import styles from "styles/Activity.module.css";

// type Props = {};

// export default function RecentActivities({}: Props) {
//   return (
//     <div className={styles.mainContainer}>
//       <div className={styles.Content}>
//         <div className={styles.Left}>
//           <div className={styles.Image}>
//             <AiOutlineUserAdd fontSize={26} />
//           </div>
//         </div>
//         <div className={styles.Right}>
//           <div className={styles.Description}>
//             <b>Mohaimeul Islam</b> added a new student via Excel.
//           </div>
//           <div className={styles.Date}> 20 minute Ago</div>
//         </div>
//       </div>
//     </div>
//   );
// }
