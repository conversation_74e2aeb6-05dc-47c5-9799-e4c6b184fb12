import { getSession } from "next-auth/react";
import api from "../fetchers/BaseUrl";

export async function SendNotifications(tokens: any, toast: any, data: any) {
  //   console.log(data);
  const session = await getSession();
  const token = session?.token;
  const Body = {
    to: tokens,
    title: data?.title,
    body: data?.body,
  };
  try {
    var response = await api.post("/map/push", Body, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    toast.success("Notifications sent!");
  } catch (e) {
    toast.success("Notifications sent!");
  }
}
