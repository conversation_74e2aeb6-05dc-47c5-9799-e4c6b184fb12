import { getSession } from "next-auth/react";
import { PgObject } from "types/dashboard";

export const fetcher = async (url: string) => {
  const session = await getSession();
  const token = session?.token;

  return fetch("https://api.lycaeumapp.com" + url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  }).then((r) => r.json());
};

export const pgfetcher = async (url: string) => {
  const session = await getSession();
  const token = session?.token;

  const res = await fetch("https://api.lycaeumapp.com" + url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  // Getting Pagination Info by Parsing
  const paginagion = res.headers.get("pagination");
  const pgobject: PgObject = JSON.parse(paginagion ? paginagion : "");

  return {
    data: await res.json(),
    page: pgobject,
  };
};