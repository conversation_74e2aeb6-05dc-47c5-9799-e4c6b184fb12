import { getSession } from "next-auth/react"
import { tLesson } from "types/dashboard"

export const RemoveLesson = async (lesson: tLesson | undefined) => {
    const session = await getSession();
    const token = session?.token;

    const res = await fetch(`https://api.lycaeumapp.com/lessons/${lesson?.id}`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
        },
    })

    return res.status === 200
}