import api from 'fetchers/BaseUrl'
import { getSession } from "next-auth/react";
import toast from 'react-hot-toast';


export async function Update(url: string, data: any, operationName: string) {
    const session = await getSession();
    const token = session?.token;

    var response = api.post(url, data, {
        headers: {
            Authorization: `Bearer ${token}`,
        }
    })
    toast.promise(response, {
        loading: `Updating ${operationName}`,
        success: () => `${operationName} Updated.`,
        error: () => `Failed to update ${operationName}`,
    })
}