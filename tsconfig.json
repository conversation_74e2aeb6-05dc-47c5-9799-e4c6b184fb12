{"compilerOptions": {"target": "ES2016", "lib": ["dom", "dom.iterable", "esnext", "ES7"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@components/*": ["components/*"], "@styles/*": ["styles/*"], "@fetchers/*": ["fetchers/*"], "@types/*": ["types/*"], "@assets/*": ["assets/*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}