"use client";

import React, { useEffect, useState } from "react";
import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import { tPaymentL, tPgPaymentL } from "types/payments";
import useSWR from "swr";
import { pgfetcher } from "@fetchers/fetcher";
import PageList from "@components/PageList";
import Payment, { PaymentListHeader } from "@components/lists/Payment";
import { useSession } from "next-auth/react";

import ListSkeltons from "@components/skeltons/ListSkeltons";

import { useForm } from "react-hook-form";

import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { GenerateQuery } from "core/Query";
import { Input } from "@components/ui/input";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Payments() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);

  const [selectedPayment, setSelectedPayment] = useState<tPaymentL>();

  const [date, setDate] = useState<Date | null>(
    selectedPayment?.date ? new Date(selectedPayment.date) : new Date(),
  );
  const [type, setType] = useState<string>("");
  const [query, setQuery] = useState<string>("");
  const [name, setName] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  const {
    data: Payments,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgPaymentL>(`/payment?p=${page}${query}`, pgfetcher);
  // console.log(Payments);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,

    formState: { errors },
  } = useForm<any>();

  const Handler = {
    UpdateName: (e: any) => {
      e.preventDefault();
      setName(e.target.value);
    },
  };

  const Action = {
    UpdateQuery: (query: string) => {
      // Construct a new query string by combining the current "page" value with the provided "query"
      let queryWithPage = `?p=${page}${query}`;

      // Use the router to update the URL to "/payments" with the new query string and "shallow" navigation
      //   router.push("/payments", queryWithPage, { shallow: true });
      router.push(`/payments${queryWithPage}`);

      // Update the "query" state with the provided "query"
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/payments", queryWithPage, { shallow: true });
      router.push(`/payments${queryWithPage}`);
      setPage(newPage);
    },

    generateQuery: () => {
      const queryParams = {
        name: name.length > 2 ? name : undefined,
      };
      Action.UpdateQuery(GenerateQuery(queryParams));
    },
  };

  useEffect(() => {
    if (isActive) {
      Action.generateQuery();
    }
  }, [name]);

  const watchField = watch();
  useEffect(() => {
    setValue("amount", selectedPayment?.amount);
    setValue("comment", selectedPayment?.comment);
    setDate(
      selectedPayment?.date ? new Date(selectedPayment.date) : new Date(),
    ),
      setType(selectedPayment?.type ? selectedPayment.type : "");
  }, [watchField && selectedPayment]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Payments | MuseCool Dashboard";

    setIsActive(false);

    //Create a URLSearchParams object from the current URL's search parameters
    const search = new URLSearchParams(window.location.search);
    //Check if the "p" parameter exists in the URL
    if (search.get("p")) {
      //If "p" parameter exists, convert it to a number and set it as the "page" state
      setPage(Number(search.get("p")));
    }

    search.delete("p");
    if (search.toString() !== "") {
      Action.UpdateQuery("&" + search.toString());
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          {/* //Header Section */}
          <div className="w-full ">
            <div className="mb-3 flex w-full flex-row items-center justify-between ">
              <div className="w-fit text-base font-medium text-gray-700">
                Payment List
              </div>

              <Input
                type="search"
                placeholder="Search "
                className="bg-white md:w-[100px] lg:w-[300px]"
                value={name}
                onChange={Handler.UpdateName}
              />
            </div>

            {/* // Payments List  */}
            <PaymentListHeader />

            {isLoading && <ListSkeltons height={45} count={15} />}
            {Payments?.data.map((payment: tPaymentL) => (
              <Payment key={payment.id} Payment={payment} mutate={mutate} />
            ))}
          </div>

          <PageList
            handler={Action.UpdatePage}
            page={Payments?.page}
            itemName="Payments"
          />
        </div>
      </Sidebar>
    );
}
