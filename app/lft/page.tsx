"use client";

import Sidebar from "@components/Sidebar";

import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import api from "fetchers/BaseUrl";
import { useForm } from "react-hook-form";
import useS<PERSON> from "swr";
import { yupResolver } from "@hookform/resolvers/yup";
import { Dots } from "react-activity";
import * as yup from "yup";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Input } from "@components/ui/input";
import { Button } from "@components/ui/button";
import { tStudentL } from "types/student";
import InputSelectorForModal from "@components/modals/InputSelectorForModal";
import { tChildF } from "types/dashboard";
import { fetcher, pgfetcher } from "@fetchers/fetcher";

import { tLftAdmin, tLftL, tPgLftL } from "types/lft";
import Header from "@components/Header";
import PageList from "@components/PageList";
import Lft, { LftListHeader } from "@components/lists/Lft";
import ListSkeltons from "@components/skeltons/ListSkeltons";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function LFTs() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [name, setName] = useState<string>("");

  const {
    data: Lfts,
    error,
    isLoading,
    mutate,
  } = useSWR<tPgLftL>(`/lft?p=${page}${query}`, pgfetcher);

  const Handelar = {
    UpdateName: (e: any) => {
      e.preventDefault();
      setName(e.target.value);
    },
  };

  const Action = {
    UpdateQuery: (query: string) => {
      let queryWithPage = `?p=${page}${query}`;
      //   router.push("/lft", queryWithPage, { shallow: true });
      router.push(`/lft${queryWithPage}`);
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/lft", queryWithPage, { shallow: true });
      router.push(`/lft${queryWithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      //   router.push("/lft", querywithPage, { shallow: true });
      router.push(`/lft${queryWithPage}`);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "LFT | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />
        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-2.5 flex flex-row items-center justify-between">
            <div className=" w-fit text-base font-medium text-gray-700">
              LFT List
            </div>

            <div className="flex flex-1 flex-row items-center justify-end space-x-2 ">
              <AddLFT mutate={mutate} />

              <Input
                type="search"
                placeholder="Student Name / Email"
                className="bg-white md:w-[100px] lg:w-[300px]"
                value={name}
                onChange={Handelar.UpdateName}
              />
            </div>
          </div>

          <LftListHeader
            updateQuery={Action.UpdateQuery}
            name={name}
            setName={setName}
            updatePage={Action.UpdatePage}
            pageReset={Action.PageReset}
          />

          {isLoading ? (
            <ListSkeltons height={42} count={30} />
          ) : (
            Lfts?.data.map((lft: tLftL) => (
              <Lft key={lft.id} lft={lft} mutate={mutate} />
            ))
          )}

          <PageList handler={Action.UpdatePage} page={Lfts?.page} />
        </div>
      </Sidebar>
    );
}

type AddLFTProps = {
  mutate: () => void;
};

function AddLFT({ mutate }: AddLFTProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [isStuNotFound, setIsStuNotFound] = useState<boolean>(false);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [student, setStudent] = useState<string>("");
  const [seletedStuId, setSeletedStuId] = useState<string>("");

  const {
    data: Children,
    mutate: childMutate,
    isLoading: chilLoading,
  } = useSWR<Array<tChildF>>(
    seletedStuId ? `/student/${seletedStuId}/children` : null,
    fetcher,
  );

  const {
    data: Admins,
    isLoading: adminLoading,
    mutate: adminMutate,
  } = useSWR<Array<tLftAdmin>>("/lft/admin-list", fetcher);

  var resolver = yup.object().shape({
    instrument: yup.string().required("Instrument name is required!"),
    childId: yup.string().required("Child name is required!"),
    lessonType: yup.string().required("Lesson type name is required!"),
    adminId: yup.string().required("Admin is required!"),
  });

  const defaultValues = {
    instrument: "",
    lessonType: "",
    childId: "",
    adminId: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    const { adminId } = data;

    if (!seletedStuId) {
      setIsStuNotFound(true);
      return;
    }

    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `/lft`,
        {
          ...data,
          studentId: seletedStuId,
          adminId: adminId.toString(),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        childMutate();
        adminMutate();

        setIsLoading(false);
        setIsOpen(false);
        toast.success(`LFT added successfully!`);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Faild to added LFT!");
    }
  };

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);

      setIsStuNotFound(false);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const SearchHandelar = {
    SelectStu: (student: tStudentL) => {
      setStudent(student.fullname);
      setSeletedStuId(student.id);
      setIsFocused(false);
      setIsStuNotFound(false);
    },
    SearchStu: (e: any) => {
      setStudent(e.target.value);
    },
  };

  useEffect(() => {
    if (!student) {
      setSeletedStuId("");
    }
  }, [student]);

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <div className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add LFT
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add LFT</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <div>
                <div
                  className={`pb-1 text-sm font-medium  ${
                    isStuNotFound ? "text-destructive" : "text-gray-800"
                  }`}
                >
                  Find A Student
                </div>
                <Input
                  type="search"
                  value={student}
                  placeholder="Select A Student"
                  onChange={SearchHandelar.SearchStu}
                  onFocus={() => setIsFocused(true)}
                  className="text-xs font-normal text-gray-800 placeholder:text-xs placeholder:font-medium placeholder:text-gray-700"
                />
                {student && (
                  <InputSelectorForModal
                    isOpen={isFocused}
                    searchValue={student}
                    onChange={SearchHandelar.SelectStu}
                    apiRoute="/student/search"
                  />
                )}

                {isStuNotFound && (
                  <div className="mt-1.5 text-xs font-medium text-destructive">
                    Student is required!
                  </div>
                )}
              </div>

              <FormField
                control={form.control}
                name="childId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select A Child</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full bg-white text-xs font-medium text-gray-700">
                          {(Children &&
                            Children.find((c) => c.id === field.value)?.name) ||
                            "Select A Child"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectGroup>
                          {Children && Children.length === 0 && (
                            <div className="p-1 text-[10px] font-medium text-blue-600">
                              No children found. Please add a child first!
                            </div>
                          )}
                          {Children &&
                            Children.map((child: any) => {
                              return (
                                <SelectItem key={child.id} value={child.id}>
                                  {child?.name}
                                </SelectItem>
                              );
                            })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="adminId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select An Admin</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full bg-white text-xs font-medium text-gray-700">
                          {(Admins &&
                            Admins.find((a) => a.id === field.value)
                              ?.fullName) ||
                            "Select An Admin"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectGroup>
                          {Admins &&
                            Admins.map((admin: tLftAdmin) => {
                              return (
                                <SelectItem key={admin.id} value={admin.id}>
                                  {admin.fullName}
                                </SelectItem>
                              );
                            })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="instrument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instrument</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full bg-white text-xs font-medium text-gray-700">
                          {field.value ? field.value : "Select An Instrument"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="hide-scrollbar max-h-56 overflow-y-scroll">
                        <SelectGroup className="text-xs font-normal text-gray-700">
                          <SelectItem value="ABRSM Music Theory Grades">
                            ABRSM Music Theory Grades
                          </SelectItem>
                          <SelectItem value="Bass Guitar">
                            Bass Guitar
                          </SelectItem>
                          <SelectItem value="Cello">Cello</SelectItem>
                          <SelectItem value="Clarinet">Clarinet</SelectItem>
                          <SelectItem value="Double Bass">
                            Double Bass
                          </SelectItem>
                          <SelectItem value="Drums">Drums</SelectItem>
                          <SelectItem value="Flute">Flute</SelectItem>
                          <SelectItem value="French Horn">
                            French Horn
                          </SelectItem>
                          <SelectItem value="Guitar">Guitar</SelectItem>
                          <SelectItem value="Harp">Harp</SelectItem>
                          <SelectItem value="Jazz Piano">Jazz Piano</SelectItem>

                          <SelectItem value="Oboe">Oboe</SelectItem>
                          <SelectItem value="Piano">Piano</SelectItem>

                          <SelectItem value="Sax">Sax</SelectItem>
                          <SelectItem value="Singing">Singing</SelectItem>
                          <SelectItem value="Trombone">Trombone</SelectItem>
                          <SelectItem value="Trumpet">Trumpet</SelectItem>
                          <SelectItem value="Viola">Viola</SelectItem>
                          <SelectItem value="Violin">Violin</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lessonType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full bg-white text-xs font-medium text-gray-700">
                          {field.value ? field.value : "Select A Lesson Type"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Unsure">Unsure</SelectItem>
                        <SelectItem value="Online">Online</SelectItem>
                        <SelectItem value="In-Person">In-Person</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
