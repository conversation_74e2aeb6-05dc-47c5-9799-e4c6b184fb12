"use client";

import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { ToggleGroup, ToggleGroupItem } from "@components/ui/toggle-group";

import { FaPlus } from "react-icons/fa";
import { Input } from "@components/ui/input";
import { useForm } from "react-hook-form";
import { Dots } from "react-activity";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";

import { Button } from "@components/ui/button";
import { tTutorL } from "types/tutor";
import InputSelectorForModal from "@components/modals/InputSelectorForModal";
import LftDetailsList, {
  AddHomeTutor,
  AddOnlineTutor,
  LftChild,
  LftHomeTutor,
  LftOnlineTutor,
  LftStudent,
} from "@components/Details/LftDetails";
import useSWR from "swr";
import { tLftAdmin, tLftD, tLftStatus, tSuggestedL } from "types/lft";
import { fetcher } from "@fetchers/fetcher";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { TbReplace } from "react-icons/tb";
import BoxSkeleton from "@components/skeltons/BoxSkeleton";

import Comment, { AddComment } from "@components/global/Comment";
import { tCommentL } from "types/comment";
import { MdCancelPresentation } from "react-icons/md";

//Draft
import {
  EditorState,
  convertToRaw,
  convertFromHTML,
  ContentState,
} from "draft-js";
import draftToHtml from "draftjs-to-html";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";

import { EditorProps } from "react-draft-wysiwyg";
import Delete from "@components/global/Delete";
import Avatar from "react-avatar";
import { GenerateQuery } from "core/Query";
import { FaMale, FaFemale } from "react-icons/fa";
import { GeoCoding } from "core/Map/Map";

import dynamic from "next/dynamic";
import { useParams, useRouter } from "next/navigation";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

// Dynamically import the Editor component from react-draft-wysiwyg
const Editor = dynamic<EditorProps>(
  () => import("react-draft-wysiwyg").then((mod: any) => mod.Editor),
  { ssr: false },
);

export default function Details() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const params = useParams<any>();
  const { id } = params;
  //   const id = router.query.id;

  //Data Fetching...

  const {
    data: Lft,
    isLoading,
    mutate,
    error,
  } = useSWR<tLftD>(id ? `/lft/${id}` : null, fetcher);

  const {
    data: Status,
    isLoading: isLoadingS,
    mutate: mutateS,
  } = useSWR<tLftStatus>(id ? `/lft/${id}/status` : null, fetcher);

  const commentsKey = Lft?.student.id ? `/comment/${Lft.student.id}` : null;

  // Data Fetching for Comments
  const {
    data: Comments,
    mutate: mutateC,
    isLoading: isLoadingC,
  } = useSWR<Array<tCommentL>>(Lft && !error ? commentsKey : null, fetcher);

  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const Generate = async () => {
    setIsGenerating(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const res = await api.post(
        `/lft/${Lft?.id}/generate`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (res.status === 200) {
        mutate();
        toast.success("Template generated successfully!");
        setIsGenerating(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      toast.error("Template generation failed. Please try again!");
      setIsGenerating(false);
    }
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Set page title ...
  useEffect(() => {
    document.title = `${Lft?.student.fullName ? Lft?.student.fullName : "LFT Details"} | MuseCool Dashboard`;
  }, [Lft?.student?.fullName]);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />
        <div className="flex h-full w-full flex-col">
          <div className="my-2 flex h-full w-full flex-row bg-[#f9f9f9] p-4">
            {/* Left Content */}
            <div className="hide-scrollbar flex h-full w-full flex-row overflow-y-scroll bg-[#f9f9f9]">
              <div className="h-fit flex-grow overflow-hidden bg-[#f9f9f9] pl-4 pr-6">
                <div className="flex w-full flex-row flex-wrap">
                  {/* // Student Info ... */}
                  <div className="my-2 mr-2.5">
                    <div className="mb-1 w-56 text-sm font-semibold  text-slate-600 ">
                      Student
                    </div>
                    {Lft && !isLoading ? (
                      <LftStudent
                        lft={Lft}
                        mutate={mutate}
                        mutateStatus={mutateS}
                      />
                    ) : (
                      <BoxSkeleton count={1} height={240} width={240} />
                    )}
                  </div>
                  {/* // Child Info  */}
                  {Lft && (
                    <div className="my-2 mr-2.5">
                      <div className="mb-1 w-56 text-sm font-semibold text-slate-600">
                        Child
                      </div>

                      {isLoading ? (
                        <BoxSkeleton count={1} height={240} width={240} />
                      ) : (
                        <LftChild
                          lft={Lft}
                          mutate={mutate}
                          mutateStatus={mutateS}
                        />
                      )}
                    </div>
                  )}

                  {/* // Home Tutor ... */}
                  <div className="my-2 mr-2.5">
                    <div className="mb-1 w-56 text-sm font-semibold text-slate-600">
                      Home Tutor
                    </div>
                    {Lft && !isLoading ? (
                      Lft?.homeTutor !== null ? (
                        <LftHomeTutor
                          lft={Lft}
                          mutate={mutate}
                          mutateStatus={mutateS}
                        />
                      ) : (
                        <div className="flex h-56 w-56 flex-col items-center justify-center rounded-md border border-slate-200 bg-white">
                          {Status && Status.canUpdateHomeTutor ? (
                            <AddHomeTutor
                              lft={Lft}
                              mutate={mutate}
                              mutateStatus={mutateS}
                            />
                          ) : (
                            <div
                              onClick={() =>
                                toast.error("Select Lesson Type First!")
                              }
                              className="flex h-24 w-24 cursor-pointer items-center justify-center rounded-full bg-slate-400"
                            >
                              <FaPlus size={28} color="white" />
                            </div>
                          )}
                        </div>
                      )
                    ) : (
                      <BoxSkeleton count={1} height={240} width={240} />
                    )}
                  </div>

                  {/* // Online Tutor */}
                  <div className="my-2 mr-2.5">
                    <div className="mb-1 w-56 text-sm font-semibold text-slate-600">
                      Online Tutor
                    </div>
                    {Lft && !isLoading ? (
                      Lft?.onlineTutor !== null ? (
                        <LftOnlineTutor
                          lft={Lft}
                          mutate={mutate}
                          mutateStatus={mutateS}
                        />
                      ) : (
                        <div className="flex h-56 w-56 flex-col items-center justify-center rounded-md border border-slate-200 bg-white">
                          {Status && Status.canUpdateOnlineTutor ? (
                            <AddOnlineTutor
                              lft={Lft}
                              mutate={mutate}
                              mutateStatus={mutateS}
                            />
                          ) : (
                            <div
                              onClick={() =>
                                toast.error("Select Lesson Type First...")
                              }
                              className="flex h-24 w-24 cursor-pointer items-center justify-center rounded-full bg-slate-400"
                            >
                              <FaPlus size={28} color="white" />
                            </div>
                          )}
                        </div>
                      )
                    ) : (
                      <BoxSkeleton count={1} height={240} width={240} />
                    )}
                  </div>
                </div>

                {Lft && !isLoading ? (
                  <LftDetailsList
                    lft={Lft}
                    mutate={mutate}
                    mutateStatus={mutateS}
                  />
                ) : (
                  <ListSkeltons height={80} count={1} />
                )}

                {Lft && Lft.content && (
                  <div className="mt-6">
                    <div className="mb-1 w-56 text-sm font-semibold  text-slate-600 ">
                      Content
                    </div>
                    <LftTemplate lft={Lft} mutate={mutate} />
                  </div>
                )}

                {/* //Delete  */}
                {Lft && (
                  <div className="my-5">
                    <Delete
                      apiUrl={`/lft/${Lft?.id}`}
                      mutate={mutate}
                      successMsg={`LFT of ${Lft.student.fullName} has been successfully deleted!`}
                      erroMsg="Faild to delete LFT!"
                      des="LFT details"
                      headerTitle="Remove"
                      useBtnUI
                      goBackUrl="/lft"
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="hide-scrollbar ml-6 h-auto w-80 overflow-y-scroll px-1">
              {Lft && <SendEmail lft={Lft} mutate={mutate} />}

              {/* //Generate Template */}
              <div
                onClick={() => {
                  if (!isGenerating) Generate();
                }}
                className={`mb-6 flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-sm font-medium text-blue-50 hover:bg-blue-600 ${
                  isGenerating ? "cursor-not-allowed opacity-50" : ""
                }`}
              >
                {isGenerating ? (
                  <>
                    <div className="mr-1 flex flex-row items-center justify-center text-xs font-medium text-blue-50">
                      Generating
                    </div>
                    <Dots />
                  </>
                ) : (
                  "Generate"
                )}
              </div>

              {/* //Problems */}
              {Status && Status.problems.length > 0 && (
                <div className="mb-6 mt-2">
                  <div className="mb-1 text-sm font-semibold text-slate-600">
                    Problems
                  </div>

                  {Status.problems.map((problem: string, index: any) => (
                    <div
                      key={index}
                      className="mb-1 flex w-full flex-row items-center space-x-1"
                    >
                      <MdCancelPresentation
                        size={16}
                        className="overflow-hidden text-destructive "
                      />
                      <div className="text-xs font-medium text-slate-500">
                        {problem}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* //Admin Details  */}

              {Lft && (
                <div className="relative mb-6 flex h-20 w-full items-center rounded-md border border-gray-200 bg-white px-2">
                  <div className="absolute right-2 top-1.5">
                    <ReplaceAdmin lft={Lft} mutate={mutate} />
                  </div>

                  <div className="flex flex-row items-center space-x-1.5">
                    <Avatar name={Lft?.admin.fullName} round={true} size="45" />
                    <div className="space-y-0.5">
                      <div className="max-w-[176px]  overflow-hidden truncate text-xs font-semibold text-slate-600">
                        {Lft?.admin.fullName}
                      </div>

                      <div className="max-w-[176px] overflow-hidden truncate text-[10px] font-normal text-slate-600">
                        {Lft?.admin.position}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* //Comment */}
              <div className="mb-1 text-sm font-semibold text-slate-600">
                Comments
              </div>

              {Lft && !isLoading ? (
                <>
                  <AddComment id={Lft.student.id} mutate={mutateC} />
                  {Comments !== undefined && Comments.length > 0 && (
                    <div className="mt-6 rounded border border-slate-200 bg-white  px-3 py-2">
                      {Comments.map((comment: tCommentL) => (
                        <Comment
                          key={comment.id}
                          comment={comment}
                          mutate={mutateC}
                        />
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <BoxSkeleton height={450} width={240} count={1} />
              )}
            </div>
          </div>
        </div>
      </Sidebar>
    );
}

type Props = {
  lft: tLftD;
  mutate: () => void;
};

function LftTemplate({ lft, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // For Draft Js
  const [editorState, setEditorState] = useState(EditorState.createEmpty());

  const onEditorStateChange = (editorState: EditorState) => {
    setEditorState(editorState);
  };

  let state: any;

  const blocksFromHTML = lft?.content ? convertFromHTML(lft.content) : null;

  // Check if 'blocksFromHTML' is not null before proceeding
  if (blocksFromHTML) {
    state = ContentState.createFromBlockArray(
      blocksFromHTML.contentBlocks,
      blocksFromHTML.entityMap,
    );
  }

  const resetFormAndEditor = () => {
    if (state) {
      setEditorState(EditorState.createWithContent(state));
    }
  };

  useEffect(() => {
    if (lft && lft.content) {
      setEditorState(EditorState.createWithContent(state));
    }
  }, [lft && lft.content]);
  return (
    <div>
      <Editor
        editorState={editorState}
        onEditorStateChange={onEditorStateChange}
        placeholder="Enter your content here..."
        editorClassName="lftEditor"
        toolbar={{
          options: [
            "inline",
            "blockType",
            // "fontSize",
            // "fontFamily",
            "list",
            "textAlign",
            // "image",
            "colorPicker",
            "link",
            // "embedded",
            "emoji",
            "remove",
            "history",
          ],
        }}
      />

      <div className="mt-4 flex w-full flex-row justify-end space-x-2">
        <Button
          type="reset"
          onClick={resetFormAndEditor}
          className="border border-black bg-white text-black hover:bg-black hover:text-white "
        >
          Reset
        </Button>
        <Button
          type="submit"
          className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
        >
          {isLoading ? <Dots /> : "Update"}
        </Button>
      </div>
    </div>
  );
}

function SendEmail({ lft, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [isOpen, setIsOpen] = useState(false);

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  const Action = {
    Cancel: () => {
      setIsOpen(false);
    },

    Submit: async () => {
      setIsLoading(true);
      try {
        const Session = await getSession();
        const token = Session?.token;

        const res = await api.post(
          `/lft/${lft.id}/email-client`,
          {},
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        );

        if (res.status === 200) {
          mutate();
          toast.success("Email sent successfully!");
          setIsLoading(false);
          setIsOpen(false);
        } else {
          toast.error("Unexpected response from the server!");
        }
      } catch (error: any) {
        toast.error("Sending email failed. Please try again!");
        setIsLoading(false);
      }
    },
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal} asChild>
        <div className="mb-6 flex h-9 w-full cursor-pointer items-center justify-center rounded bg-blue-500 text-sm font-medium text-blue-50 hover:bg-blue-600">
          {isLoading ? <Dots /> : "Send Email"}
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Are you sure?</DialogTitle>

          <DialogDescription>
            Please take a moment to confirm your action. This process cannot be
            undone. Review all the details carefully before proceeding.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <div
            onClick={() => {
              if (!isLoading) Action.Submit();
            }}
            className={`flex h-8 w-fit cursor-pointer items-center justify-center rounded border border-blue-800 bg-blue-800 px-4 text-sm font-medium text-white hover:border-blue-800 hover:bg-white hover:text-blue-800 ${
              isLoading ? "cursor-not-allowed opacity-50" : ""
            }`}
          >
            {isLoading ? (
              <>
                <div className="mr-1 text-xs font-medium">Sending Email</div>
                <Dots />
              </>
            ) : (
              "Send"
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function ReplaceAdmin({ lft, mutate }: Props) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const {
    data: Admins,
    isLoading: adminLoading,
    mutate: adminMutate,
  } = useSWR<Array<tLftAdmin>>("/lft/admin-list", fetcher);

  var resolver = yup.object().shape({
    id: yup.string().required("Admin is required!"),
  });

  const defaultValues = {
    id: lft.admin.id,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },

    openModal: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);

    try {
      const Session = await getSession();
      const token = await Session?.token;

      const res = await api.put(
        `/lft/${lft.id}/replace-admin`,
        { ...data },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (res.status === 200) {
        mutate();
        adminMutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success(`Admin has been replaced successfully!`);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);

      toast.error(`Failed to replace admin. Please try again!`);
    }
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <TbReplace size={15} className="cursor-pointer text-slate-700" />
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Admin Replacement</DialogTitle>
          <DialogDescription className="text-center">
            {lft.admin.fullName}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-3.5">
              <FormField
                control={form.control}
                name="id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select A New Admin</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full  bg-white">
                          {(Admins &&
                            Admins.find((a) => a.id === field.value)
                              ?.fullName) ||
                            "Select An Admin"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectGroup>
                          {Admins &&
                            Admins.map((admin: tLftAdmin) => {
                              return (
                                <SelectItem key={admin.id} value={admin.id}>
                                  {admin.fullName}
                                </SelectItem>
                              );
                            })}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* //Action Buttons  */}
            <div className="mt-6 flex w-full flex-row justify-end space-x-2.5">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>

              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Replace"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
