"use client";

import Header from "@components/Header";

import Sidebar from "@components/Sidebar";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import ConfirmedBooking, {
  ConfirmedBookingHeader,
} from "@components/lists/Product/ConfirmedBooking";
import { tConfirmBookingL, tPgConfirmBookingL } from "types/product";
import useSWR from "swr";
import { pgfetcher } from "@fetchers/fetcher";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import PageList from "@components/PageList";
import { DataGrabber } from "core/Excel/exportBookings";

import dynamic from "next/dynamic";
import { useRouter, useSearchParams } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function ConfirmedBookings() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const id = useSearchParams().get("id");
  //   const { id } = router.query;

  const [page, setPage] = useState<number>(1);

  //Data Fetching ...
  const {
    data: Bookings,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgConfirmBookingL>(
    `/product/bookings/${id}?p=${page}`,
    pgfetcher,
  );

  // console.log("Confirm Bookings", Bookings);

  const Action = {
    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}`;
      //   router.push("/product/confirmed-bookings", queryWithPage, {
      //     shallow: true,
      //   });
      router.push(`/products/confirmed-bookings${queryWithPage}`);
      setPage(newPage);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Confirmed Bookings | MuseCool Admin";

    var search = new URLSearchParams(window.location.search);

    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-6 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Confirmed Bookings
            </div>

            <div className="flex flex-row items-center justify-end">
              <div
                onClick={() => DataGrabber(id?.toString() || "")}
                className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-4 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                <p>Export</p>
              </div>
            </div>
          </div>

          <ConfirmedBookingHeader />

          {isLoading ? (
            <ListSkeltons height={50} count={25} />
          ) : (
            Bookings?.data.map((booking: tConfirmBookingL) => (
              <ConfirmedBooking
                key={booking.id}
                booking={booking}
                mutate={mutate}
              />
            ))
          )}

          <PageList page={Bookings?.page} handler={Action.UpdatePage} />
        </div>
      </Sidebar>
    );
}
