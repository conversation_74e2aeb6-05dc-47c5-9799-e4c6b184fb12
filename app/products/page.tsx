"use client";

import Header from "@components/Header";

import Sidebar from "@components/Sidebar";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import useSWR from "swr";
import { tPgProductL, tProductL } from "types/product";
import { pgfetcher } from "@fetchers/fetcher";
import PageList from "@components/PageList";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from "components/ui/select";
import { GenerateQuery } from "core/Query";
import Product, { ProductListHeader } from "@components/lists/Product";
import axios from "axios";
import toast from "react-hot-toast";
import { MdOutlineSync } from "react-icons/md";
import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Products() {
  //Routing Hooks
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  const [country, setCountry] = useState<string>("");
  const [type, setType] = useState<string>("");

  const [syncUKLoading, setSyncUKLoading] = useState<boolean>(false);
  const [syncUSLoading, setSyncUSLoading] = useState<boolean>(false);

  //Data Fetching ...
  const {
    data: Products,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgProductL>(`/product?p=${page}${query}`, pgfetcher);

  const Action = {
    UpdateQuery: (query: string) => {
      let queryWithPage = `?p=${page}${query}`;
      //   router.push("/product", queryWithPage, { shallow: true });
      router.push(`/products${queryWithPage}`);
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/product", queryWithPage, { shallow: true });
      router.push(`/products${queryWithPage}`);
      setPage(newPage);
    },
  };

  const FilterHandelar = {
    UpdateCountry: (e: any) => {
      setCountry(e);
    },

    UpdateType: (e: any) => {
      setType(e);
    },
  };

  const Sync = {
    US: async () => {
      setSyncUSLoading(true);
      try {
        const response = await axios.post(
          "https://musecool.com/us/wp-json/musecool/v1/update-events",
          null,
          {
            headers: {
              Authorization: `Bearer b5742769asd213a437a4d409d9db71d8f8ef93b5e88b330asds923bc7aad3b16b8f70`,
            },
          },
        );

        if (response.status === 200) {
          setSyncUSLoading(false);
          toast.success("Sync completed!");
        } else {
          setSyncUSLoading(false);
          toast.error("Unexpected response from the server!");
        }
      } catch (error: any) {
        setSyncUSLoading(false);
        // console.error("Error updating events:", error);
        toast.error("Sync failed! Please try again.");
      }
    },
    UK: async () => {
      setSyncUKLoading(true);
      try {
        const response = await axios.post(
          "https://musecool.com/uk/wp-json/musecool/v1/update-events",
          null,
          {
            headers: {
              Authorization: `Bearer b5742769asd213a437a4d409d9db71d8f8ef93b5e88b330asds923bc7aad3b16b8f70`,
            },
          },
        );

        if (response.status === 200) {
          setSyncUKLoading(false);
          toast.success("Sync completed!");
        } else {
          setSyncUKLoading(false);
          toast.error("Unexpected response from the server!");
        }
      } catch (error: any) {
        setSyncUKLoading(false);
        // console.error("Error updating events:", error);
        toast.error("Sync failed! Please try again.");
      }
    },
  };

  const generateQuery = () => {
    const queryParams = { country, type };
    Action.UpdateQuery(GenerateQuery(queryParams));
  };

  useEffect(() => {
    if (isActive) {
      generateQuery();
    }
  }, [country, type]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Products | MuseCool Dashboard";

    setIsActive(false);

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-6 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Products
            </div>

            <div className="flex flex-row items-center justify-end space-x-2.5">
              <div className="w-44">
                <Select
                  onValueChange={FilterHandelar.UpdateCountry}
                  defaultValue={country}
                >
                  <SelectTrigger className=" w-full rounded-sm border border-slate-300 bg-white">
                    <div className="">{country ? country : "Both"}</div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-gray-700">
                    <SelectGroup>
                      <SelectItem value={null as any}>Both</SelectItem>

                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div className="w-44">
                <Select
                  onValueChange={FilterHandelar.UpdateType}
                  defaultValue={type}
                >
                  <SelectTrigger className="w-full rounded-sm border border-slate-300 bg-white">
                    <div>{type ? type : "Both"}</div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-gray-700">
                    <SelectGroup>
                      <SelectItem value={null as any}>Both</SelectItem>

                      <SelectItem value="Event">Event</SelectItem>
                      <SelectItem value="Course">Course</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div
                onClick={Sync.US}
                className="flex h-9 w-20 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                {syncUSLoading ? (
                  <MdOutlineSync
                    size={17}
                    className="animate-spin text-blue-50"
                  />
                ) : (
                  "Sync US"
                )}
              </div>

              <div
                onClick={Sync.UK}
                className="flex h-9 w-20 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                {syncUKLoading ? (
                  <MdOutlineSync
                    size={17}
                    className="animate-spin text-blue-50"
                  />
                ) : (
                  "Sync UK"
                )}
              </div>

              <Link
                href={`/products/add-product`}
                target="_blank"
                className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                Add Product
              </Link>
            </div>
          </div>

          <ProductListHeader />

          {isLoading ? (
            <ListSkeltons height={50} count={25} />
          ) : (
            Products?.data.map((product: tProductL) => (
              <Product key={product.id} product={product} mutate={mutate} />
            ))
          )}

          <PageList page={Products?.page} handler={Action.UpdatePage} />
        </div>
      </Sidebar>
    );
}
