"use client";

import React, { useEffect, useState } from "react";
import Header from "@components/Header";

import Sidebar from "@components/Sidebar";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Input } from "@components/ui/input";
import { Dots } from "react-activity";
import { Button } from "@components/ui/button";

//Rich Text Editor....

import axios from "axios";
// import "jodit";
const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });

import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { getSession, useSession } from "next-auth/react";
import api from "fetchers/BaseUrl";
import { MdAddCircle } from "react-icons/md";
import toast from "react-hot-toast";
import { RiDeleteBin6Line } from "react-icons/ri";
import moment from "moment";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

const buttons = [
  // "undo",
  // "redo",
  // "|",
  "bold",
  "strikethrough",
  "underline",
  "italic",
  "|",
  "superscript",
  "subscript",
  "|",
  "align",
  "|",
  "ul",
  "ol",
  "outdent",
  "indent",
  "|",
  "font",
  "fontsize",
  "brush",
  "paragraph",
  "|",
  "image",
  "link",
  "table",
  "|",
  "hr",
  "eraser",
  "copyformat",
  "|",
  "fullsize",
  "selectall",
  "print",
  "|",
  "source",
  "|",
];

const editorConfig = {
  readonly: false,
  toolbar: true,
  spellcheck: true,
  language: "en",
  toolbarAdaptive: false,
  // toolbarButtonSize: "small", // Set to an appropriate size (e.g., "small", "medium", etc.)
  showCharsCounter: false,
  showWordsCounter: false,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  // defaultActionOnPaste: "insert_clear_html",
  buttons: buttons, // Make sure you define the 'buttons' array
  uploader: {
    insertImageAsBase64URI: true,
    imagesExtensions: ["jpg", "png", "jpeg", "gif", "svg", "webp"],
  },
  width: "100%",
  height: "auto",
  minHeight: "450px",

  theme: "default",
};

type tTicket = {
  title: string;
  price: number;
  quantity: number;
};

export default function AddProduct() {
  const router = useRouter();
  const { data: session, status } = useSession<any>();

  var resolver = yup.object().shape({
    title: yup.string().required("Title is required!"),
    country: yup.string().required("Country is required!"),
    type: yup.string().required("Type is required!"),
    category: yup.string().required("Category is required!"),
    start: yup.string().required("Date is required!"),
  });

  const defaultValues = {
    title: "",
    country: "",
    type: "",
    category: "",
    start: new Date(),
    end: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [editorState, setEditorState] = useState("");

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);

  const [showTickets, setShowTickets] = useState<boolean>(false);
  const [tickets, setTickets] = useState<tTicket[]>([]);

  const [ticket, setTicket] = useState<{
    title: string;
    price: string;
    quantity: string;
  }>({
    title: "",
    price: "",
    quantity: "",
  });

  const Action = {
    OnSubmit: async (data: any) => {
      if (showTickets) {
        toast.error("Ticket incomplete! Please fill all fields.");
        return;
      } else {
        setIsLoading(true);
        const { start, end } = data;

        // console.log("Data ", data);

        // console.log("Ticket", tickets);
        // console.log("Details (Rich Editor)", editorState);
        // console.log("Start Date", moment(start).format("YYYY-MM-DD"));

        try {
          const session = await getSession();
          const token = session?.token;

          var imgUrl: any;
          // Create a new FormData object for upload image
          if (selectedFile) {
            const formData = new FormData();
            formData.append("image", selectedFile as any);
            formData.append("key", "b9c236a6bcebb4b78487c74f94bfdba8");

            try {
              const response = await axios.post(
                "https://api.imgbb.com/1/upload",
                formData,
              );

              // console.log("Response ", response);
              // console.log("Response URL ", response.data.data.display_url);
              if (response.status === 200) {
                imgUrl = response.data.data.url;
                // setImgUrl(response.data.data.url);
                toast.success("Image uploaded successfully!");
              } else {
                toast.error("Unexpected response from the server!");
              }
            } catch (error) {
              console.error("Image upload failed:", error);
              toast.error("Image upload failed. Please try again!");
              return null;
            }
          }

          // console.log("Image URL", imgUrl);

          const response = await api.post(
            "/product",
            {
              ...data,
              start: moment(start).format("YYYY-MM-DD"),
              end: end ? moment(end).format("YYYY-MM-DD") : null,
              thumbnail: imgUrl ? imgUrl : "",
              details: editorState,
              tickets: tickets,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            },
          );

          if (response.status === 200) {
            setIsLoading(false);
            toast.success("Product added successfully!");
            router.push("/products");
          } else {
            toast.error("Unexpected response from the server!");
          }
        } catch (e: any) {
          setIsLoading(false);
          console.log("Error: ", e);
          toast.error("Failed to added product!");
        }
      }
    },
  };

  // Upload Image In imaBB...
  const handleImageUpload = async (file: any) => {
    const formData = new FormData();
    formData.append("image", file);
    formData.append("key", "b9c236a6bcebb4b78487c74f94bfdba8");

    try {
      const response = await axios.post(
        "https://api.imgbb.com/1/upload",
        formData,
      );

      console.log("Response(imgBB) Under Rich Editor", response);
      console.log(
        "Response URL(imgBB) Under Rich Editor",
        response.data.data.display_url,
      );
      return response.data.data.url;
    } catch (error) {
      console.error("Image upload failed:", error);
      return null;
    }
  };

  const handleEditorChange = async (value: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(value, "text/html");
    const images = doc.querySelectorAll("img");

    for (let img of images) {
      if (img.src.startsWith("data:")) {
        const file = await fetch(img.src).then((res) => res.blob());
        const imageUrl = await handleImageUpload(file);
        if (imageUrl) {
          img.src = imageUrl;
        }
      }
    }

    setEditorState(doc.body.innerHTML);
  };

  const handleFileChange = (e: any) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // function isValidTicket(ticket: tTicket) {
  //   return (
  //     ticket.title && // Checks if the title exists and is not null or undefined
  //     ticket.title.trim() !== "" && // Ensures the title is not just empty spaces
  //     ticket.price >= 0 && // Validates that price is a non-negative number (0 or greater)
  //     ticket.quantity >= 0 // Validates that quantity is a non-negative number (0 or greater)
  //   );
  // }

  function isValidTicket(ticket: tTicket) {
    return (
      ticket.title && // Checks if the title exists and is not null or undefined
      ticket.title.trim() !== "" && // Ensures the title is not just empty spaces
      !isNaN(ticket.price) &&
      ticket.price >= 0 && // Validates that price is a non-negative number (0 or greater)
      !isNaN(ticket.quantity) &&
      ticket.quantity >= 0 // Validates that quantity is a non-negative number (0 or greater)
    );
  }

  const handleAddTicket = () => {
    // console.log("Under Submission Ticket", ticket);

    const price = parseFloat(ticket.price);
    const quantity = parseInt(ticket.quantity, 10);

    if (isNaN(price) || isNaN(quantity)) {
      toast.error(
        `Please enter a valid number for ${
          isNaN(price) && isNaN(quantity)
            ? "both price and quantity!"
            : isNaN(price)
              ? "price!"
              : "quantity!"
        }`,
      );
      return;
    }

    const isValid = isValidTicket({
      title: ticket.title,
      price: price,
      quantity: quantity,
    });

    if (isValid) {
      setTickets([...tickets, { title: ticket.title, price, quantity }]);
      setTicket({ title: "", price: "", quantity: "" });
      setShowTickets(false);
    } else {
      toast.error("All fields are required for each ticket!");
    }
  };

  const handleRemoveTicket = (index: number) => {
    setTickets(tickets.filter((_, i) => i !== index));
  };

  // console.log("Tickets", tickets);

  const watchType = form.watch("type");

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Add Product | MuseCool Admin";
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="flex h-full w-full flex-col">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(Action.OnSubmit)}
              className="flex h-full w-full flex-col p-4 pb-24"
            >
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="w-2/3 pb-10">
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter title"
                        {...field}
                        className="bg-white text-xs font-normal text-slate-600"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex h-full w-full flex-row">
                {/* //Left  */}
                <div className="hide-scrollbar h-full w-2/3 overflow-y-auto bg-[#f9f9f9]">
                  <FormLabel>Details</FormLabel>
                  {/* //Details (Rich Text Editor) */}

                  <div
                    style={{
                      maxWidth: editorConfig.width,
                      minHeight: "200px",
                      margin: "5px auto",

                      fontSize: "13px",
                      lineHeight: "250%",
                      fontWeight: "400",
                    }}
                  >
                    <JoditEditor
                      value={editorState}
                      config={editorConfig}
                      // onChange={(value: any) => setEditorState(value)}
                      onChange={handleEditorChange}
                      className="editor"
                    />
                  </div>
                </div>

                {/* //Right  */}
                <div className="hide-scrollbar h-full w-1/3 space-y-5 overflow-y-auto pl-10 pr-1">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              {field.value ? field.value : "Select Country"}
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="United Kingdom">
                              United Kingdom
                            </SelectItem>
                            <SelectItem value="United States">
                              United States
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem className="">
                        <FormLabel>Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <div>
                                {field.value ? field.value : "Select Type"}
                              </div>
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="text-xs font-normal text-gray-700">
                            <SelectGroup>
                              <SelectItem value="Event">Event</SelectItem>
                              <SelectItem value="Course">Course</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white">
                              <div>
                                {field.value ? field.value : "Select Category"}
                              </div>
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="text-xs font-normal text-gray-700">
                            {watchType === "" ? (
                              <div className="px-2.5 py-1.5 text-center text-red-500">
                                Please select a type first!
                              </div>
                            ) : watchType === "Event" ? (
                              <SelectGroup>
                                <SelectItem value="Concerts">
                                  Concerts
                                </SelectItem>
                                <SelectItem value="Workshops">
                                  Workshops
                                </SelectItem>
                                <SelectItem value="Competition">
                                  Competition
                                </SelectItem>
                                <SelectItem value="ABRSM Mock Exam">
                                  ABRSM Mock Exam
                                </SelectItem>
                              </SelectGroup>
                            ) : (
                              <SelectGroup>
                                <SelectItem value="From 5-7 yo">
                                  From 5-7 yo
                                </SelectItem>
                                <SelectItem value="From 7-12 yo">
                                  From 7-12 yo
                                </SelectItem>
                                <SelectItem value="Music Theory Courses">
                                  Music Theory Courses
                                </SelectItem>
                              </SelectGroup>
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex flex-row items-center space-x-2.5">
                    <FormField
                      control={form.control}
                      name="start"
                      render={({ field }) => (
                        <FormItem className="flex w-1/2 flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <FormControl>
                            <ReactDatePicker
                              dateFormat="yyyy-MM-dd"
                              selected={field.value}
                              onChange={field.onChange}
                              className="mt-1 h-10 w-full cursor-pointer rounded-md border border-gray-200 px-2.5 text-xs font-medium text-gray-600 focus:outline-none"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="end"
                      render={({ field }) => (
                        <FormItem className="flex w-1/2 flex-col">
                          <FormLabel>End Date</FormLabel>
                          <FormControl>
                            <ReactDatePicker
                              dateFormat="yyyy-MM-dd"
                              selected={field.value}
                              onChange={field.onChange}
                              className="mt-1 h-10 w-full cursor-pointer rounded-md border border-gray-200 px-2.5 text-xs font-medium text-gray-600 focus:outline-none"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* //Image Uploader */}
                  <div>
                    <input
                      type="file"
                      onChange={handleFileChange}
                      className="block w-full text-sm text-gray-500 file:mr-4 file:h-9 file:cursor-pointer file:rounded file:border-0 file:border-gray-400 file:bg-white file:px-3 file:text-sm file:font-medium file:text-gray-600"
                    />

                    {preview && (
                      <img
                        src={preview}
                        alt="Preview"
                        width="100"
                        height="100"
                        className="mt-4 rounded"
                      />
                    )}
                    {/* <Image
                      src={selectedFile}
                      alt="Preview"
                      width={500}
                      height={500}
                    /> */}
                  </div>
                  {/* //Tickes */}
                  <div className="pb-10">
                    <div className="flex items-center pb-2.5">
                      <FormLabel>Tickets</FormLabel>
                      <div
                        onClick={() => {
                          setShowTickets(true);
                        }}
                        // onClick={addTicket}
                        className="ml-1 cursor-pointer text-slate-400"
                      >
                        <MdAddCircle size={22} />
                      </div>
                    </div>

                    {/* //Show Tickes */}
                    {tickets.map((t: tTicket, index: number) => (
                      <div
                        key={index}
                        className="mb-2.5 grid h-fit w-full grid-cols-5 items-center gap-x-5 rounded-md border border-slate-200 bg-white px-4 py-2.5"
                      >
                        <div className="col-span-2 space-y-1.5">
                          <div className=" text-xs font-semibold text-slate-600">
                            Title
                          </div>
                          <div className=" max-w-40 overflow-hidden truncate text-[10.5px] font-medium text-slate-500">
                            {t?.title}
                          </div>
                        </div>

                        <div className="space-y-1.5">
                          <div className="text-xs font-semibold text-slate-600">
                            Price
                          </div>
                          <div className="text-[10.5px] font-medium text-slate-500">
                            {t?.price}
                          </div>
                        </div>

                        <div className="space-y-1.5">
                          <div className="text-xs font-semibold text-slate-600">
                            Quantity
                          </div>
                          <div className="text-[10.5px] font-medium text-slate-500">
                            {t?.quantity}
                          </div>
                        </div>

                        <div
                          onClick={() => handleRemoveTicket(index)}
                          className="flex cursor-pointer flex-row items-center justify-center"
                        >
                          <RiDeleteBin6Line size={16} color="#ff0000" />
                        </div>
                      </div>
                    ))}

                    {/* //Ticket Input Fields */}
                    {showTickets && (
                      <div>
                        <div className="mt-4">
                          <div>
                            <FormLabel>Title</FormLabel>
                            <Input
                              type="text"
                              placeholder="Ticket title"
                              value={ticket.title}
                              onChange={(e) =>
                                setTicket({
                                  ...ticket,
                                  title: e.target.value,
                                })
                              }
                              className="bg-white text-xs font-normal text-slate-600"
                            />
                          </div>

                          <div className="flex flex-row items-center space-x-2.5 py-4">
                            <div className="w-1/2">
                              <FormLabel>Price</FormLabel>
                              <Input
                                type="number"
                                placeholder="Ticket price"
                                value={ticket.price}
                                onChange={(e) =>
                                  setTicket({
                                    ...ticket,
                                    price: e.target.value,
                                  })
                                }
                                // onKeyDown={(e) => {
                                //   if (["e", "E", "+", "-"].includes(e.key)) {
                                //     e.preventDefault();
                                //   }
                                // }}
                                className="bg-white text-xs font-normal text-slate-600"
                              />
                            </div>

                            <div className="w-1/2">
                              <FormLabel>Quantity</FormLabel>
                              <Input
                                type="number"
                                placeholder="Ticket quantity"
                                value={ticket.quantity}
                                onChange={(e) =>
                                  setTicket({
                                    ...ticket,
                                    quantity: e.target.value,
                                  })
                                }
                                // onKeyDown={(e) => {
                                //   if (
                                //     ["e", "E", "+", "-", "."].includes(e.key)
                                //   ) {
                                //     e.preventDefault();
                                //   }
                                // }}
                                className="bg-white text-xs font-normal text-slate-600"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="mt-1.5 flex w-full flex-row justify-end">
                          <div
                            onClick={handleAddTicket}
                            className="flex h-8 w-16 cursor-pointer items-center justify-center rounded bg-gray-200  text-xs font-semibold text-gray-600 hover:bg-gray-300"
                          >
                            ADD
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  {/* //Submit Button Section  */}
                  <div className="pb-10">
                    <Button
                      type="submit"
                      className="w-full border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                    >
                      {isLoading ? <Dots /> : "Submit"}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </Sidebar>
    );
}
