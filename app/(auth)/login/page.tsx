"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import toast from "react-hot-toast";
import { signIn, useSession } from "next-auth/react";
import GrayLogo from "@assets/logo-gray.png";
import background from "@assets/music.jpg";

import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import { Dots } from "react-activity";
import { Button } from "@components/ui/button";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Login() {
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  const { data: session, status } = useSession();
  const router = useRouter();

  const handleSubmit = async () => {
    setIsLoading(true);
    setIsError(false);

    const res: any = await signIn("credentials", {
      email: email,
      password: password,
      redirect: false,
    });

    setIsLoading(false);

    if (res.ok) {
      router.push("/");
    } else {
      setIsError(true);
      toast("Failed to login! You have entered a wrong Email or Password!", {
        icon: "❌",
        style: {
          borderRadius: "10px",
          background: "#333",
          color: "#fff",
          fontSize: 13,
        },
        position: "top-right",
      });
    }
  };

  useEffect(() => {
    if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Login | MuseCool Dashboard";
  }, []);

  if (status === "loading") {
    return <Loading />;
  } else if (status === "authenticated") {
    return <Loading />;
  } else
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex h-full w-full flex-row items-center justify-center bg-white">
          <div className="hidden h-full sm:flex sm:w-[53%] sm:items-center sm:justify-center  sm:bg-black lg:w-3/5">
            <Image
              src={background}
              alt="MuseCool Music"
              className="h-full w-full object-cover opacity-70"
              priority
            />
          </div>
          <div className="flex h-full  w-full items-center justify-center sm:w-[47%] lg:w-2/5">
            {/* Login Container */}
            <div className="w-3/4 sm:w-4/6 lg:w-[55%]">
              <div className="mb-5 flex items-center justify-center">
                <Image
                  src={GrayLogo}
                  alt="MuseCool Music Ltd."
                  height={200}
                  width={200}
                />
              </div>

              <div className="text-center text-sm font-bold text-slate-600">
                Login To Your Account
              </div>
              <div className="mt-1.5 text-center text-xs font-normal text-slate-600">
                Plese login here to access your account
              </div>

              {/*Login  Form */}
              <div className="mt-6 w-full">
                <Label>Email</Label>

                <Input
                  type="email"
                  placeholder="Email Address"
                  value={email}
                  onChange={(e: any) => setEmail(e.target.value)}
                  className="mb-3.5 text-xs font-normal text-slate-600"
                />

                <Label>Password</Label>

                <Input
                  placeholder="Password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="mb-2.5 text-xs font-normal text-slate-600"
                />

                <Button
                  type="submit"
                  className={`mt-6 w-full ${
                    isError
                      ? "bg-red-600"
                      : isLoading
                        ? "bg-gray-700"
                        : "bg-black"
                  }`}
                  onClick={handleSubmit}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-1.5">
                      <span>Authenticating</span> <Dots />
                    </div>
                  ) : (
                    "Login"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}
