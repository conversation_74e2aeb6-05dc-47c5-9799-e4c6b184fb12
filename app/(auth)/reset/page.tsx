"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import toast from "react-hot-toast";
import { useSession } from "next-auth/react";
import GrayLogo from "assets/logo-gray.png";
import background from "assets/music.jpg";
import { useForm } from "react-hook-form";
// import api from "../fetchers/BaseUrl";
import api from "@fetchers/BaseUrl";

import { Dots } from "react-activity";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";

import dynamic from "next/dynamic";
import { useRouter, useSearchParams } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Reset() {
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState<boolean>(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams?.get("email");
  const code = searchParams?.get("code");
  //   const params = useParams<any>();
  //   const { email, code } = params;

  const { status } = useSession();

  var resolver = yup.object().shape({
    password: yup
      .string()
      .required("Password is required")
      .min(6, "Password should be at least 6 characters long"),

    confirmPassword: yup
      .string()
      .required("Confirm password is required!")
      .oneOf([yup.ref("password")], "Passwords do not match"),
  });

  const form = useForm<any>({ resolver: yupResolver(resolver) });

  const Action = {
    onSubmit: async (data: any) => {
      setIsLoading(true);
      setIsError(false);

      const { password } = data;

      console.log("Password:- ", password, code, email);

      try {
        let res = await api.post("/auth/resetpass", {
          email: email,
          code: code,
          password: password,
        });

        if (res.status === 200) {
          setIsLoading(false);
          toast.success(
            "You have successfully done resetting your password. Please login to your account!.",
          );
          router.push("/login");
        }
      } catch (error: any) {
        setIsLoading(false);
        setIsError(true);
      }
    },
  };

  useEffect(() => {
    if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Reset Password | MuseCool Dashboard";
  }, []);

  if (status === "loading") {
    return <Loading />;
  } else if (status === "authenticated") {
    return <Loading />;
  } else
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex h-full w-full flex-row items-center justify-center bg-white">
          <div className="hidden h-full sm:flex sm:w-[53%] sm:items-center sm:justify-center  sm:bg-black lg:w-3/5">
            <Image
              src={background}
              alt="MuseCool Music"
              className="h-full w-full object-cover opacity-70"
              priority
            />
          </div>

          <div className="flex h-full  w-full items-center justify-center sm:w-[47%] lg:w-2/5">
            <div className="w-3/4 sm:w-4/6 lg:w-[55%]">
              <div className="mb-5 flex items-center justify-center">
                <Image
                  src={GrayLogo}
                  alt="MuseCool Music Ltd."
                  height={200}
                  width={200}
                />
              </div>

              <div className="mb-2.5 text-center text-sm font-bold text-slate-600">
                Reset Your Password
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(Action.onSubmit)}>
                  <div className="mt-6 w-full space-y-3.5">
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>

                          <FormControl>
                            <Input
                              type="password"
                              placeholder="Password"
                              {...field}
                              className="text-xs font-normal text-slate-600"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirm Password</FormLabel>

                          <FormControl>
                            <Input
                              type="password"
                              placeholder="Confirm Password"
                              {...field}
                              className="text-xs font-normal text-slate-600"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    className={`mt-6 w-full ${
                      isError
                        ? "bg-red-600"
                        : isLoading
                          ? "bg-gray-700"
                          : "bg-black"
                    }`}
                    // onClick={handleSubmit(Action.onSubmit)}
                  >
                    {isLoading ? (
                      <div className="flex items-center space-x-1.5">
                        <span>Please wait</span> <Dots />
                      </div>
                    ) : (
                      "Reset Password"
                    )}
                  </Button>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
    );
}
