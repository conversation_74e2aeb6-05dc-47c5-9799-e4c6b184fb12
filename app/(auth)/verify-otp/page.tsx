"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import <PERSON><PERSON><PERSON> from "assets/logo-gray.png";
import background from "assets/music.jpg";
import { Dots } from "react-activity";
import api from "@fetchers/BaseUrl";
import { toast } from "react-hot-toast";
import { useSession } from "next-auth/react";

import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "components/ui/input-otp";
import { Button } from "@components/ui/button";

import dynamic from "next/dynamic";
import { useRouter, useSearchParams } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function VerifyOtp() {
  const [code, setCode] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams?.get("email");
  const { status } = useSession();
  // console.log(email);

  const Action = {
    OTPVerify: async () => {
      setIsLoading(true);
      setIsError(false);

      try {
        let res = await api.post("/auth/verifycode", { email, code });

        if (res.status === 200) {
          setIsLoading(false);
          router.push(`/reset?email=${email}&code=${code}`);

          console.log("Successfully verify the code.");
        } else {
          toast.error("Unexpected response from the server!");
        }
      } catch (error: any) {
        setIsLoading(false);
        setIsError(true);

        toast.error(
          "Wrong Code!.\nYou have either entered a wrong code or the code is expired!. \nPlease enter the code carefully or request for a new one.",
          { duration: 3000 },
        );
      }
    },

    ResedCode: () => {
      router.push("/reset");
    },
  };

  useEffect(() => {
    if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Code Verification | MuseCool Dashboard";
  }, []);

  if (status === "loading") {
    return <Loading />;
  } else if (status === "authenticated") {
    router.push("/");
    return <Loading />;
  } else
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex h-full w-full flex-row items-center justify-center bg-white">
          {/* // Left Section */}
          <div className="hidden h-full sm:flex sm:w-[53%] sm:items-center sm:justify-center  sm:bg-black lg:w-3/5">
            <Image
              src={background}
              alt="MuseCool Music Ltd."
              className="h-full w-full object-cover opacity-70"
              priority
            />
          </div>
          {/* // Right Section  */}
          <div className="flex h-full  w-full items-center justify-center sm:w-[47%] lg:w-2/5">
            {/* //Login */}
            <div className="w-3/4 sm:w-4/6 lg:w-[55%]">
              <div className="mb-5 flex items-center justify-center">
                <Image
                  src={GrayLogo}
                  alt="MuseCool Music Ltd."
                  height={200}
                  width={200}
                />
              </div>

              <div className="mt-1 text-center text-xs font-normal text-slate-600">
                A code (OTP) has been sent to
                <span className="font-semibold"> {email}</span>. Please check
                your email inbox (including the spam / junk folder)
              </div>

              <div className="relative mt-5 w-full">
                <div className="block text-sm font-semibold text-slate-600">
                  Verification Code
                </div>
                <div className="mt-1">
                  <InputOTP
                    maxLength={6}
                    value={code}
                    onChange={(e: any) => setCode(e)}
                  >
                    <InputOTPGroup>
                      <InputOTPSlot className="w-7 lg:w-9" index={0} />
                      <InputOTPSlot className="w-7 lg:w-9" index={1} />
                      <InputOTPSlot className="w-7 lg:w-9" index={2} />
                    </InputOTPGroup>
                    <InputOTPSeparator />
                    <InputOTPGroup>
                      <InputOTPSlot className="w-7 lg:w-9" index={3} />
                      <InputOTPSlot className="w-7 lg:w-9" index={4} />
                      <InputOTPSlot className="w-7 lg:w-9" index={5} />
                    </InputOTPGroup>
                  </InputOTP>
                </div>

                <div
                  className="block cursor-pointer pt-2.5 text-[11px] font-medium text-slate-600 hover:underline"
                  onClick={Action.ResedCode}
                >
                  Resend Code
                </div>

                <Button
                  type="submit"
                  className={`mt-6 w-full ${
                    isError
                      ? "bg-red-600"
                      : isLoading
                        ? "bg-gray-700"
                        : "bg-black"
                  }`}
                  onClick={Action.OTPVerify}
                >
                  {isLoading ? <Dots /> : "Verify OTP"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}
