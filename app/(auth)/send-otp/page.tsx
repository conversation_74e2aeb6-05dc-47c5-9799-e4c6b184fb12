"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import <PERSON><PERSON><PERSON> from "assets/logo-gray.png";
import background from "assets/music.jpg";
import api from "@fetchers/BaseUrl";
import { toast } from "react-hot-toast";
import { useSession } from "next-auth/react";

import { Label } from "@components/ui/label";
import { Input } from "@components/ui/input";
import { Dots } from "react-activity";
import { Button } from "@components/ui/button";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function SendOtp() {
  const [email, setEmail] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  const router = useRouter();
  const { status } = useSession();

  const Action = {
    OTPSend: async () => {
      setIsLoading(true);
      setIsError(false);

      try {
        let res = await api.post("/auth/resetcode", { email });
        if (res.status === 200) {
          setIsLoading(false);
          router.push(`/verify-otp?email=${email}`);

          console.log("Successfully sent the code");
        } else {
          toast.error("Unexpected response from the server!");
        }
      } catch (error: any) {
        setIsLoading(false);
        setIsError(true);

        toast.error("Account not found. Please enter your email carefully!");
      }
    },
  };

  useEffect(() => {
    if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Send Verification Code | MuseCool Dashboard";
  }, []);

  if (status === "loading") {
    return <Loading />;
  } else if (status === "authenticated") {
    return <Loading />;
  } else
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex h-full w-full flex-row items-center justify-center bg-white">
          <div className="hidden h-full sm:flex sm:w-[53%] sm:items-center sm:justify-center  sm:bg-black lg:w-3/5">
            <Image
              src={background}
              alt="MuseCool Music Ltd."
              className="h-full w-full object-cover opacity-70"
              priority
            />
          </div>
          <div className="flex h-full  w-full items-center justify-center sm:w-[47%] lg:w-2/5">
            <div className="w-3/4 sm:w-4/6 lg:w-[55%]">
              <div className="mb-5 flex items-center justify-center">
                <Image
                  src={GrayLogo}
                  alt="MuseCool Music Ltd."
                  height={200}
                  width={200}
                />
              </div>

              <div className="text-center text-sm font-bold text-slate-600">
                Request OTP
              </div>
              <div className="mt-1.5 text-center text-xs font-normal text-slate-600">
                Please provide your registered email for sending the OTP
              </div>

              <div className="mt-6 w-full">
                <Label>Email</Label>

                <Input
                  type="email"
                  placeholder="Email Address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-0.5 text-xs font-normal text-slate-600"
                />

                <Button
                  type="submit"
                  className={`mt-6 w-full ${
                    isError
                      ? "bg-red-600"
                      : isLoading
                        ? "bg-gray-700"
                        : "bg-black"
                  }`}
                  onClick={Action.OTPSend}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-1.5">
                      <span>Sending</span> <Dots />
                    </div>
                  ) : (
                    "Request OTP"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}
