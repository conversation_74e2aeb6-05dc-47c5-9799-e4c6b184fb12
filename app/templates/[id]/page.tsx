"use client";

import { tTemplateD } from "types/template";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { fetcher } from "@fetchers/fetcher";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { getSession } from "next-auth/react";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { Dots } from "react-activity";
import { Input } from "@components/ui/input";
import { Button } from "@components/ui/button";
import ListSkeltons from "@components/skeltons/ListSkeltons";

import axios from "axios";

import dynamic from "next/dynamic";
import { useParams, useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});
// import "jodit";
const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });

const buttons = [
  // "undo",
  // "redo",
  // "|",
  "bold",
  "strikethrough",
  "underline",
  "italic",
  "|",
  "superscript",
  "subscript",
  "|",
  "align",
  "|",
  "ul",
  "ol",
  "outdent",
  "indent",
  "|",
  "font",
  "fontsize",
  "brush",
  "paragraph",
  "|",
  "image",
  "link",
  "table",
  "|",
  "hr",
  "eraser",
  "copyformat",
  "|",
  "fullsize",
  "selectall",
  "print",
  "|",
  "source",
  "|",
];

const editorConfig = {
  readonly: false,
  toolbar: true,
  spellcheck: true,
  language: "en",
  toolbarAdaptive: false,
  // toolbarButtonSize: "small", // Set to an appropriate size (e.g., "small", "medium", etc.)
  showCharsCounter: false,
  showWordsCounter: false,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  // defaultActionOnPaste: "insert_clear_html",
  buttons: buttons, // Make sure you define the 'buttons' array
  uploader: {
    insertImageAsBase64URI: true,
    imagesExtensions: ["jpg", "png", "jpeg", "gif", "svg", "webp"],
  },
  width: "100%",
  height: "auto",
  theme: "default",
};

export default function Details() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams<any>();
  const { id } = params;

  //   const id = router.query.id;

  const [isLoadingS, setIsLoadingS] = useState<boolean>(false);

  const {
    data: Template,
    error,
    isLoading,
    mutate,
  } = useSWR<tTemplateD>(id ? `/template/${id}` : null, fetcher);

  const [editorState, setEditorState] = useState(
    Template?.content ? Template.content : "",
  );

  var resolver = yup.object().shape({
    category: yup.string().required("Category is required!"),
    title: yup.string().required("Title is required!"),
  });

  const defaultValues = {
    category: Template?.category || "",
    title: Template?.title || "",
    subject: Template?.subject || "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  // Upload Image In imaBB...
  const handleImageUpload = async (file: any) => {
    const formData = new FormData();
    formData.append("image", file);
    formData.append("key", "b9c236a6bcebb4b78487c74f94bfdba8");

    // console.log("Form Data : ", formData, " //END//");

    try {
      const response = await axios.post(
        "https://api.imgbb.com/1/upload",
        formData,
      );

      //   console.log("Response ", response);
      return response.data.data.url;
    } catch (error) {
      console.error("Image upload failed:", error);
      return null;
    }
  };

  const handleEditorChange = async (value: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(value, "text/html");
    const images = doc.querySelectorAll("img");

    for (let img of images) {
      if (img.src.startsWith("data:")) {
        const file = await fetch(img.src).then((res) => res.blob());
        const imageUrl = await handleImageUpload(file);
        if (imageUrl) {
          img.src = imageUrl;
        }
      }
    }

    setEditorState(doc.body.innerHTML);
  };

  const onSubmit = async (data: any) => {
    setIsLoadingS(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(
        `/template/${Template?.id ? Template.id : null}`,
        {
          ...data,
          content: editorState,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoadingS(false);
        toast.success("Template Updated Successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoadingS(false);
      toast.error("Failed to Update Template.Please Try Again");
    }
  };

  const resetFormAndEditor = () => {
    if (Template) {
      form.setValue("category", Template.category || "");
      form.setValue("title", Template.title || "");
      form.setValue("subject", Template.subject || "");
      setEditorState(Template.content || "");
    }

    // if (state) {
    //   setEditorState(Template?.content);
    // }
  };

  useEffect(() => {
    document.title = `${Template?.title ? Template.title : "Update Template"} | MuseCool Dashboard`;

    if (Template) {
      form.setValue("category", Template.category || "");
      form.setValue("title", Template.title || "");
      form.setValue("subject", Template.subject || "");
      if (Template.content) setEditorState(Template.content || "");
    }
  }, [Template]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else {
    }
  }, [status, router]);

  if (status === "loading") return <Loading />;
  else if (!session) {
    router.push("/login");
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        {isLoading ? (
          <ListSkeltons height={45} count={30} />
        ) : (
          <div className="hide-scrollbar h-full w-full overflow-y-scroll px-8 py-5">
            <div className="mb-4 text-lg font-semibold leading-none tracking-tight ">
              Update Template
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter title"
                          {...field}
                          className=" bg-white text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="my-5 flex w-full items-center space-x-2.5">
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem className="w-1/2">
                        <FormLabel>Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white text-xs font-normal text-slate-600">
                              {field.value ? field.value : "Select Category"}
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white text-xs font-normal text-slate-600">
                            <SelectGroup>
                              <SelectItem value="LFT Sender">
                                LFT Sender
                              </SelectItem>
                              <SelectItem value="Email Journey">
                                Email Journey
                              </SelectItem>
                              <SelectItem value="Enquiry">Enquiry</SelectItem>
                              <SelectItem value="Others">Others</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem className="w-1/2">
                        <FormLabel>Subject</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="Enter subject"
                            {...field}
                            // value={field.value || ""}
                            // onChange={field.onChange}
                            className="bg-white text-xs font-normal text-slate-600"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* //Draft code .. */}
                <FormLabel>Content</FormLabel>
                <div
                  style={{
                    maxWidth: editorConfig.width,
                    minHeight: "120px",
                    margin: "5px auto",

                    fontSize: "13px",
                    lineHeight: "250%",
                    fontWeight: "400",
                  }}
                >
                  <JoditEditor
                    value={editorState}
                    config={editorConfig}
                    // onChange={(value: any) => setEditorState(value)}
                    onChange={handleEditorChange}
                    className="editor"
                  />
                </div>

                {/* //Button Section... */}
                <div className="mt-10 flex w-full flex-row justify-end space-x-2">
                  <Button
                    type="reset"
                    onClick={resetFormAndEditor}
                    className="border border-black bg-white text-black hover:bg-black hover:text-white "
                  >
                    Reset
                  </Button>
                  <Button
                    type="submit"
                    className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                  >
                    {isLoadingS ? <Dots /> : "Update"}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
      </Sidebar>
    );
}
