"use client";

import Header from "@components/Header";
import PageList from "@components/PageList";
import Sidebar from "@components/Sidebar";

import { fetcher, pgfetcher } from "@fetchers/fetcher";
import { tPgTemplateL, tTemplateD, tTemplateL } from "types/template";
import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { useForm } from "react-hook-form";
import * as yup from "yup";

import { yupResolver } from "@hookform/resolvers/yup";
import { Textarea } from "@components/ui/textarea";
import { Dots } from "react-activity";

import api from "fetchers/BaseUrl";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import { Button } from "@components/ui/button";
import Template, { TemplateListHeader } from "@components/lists/template";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { Input } from "@components/ui/input";

import dynamic from "next/dynamic";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

import { EditorState, convertToRaw } from "draft-js";
import draftToHtml from "draftjs-to-html";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
// import { Editor } from "react-draft-wysiwyg";
import { EditorProps } from "react-draft-wysiwyg";
import { useRouter } from "next/navigation";

// Dynamically import the Editor component from react-draft-wysiwyg
const Editor = dynamic<EditorProps>(
  () => import("react-draft-wysiwyg").then((mod: any) => mod.Editor),
  { ssr: false },
);

const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });

export default function Templates() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");

  // Data Fetching ...

  const {
    data: Templates,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgTemplateL>(`/template/list?p=${page}`, pgfetcher);

  //   const id = "6e268ed0-b930-4350-5a6c-08dc49814d4e";
  //   const { data: TemplateD } = useSWR<tTemplateD>(`/template/${id}`, fetcher);

  //   console.log("Template Details", TemplateD);

  //   console.log(Templates);

  const Action = {
    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/templates", querywithPage, { shallow: true });
      router.push(`/templates${queryWithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      //   router.push("/templates", querywithPage, { shallow: true });
      router.push(`/templates${queryWithPage}`);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else {
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Templates | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-4 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Template List
            </div>

            <div className="flex flex-row items-center justify-end">
              <AddTemplate mutate={mutate} />
            </div>
          </div>

          <TemplateListHeader />

          {isLoading ? (
            <ListSkeltons height={40} count={30} />
          ) : (
            Templates?.data.map((template: tTemplateL) => (
              <Template key={template.id} template={template} mutate={mutate} />
            ))
          )}

          <PageList handler={Action.UpdatePage} page={Templates?.page} />
        </div>
      </Sidebar>
    );
}

type AddTempProps = {
  mutate: () => void;
};

const buttons = [
  // "undo",
  // "redo",
  // "|",
  "bold",
  "strikethrough",
  "underline",
  "italic",
  "|",
  "superscript",
  "subscript",
  "|",
  "align",
  "|",
  "ul",
  "ol",
  "outdent",
  "indent",
  "|",
  "font",
  "fontsize",
  "brush",
  "paragraph",
  "|",
  "image",
  "link",
  "table",
  "|",
  "hr",
  "eraser",
  "copyformat",
  "|",
  "fullsize",
  "selectall",
  "print",
  "|",
  "source",
  "|",
];

const editorConfig = {
  readonly: false,
  toolbar: true,
  spellcheck: true,
  language: "en",
  toolbarAdaptive: false,
  // toolbarButtonSize: "small", // Set to an appropriate size (e.g., "small", "medium", etc.)
  showCharsCounter: false,
  showWordsCounter: false,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  // defaultActionOnPaste: "insert_clear_html",
  buttons: buttons, // Make sure you define the 'buttons' array
  uploader: {
    insertImageAsBase64URI: true,
    imagesExtensions: ["jpg", "png", "jpeg", "gif", "svg", "webp"],
  },
  width: "100%",
  height: "auto",
  theme: "default",
};

function AddTemplate({ mutate }: AddTempProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  // For Draft
  const [editorState, setEditorState] = useState<string>("");

  var resolver = yup.object().shape({
    category: yup.string().required("Category is required!"),
    title: yup.string().required("Title is required!"),
  });

  const defaultValues = {
    category: "",
    title: "",
    subject: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    // console.log("Content :", editorState);
    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        "/template/",
        {
          ...data,
          content: editorState,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success("Template Added Successfully!");
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to Add Template! Please Try Again.");
    }
  };

  const reset = (value?: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
    setEditorState("");
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="mx-3 flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add Template
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add Template</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Category</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        {field.value ? field.value : "Select Category"}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value="LFT Sender">LFT Sender</SelectItem>
                        <SelectItem value="Email Journey">
                          Email Journey
                        </SelectItem>
                        <SelectItem value="Enquiry">Enquiry</SelectItem>
                        <SelectItem value="Others">Others</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter title"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Subject</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter subject"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Draft code .. */}

            <FormLabel>Content</FormLabel>
            <div
              style={{
                maxWidth: editorConfig.width,
                minHeight: "120px",
                margin: "5px auto",

                fontSize: "13px",
                lineHeight: "250%",
                fontWeight: "400",
              }}
            >
              <JoditEditor
                value={editorState}
                config={editorConfig}
                onChange={(value: any) => setEditorState(value)}
                className="editor"
              />
              {/* <Editor
                editorState={editorState}
                onEditorStateChange={onEditorStateChange}
                placeholder="Enter your content here..."
                editorClassName="editor"
                toolbar={{
                  options: [
                    "inline",
                    "blockType",
                    // "fontSize",
                    // "fontFamily",
                    "list",
                    "textAlign",
                    "image",
                    "colorPicker",
                    "link",
                    // "embedded",
                    "emoji",
                    "remove",
                    "history",
                  ],
                  image: {
                    uploadCallback: uploadImageCallBack,
                    alt: { present: true, mandatory: false },
                    defaultSize: {
                      height: "200px",
                      width: "200px",
                    },
                    inputAccept:
                      "image/gif,image/jpeg,image/jpg,image/png,image/svg",
                  },
                }}
              /> */}
            </div>

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
