"use client";

import Header from "@components/Header";
import PageList from "@components/PageList";
import Sidebar from "@components/Sidebar";

import { pgfetcher } from "@fetchers/fetcher";
import { tPgSurveyL } from "types/surveys";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";

import Survey, { SurveyListHeader } from "@components/lists/survey";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { Input } from "@components/ui/input";
import { GenerateQuery } from "core/Query";
import { SurveyDataGrabber } from "core/Excel/exportTutor";

import dynamic from "next/dynamic";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

import { useRouter } from "next/navigation";

export default function Surveys() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [searchName, setSearchName] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  // Data Fetching
  const {
    data: Surveys,
    isLoading,
    mutate,
  } = useSWR<tPgSurveyL>(`/survey/list?p=${page}${query}`, pgfetcher);

  const Handler = {
    UpdateSearchName: (e: any) => {
      e.preventDefault();
      setSearchName(e.target.value);
    },
  };

  const Action = {
    UpdateQuery: (query: string) => {
      // Construct a new query string by combining the current "page" value with the provided "query"
      let queryWithPage = `?p=${page}${query}`;
      // Use the router to update the URL to "/surveys" with the new query string
      router.push(`/surveys${queryWithPage}`);
      // Update the "query" state with the provided "query"
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      router.push(`/surveys${queryWithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      router.push(`/surveys${queryWithPage}`);
    },

    generateQuery: () => {
      const queryParams = {
        name: searchName.length > 2 ? searchName : undefined,
      };
      Action.UpdateQuery(GenerateQuery(queryParams));
    },
  };

  useEffect(() => {
    if (isActive) {
      Action.generateQuery();
    }
  }, [searchName]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Page Operation & Navigation
  useEffect(() => {
    document.title = "Surveys | MuseCool Dashboard";

    setIsActive(false);

    // Create a URLSearchParams object from the current URL's search parameters
    const search = new URLSearchParams(window.location.search);
    // Check if the "p" parameter exists in the URL
    if (search.get("p")) {
      // If "p" parameter exists, convert it to a number and set it as the "page" state
      setPage(Number(search.get("p")));
    }
    // Check if name parameter exists
    if (search.get("name")) {
      setSearchName(search.get("name") || "");
    }

    search.delete("p");
    if (search.toString() !== "") {
      Action.UpdateQuery("&" + search.toString());
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-4 py-5 sm:px-8">
          <div className="mb-4 flex w-full flex-col items-center justify-between gap-4 sm:flex-row">
            <div className="w-fit text-base font-medium text-gray-700">
              Survey List
            </div>

            <div className="flex w-full flex-col items-center justify-end gap-2 sm:w-auto sm:flex-row">
              <div
                onClick={() => SurveyDataGrabber(query)}
                className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-4 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                Export
              </div>
              <Input
                type="search"
                placeholder="Search by name..."
                className="w-full bg-white text-xs sm:w-[200px] md:w-[250px] lg:w-[300px]"
                value={searchName}
                onChange={Handler.UpdateSearchName}
              />
            </div>
          </div>

          <SurveyListHeader />

          {isLoading ? (
            <ListSkeltons height={60} count={20} />
          ) : (
            Surveys?.data.map((survey) => (
              <Survey key={survey.studentId} survey={survey} mutate={mutate} />
            ))
          )}

          <PageList handler={Action.UpdatePage} page={Surveys?.page} />
        </div>
      </Sidebar>
    );
}
