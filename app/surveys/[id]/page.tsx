"use client";

import { tSurveyD } from "types/surveys";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card";
import { Badge } from "@components/ui/badge";
import { Button } from "@components/ui/button";
import dynamic from "next/dynamic";
import { useParams, useRouter } from "next/navigation";
import { FiUsers, FiMail, FiCopy } from "react-icons/fi";
import { MdOutlinePoll, MdQuestionAnswer } from "react-icons/md";
import SurveyDetailsSk from "@components/skeltons/SurveyDetailsSk";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@components/ui/tooltip";
import toast from "react-hot-toast";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

export default function SurveyDetails() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams<any>();
  const { id } = params;

  const [Survey, setSurvey] = useState<tSurveyD | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Copy email to clipboard function
  const copyToClipboard = async (email: string) => {
    try {
      await navigator.clipboard.writeText(email);
      toast.success(`Email copied to clipboard: ${email}`);
    } catch (err) {
      toast.error("Failed to copy email to clipboard");
    }
  };

  // Load survey data from localStorage
  useEffect(() => {
    if (id) {
      try {
        const storedData = localStorage.getItem(`survey_${id}`);
        if (storedData) {
          const surveyData = JSON.parse(storedData);
          setSurvey(surveyData);
          setError(null);
        } else {
          setError(
            "Survey data not found. Please navigate from the survey list page.",
          );
        }
      } catch (err) {
        setError(
          "Failed to load survey data. Please try again from the survey list page.",
        );
      } finally {
        setIsLoading(false);
      }
    }
  }, [id]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = `${Survey?.name ? Survey.name + " Survey" : "Survey Details"} | MuseCool Dashboard`;
  }, [Survey]);

  if (status === "loading") {
    return <Loading />;
  }

  if (!session) {
    return <Loading />;
  }

  if (isLoading) {
    return (
      <Sidebar>
        <Header />
        <div className="px-4 py-5 sm:px-8">
          <SurveyDetailsSk />
        </div>
      </Sidebar>
    );
  }

  if (error || (!isLoading && !Survey)) {
    return (
      <Sidebar>
        <Header />
        <div className="px-4 py-5 sm:px-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="mb-4 text-gray-500">
                  {error || "Survey not found or failed to load."}
                </div>
                <Button
                  onClick={() => router.push("/surveys")}
                  className="bg-[#0094ba] hover:bg-[#0077a3]"
                >
                  Back to Survey List
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </Sidebar>
    );
  }

  if (!Survey) {
    return <Loading />;
  }

  return (
    <Sidebar>
      <Header />

      <div className="flex h-full w-full bg-gradient-to-br from-slate-50 to-cyan-50">
        {/* Left Section - Fixed Width 384px */}
        <div className="w-96 flex-shrink-0 border-r border-gray-200 bg-white">
          <div className="hide-scrollbar h-full overflow-y-auto p-4">
            {/* Header Section - Modern Design */}
            <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-[#0094ba] via-[#0085a8] to-[#0077a3] p-5 text-white shadow-xl">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div
                  className="absolute inset-0 bg-repeat"
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                  }}
                ></div>
              </div>

              <div className="relative z-10">
                <div className="mb-4">
                  <h1 className="text-xl font-bold leading-tight">
                    {Survey.name}
                  </h1>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm">
                    <MdOutlinePoll size={14} />
                    <span>{Survey.option}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm">
                    <FiUsers size={14} />
                    <span>{Survey.totalReferred} Referrals</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Survey Referrals Section - Modern Card Design */}
            <Card className="mt-5 border-0 bg-white shadow-lg ring-1 ring-gray-100 transition-all duration-200 hover:shadow-xl">
              <CardHeader className="border-b border-gray-100 bg-gradient-to-r from-gray-50 via-white to-gray-50 px-4 py-4">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-xl bg-[#0094ba]/10">
                      <FiUsers size={16} className="text-[#0094ba]" />
                    </div>
                    <div>
                      <h3 className="text-sm font-bold text-gray-900">
                        Survey Referrals
                      </h3>
                      <p className="text-xs text-gray-500">
                        Referred participants
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-gradient-to-r from-[#0094ba] to-[#0077a3] px-3 py-1 text-xs font-bold text-white shadow-sm">
                    {Survey.totalReferred}
                  </Badge>
                </CardTitle>
              </CardHeader>

              <CardContent className="p-4">
                {Survey.surveyReferred && Survey.surveyReferred.length > 0 ? (
                  <div className="space-y-3">
                    {Survey.surveyReferred.map((referral, index) => (
                      <div
                        key={index}
                        className="group relative overflow-hidden rounded-xl border border-green-100 bg-gradient-to-r from-green-50 to-emerald-50 p-3 transition-all duration-200 hover:border-green-200 hover:shadow-md"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5"></div>
                        <div className="relative flex items-center space-x-3">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex min-w-0 flex-1 cursor-pointer items-center space-x-3">
                                  <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-emerald-500 text-xs font-bold text-white shadow-sm">
                                    {index + 1}
                                  </div>
                                  <div className="flex min-w-0 flex-1 items-center space-x-2">
                                    <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100">
                                      <FiMail
                                        size={12}
                                        className="text-green-600"
                                      />
                                    </div>
                                    <p className="truncate text-sm font-medium text-gray-800">
                                      {referral.email}
                                    </p>
                                  </div>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-xs">{referral.email}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>

                          <div className="flex-shrink-0">
                            <button
                              onClick={() => copyToClipboard(referral.email)}
                              className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-100 text-gray-600 transition-colors hover:bg-gray-200"
                            >
                              <FiCopy size={12} />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-50 ring-8 ring-gray-50/50">
                      <FiUsers size={20} className="text-gray-400" />
                    </div>
                    <h3 className="mb-2 text-sm font-bold text-gray-900">
                      No Referrals Yet
                    </h3>
                    <p className="text-xs text-gray-500">
                      No referrals found for this survey
                    </p>
                    <div className="mt-4 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Right Section - Questions */}
        <div className="flex-1 overflow-hidden bg-white px-10">
          <div className="hide-scrollbar h-full overflow-y-auto p-4">
            {/* Survey Questions Section */}
            <div className="border-0">
              <div className="rounded-xl border border-cyan-600 bg-white py-4 shadow-md">
                <div className="flex flex-row items-center justify-center space-x-2 text-base font-bold text-cyan-600">
                  <MdQuestionAnswer size={18} className="text-cyan-600" />
                  <span>Survey Questions & Answers</span>

                  <Badge className="bg-cyan-600 px-2 py-1 text-xs font-bold text-cyan-50">
                    {Survey.surveyQuestions.length}
                  </Badge>
                </div>
              </div>

              <div className="py-4">
                {Survey.surveyQuestions && Survey.surveyQuestions.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
                    {Survey.surveyQuestions.map((qa, index) => (
                      <div
                        key={index}
                        className="rounded-xl border border-l-4 border-[#0094ba] shadow-lg transition-all duration-200"
                      >
                        <div className="p-4">
                          {/* Question Header */}
                          <div className="mb-3 flex items-start space-x-2">
                            <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-[#0094ba] text-xs font-bold text-white">
                              {index + 1}
                            </div>

                            <div className="min-w-0 flex-1 pt-1">
                              <h3 className="text-xs font-medium leading-tight text-gray-600">
                                {qa.question}
                              </h3>
                            </div>
                          </div>

                          {/* Answer */}
                          <div className="rounded-2xl bg-cyan-600 p-3">
                            <div className="space-y-2">
                              {qa.answer
                                .split("/")
                                .map((answer, answerIndex) => (
                                  <div
                                    key={answerIndex}
                                    className="flex items-start space-x-2"
                                  >
                                    <div className="mt-0.5 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full bg-white">
                                      <div className="h-1.5 w-1.5 rounded-full bg-cyan-600"></div>
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <p className="text-xs font-semibold leading-relaxed text-white">
                                        {answer.trim()}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <MdQuestionAnswer size={20} className="text-gray-400" />
                    </div>
                    <h3 className="mb-1 text-lg font-bold text-gray-600">
                      No Questions Available
                    </h3>
                    <p className="text-sm text-gray-500">
                      No survey questions found for this participant
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Sidebar>
  );
}
