"use client";

import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import Pfl from "@components/lists/Pfl";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { tPflL, tPgPflL } from "types/pending1stL";
import { pgfetcher } from "@fetchers/fetcher";
import PageList from "@components/PageList";
import { GenerateQuery } from "core/Query";
import ListSkeltons from "@components/skeltons/ListSkeltons";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from "components/ui/select";

import { Input } from "@components/ui/input";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function PfLs() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");

  // For Control Filter....
  const [Confirmed, setConfirmed] = useState<string>("");
  const [Manager, setManager] = useState<string>("");
  const [OrderBy, setOrderBy] = useState<string>("");
  const [Search, setSearch] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  const [country, setCountry] = useState<string>("");

  const {
    data: PFLs,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgPflL>(`/pfl?p=${page}${query}`, pgfetcher);

  // Finish UseFrom ............

  const Action = {
    UpdateQuery: (query: string) => {
      let queryWithPage = `?p=${page}${query}`;
      //   router.push("/pfl", queryWithPage, { shallow: true });
      router.push(`/pfl${queryWithPage}`);
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/pfl", queryWithPage, { shallow: true });
      router.push(`/pfl${queryWithPage}`);
      setPage(newPage);
    },
  };

  const FilterHandelar = {
    UpdateConfirmed: (e: any) => {
      setConfirmed(e);
    },

    UpdateManager: (e: any) => {
      setManager(e);
    },

    UpdateOrderBy: (e: any) => {
      setOrderBy(e);
    },
    UpdateSearch: (e: any) => {
      e.preventDefault();
      setSearch(e.target.value);
    },

    UpdateCountry: (e: any) => {
      setCountry(e);
    },
  };

  // const generateQuery = () => {
  //   if (Search.length > 2) {
  //     Action.UpdateQuery(
  //       GenerateQuery({
  //         Search,
  //         Confirmed,
  //         OrderBy,
  //       })
  //     );
  //   } else {
  //     Action.UpdateQuery(
  //       GenerateQuery({
  //         Confirmed,
  //         OrderBy,
  //       })
  //     );
  //   }
  // };

  const generateQuery = () => {
    const queryParams = {
      Search: Search.length > 2 ? Search : undefined,
      Confirmed,
      Manager,
      OrderBy,
      country,
    };

    Action.UpdateQuery(GenerateQuery(queryParams));
  };

  useEffect(() => {
    if (isActive) {
      generateQuery();
    }
  }, [Search, Confirmed, OrderBy, Manager, country]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "PFL | MuseCool Dashboard";

    setManager("me");
    setIsActive(false);

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />
        <div className="flex h-full w-full flex-col overflow-y-scroll p-4">
          <div className="flex w-full flex-row items-center">
            <div className="my-4 flex w-full flex-row space-x-2">
              {/* //Schedulede or Not ... */}
              <div className="w-1/4">
                <Select
                  onValueChange={FilterHandelar.UpdateConfirmed}
                  defaultValue={Confirmed}
                >
                  <SelectTrigger className="mb-2 w-full rounded-sm border border-slate-300 bg-white">
                    <div className="">
                      {Confirmed
                        ? Confirmed === "yes"
                          ? "Scheduled"
                          : "Not-Schedulede"
                        : "All"}
                    </div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-gray-700">
                    <SelectGroup>
                      <SelectItem value={null as any}>All</SelectItem>
                      <SelectItem value="yes">Scheduled</SelectItem>
                      <SelectItem value="no">Not-Schedulede</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              {/* Manager.. : */}
              <div className="w-1/4">
                <Select
                  onValueChange={FilterHandelar.UpdateManager}
                  defaultValue={Manager}
                >
                  <SelectTrigger className="mb-2 w-full rounded-sm border border-slate-300 bg-white">
                    <div className="">
                      {Manager === "all"
                        ? "All"
                        : Manager === "me"
                          ? "Me"
                          : "Others"}
                    </div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-gray-700">
                    <SelectGroup>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="me">Me</SelectItem>
                      <SelectItem value="others">Others</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex w-full   flex-row justify-end space-x-2">
              {/* Country */}
              <div className="w-1/4">
                <Select
                  onValueChange={FilterHandelar.UpdateCountry}
                  defaultValue={country}
                >
                  <SelectTrigger className="mb-2 w-full rounded-sm border border-slate-300 bg-white">
                    <div className="">{country ? country : "Both"}</div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-gray-700">
                    <SelectGroup>
                      <SelectItem value={null as any}>Both</SelectItem>
                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              {/* Ordering... */}
              <div className="w-1/4">
                <Select
                  onValueChange={FilterHandelar.UpdateOrderBy}
                  defaultValue={OrderBy}
                >
                  <SelectTrigger className="mb-2 w-full rounded-sm border border-slate-300 bg-white">
                    <div className="">{OrderBy ? OrderBy : "Order By"}</div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-gray-700">
                    <SelectGroup>
                      <SelectItem value="Name">Name</SelectItem>
                      <SelectItem value="ConfirmedAsc">
                        Confirmed - Asc
                      </SelectItem>
                      <SelectItem value="ConfirmedDesc">
                        Confirmed - Desc
                      </SelectItem>
                      <SelectItem value="AddedAsc">Added - Asc</SelectItem>
                      <SelectItem value="AddedDesc">Added - Desc</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              {/* //Searching.... */}
              <Input
                type="search"
                placeholder="Name/Email"
                className="rounded-sm border border-slate-300 bg-white md:w-[100px] lg:w-[300px]"
                value={Search}
                onChange={FilterHandelar.UpdateSearch}
              />
            </div>
          </div>

          <div>
            {isLoading && <ListSkeltons height={100} count={15} />}

            {PFLs?.data.map((pfl: tPflL) => (
              <Pfl P1stLesson={pfl} key={pfl.id} mutate={mutate} />
            ))}
          </div>

          <PageList handler={Action.UpdatePage} page={PFLs?.page} />
        </div>
      </Sidebar>
    );
}
