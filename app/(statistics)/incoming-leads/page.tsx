"use client";

import React, { useEffect, useState } from "react";

import { useSession } from "next-auth/react";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

import { DateRange } from "react-day-picker";
import { Button } from "components/ui/button";
import { Calendar } from "components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";

import dynamic from "next/dynamic";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";

import moment from "moment";
import { SlCalender } from "react-icons/sl";
import { Input } from "@components/ui/input";
import { GenerateQuery } from "core/Query";
import useSWR from "swr";
import { tPgStudentL, tStudentL } from "types/student";
import { pgfetcher } from "@fetchers/fetcher";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import PageList from "@components/PageList";
import Student, { StudentListHeaderWOFilter } from "@components/lists/Student";
import EmptyStudentList from "@components/Student/EmptyStudentList";
import { useRouter } from "next/navigation";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function IncomingLeads() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  const [date, setDate] = useState<DateRange | any>({
    from: moment().subtract(15, "days").toDate(),
    to: new Date(),
  });

  const {
    data: Leads,
    isLoading,
    error,
    mutate,
  } = useSWR<tPgStudentL>(`/dashboard/enquiries?p=${page}${query}`, pgfetcher);

  const [country, setCountry] = useState<string>("");
  const [name, setName] = useState<string>("");

  const FilterHandelar = {
    UpdateCountry: (e: any) => {
      setCountry(e);
    },
    UpdateName: (e: any) => {
      e.preventDefault();
      setName(e.target.value);
    },
  };

  const Action = {
    UpdateQuery: (query: string) => {
      let querywithPage = `?p=${page}${query}`;
      router.push(`/incoming-leads${querywithPage}`);
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let querywithPage = `?p=${newPage}${query}`;
      router.push(`/incoming-leads${querywithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let querywithPage = `?p=${1}${query}`;
      router.push(`/incoming-leads${querywithPage}`);
    },
  };
  const generateQuery = () => {
    Action.UpdateQuery(
      GenerateQuery({
        name: name.length > 2 ? name : "",
        country,
        start: date?.from ? moment(date?.from).format("YYYY-MM-DD") : "",
        end: date?.to ? moment(date?.to).format("YYYY-MM-DD") : "",
      }),
    );
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = `Incoming Leads | MuseCool Admin`;

    setIsActive(false);

    var search = new URLSearchParams(window.location.search);

    // Set page number from URL, default to 1
    setPage(search.get("p") ? Number(search.get("p")) : 1);

    // Set date range if available in URL, otherwise apply default (last 15 days)
    if (search.get("start") && search.get("end")) {
      setDate({
        from: new Date(search.get("start")!),
        to: new Date(search.get("end")!),
      });
    } else {
      // If no date is present in URL, apply default
      setDate({
        from: moment().subtract(15, "days").toDate(),
        to: new Date(),
      });
    }

    setIsActive(true);
  }, []);

  useEffect(() => {
    if (isActive) {
      generateQuery();
    }
  }, [country, name, date]);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          {/* <div className="mb-3 flex flex-row items-center ">
            <BsGraphUpArrow className="text-gray-600" size={14} />
            <div className="">
              <Select
                onValueChange={(e: any) => setCompare(e)}
                defaultValue={compare}
              >
                <SelectTrigger className="w-fit border-0 px-2  shadow-none outline-none  focus:outline-none focus:ring-0">
                  <div className="text-sm font-medium text-gray-600">
                    {compare === "1"
                      ? "Today vs Previous Day"
                      : compare === "7"
                        ? "This Week vs Previous Week"
                        : " This Month vs Previous Month"}
                  </div>
                </SelectTrigger>
                <SelectContent className="text-xs font-normal text-gray-700">
                  <SelectGroup>
                    <SelectItem value="1">Today vs Previous Day</SelectItem>
                    <SelectItem value="7">
                      This Week vs Previous Week
                    </SelectItem>
                    <SelectItem value="30">
                      This Month vs Previous Month
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>  */}

          <div className="mb-10 flex flex-row items-end justify-between">
            <div>
              <div className="pb-1 text-base font-semibold text-slate-800">
                Date Range
              </div>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="date"
                    variant={"outline"}
                    className="max-w-xs bg-white px-4 text-left font-normal"
                  >
                    <SlCalender size={16} className="mr-2" />

                    {date?.from ? (
                      date.to ? (
                        <>
                          {moment(date.from).format("MMM D, YYYY")} -{" "}
                          {moment(date.to).format("MMM D, YYYY")}
                        </>
                      ) : (
                        moment(date.from).format("MMM D, YYYY")
                      )
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>

                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={date?.from}
                    selected={date}
                    onSelect={setDate}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex flex-row items-center justify-end space-x-2.5">
              <div className="w-44">
                <Select
                  onValueChange={FilterHandelar.UpdateCountry}
                  defaultValue={country}
                >
                  <SelectTrigger className="w-full bg-white">
                    <div className="text-sm font-medium text-slate-800">
                      {country ? country : "Both"}
                    </div>
                  </SelectTrigger>
                  <SelectContent className="text-xs font-normal text-slate-600">
                    <SelectGroup>
                      <SelectItem value={null as any}>Both</SelectItem>

                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <Input
                type="search"
                placeholder="Name"
                className="bg-white md:w-[100px] lg:w-[300px]"
                value={name}
                onChange={FilterHandelar.UpdateName}
              />
            </div>
          </div>

          <div>
            {!isLoading &&
              (Leads?.data && Leads?.data.length > 0 ? (
                <StudentListHeaderWOFilter />
              ) : (
                <EmptyStudentList text="No new leads have been received during this time period." />
              ))}

            {isLoading ? (
              <ListSkeltons height={42} count={30} />
            ) : (
              Leads?.data.map((lead: tStudentL) => (
                <Student key={lead.id} student={lead} mutate={mutate} />
              ))
            )}

            <PageList
              handler={Action.UpdatePage}
              page={Leads?.page}
              itemName="Students"
            />
          </div>
        </div>
      </Sidebar>
    );
}
