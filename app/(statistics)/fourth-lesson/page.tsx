"use client";

import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import Student, { StudentListHeaderWOFilter } from "@components/lists/Student";
import { pgfetcher } from "@fetchers/fetcher";
import { tPgStudentL, tStudentL } from "types/student";
import React, { useEffect, useState } from "react";
import { BsGraphUpArrow } from "react-icons/bs";
import useSWR from "swr";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { useSession } from "next-auth/react";
import EmptyStudentList from "@components/Student/EmptyStudentList";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import PageList from "@components/PageList";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";

import dynamic from "next/dynamic";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Fourth() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const pathName = usePathname();
  const searchParams = useSearchParams();
  const Compare = searchParams?.get("Compare");

  // const parseCompare = typeof Compare === "string" ? parseInt(Compare, 10) : 7;
  const parseCompare = typeof Compare === "string" ? Compare : "7";

  const [page, setPage] = useState<number>(1);
  const [compare, setCompare] = useState<string>(parseCompare);

  const {
    data: Students,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgStudentL>(
    `/dashboard/having-four?p=${page}&date=${new Date().toDateString()}&compare=${parseInt(
      compare,
      10,
    )}`,
    pgfetcher,
  );

  const Action = {
    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}&date=${new Date().toDateString()}&compare=${parseInt(
        compare,
        10,
      )};`;
      router.push(`/fourth-lesson${queryWithPage}`);

      setPage(newPage);
    },

    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}&date=${new Date().toDateString()}&compare=${parseInt(
        compare,
        10,
      )};`;
      //   router.push("/stat/fourth", queryWithPage, { shallow: true });
      router.push(`/fourth-lesson${queryWithPage}`);
    },
  };

  // Update the URL when the compare value changes
  useEffect(() => {
    if (!pathName) return;

    // Create a new URLSearchParams object from the current URL
    const newParams = new URLSearchParams(window.location.search);

    console.log("New param", newParams);

    // Update the Compare parameter
    newParams.set("Compare", compare.toString());

    // ✅ Update the URL without adding to history (mimics shallow routing)
    router.replace(`${pathName}?${newParams.toString()}`);
  }, [compare]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = `Fourth Lesson | MuseCool Admin`;
    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />
        <div className="flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-3 flex flex-row items-center ">
            <BsGraphUpArrow className="text-gray-600" size={14} />
            <div className="">
              <Select
                onValueChange={(e: any) => setCompare(e)}
                defaultValue={compare}
              >
                <SelectTrigger className="w-fit border-0 px-2  shadow-none outline-none  focus:outline-none focus:ring-0">
                  <div className="text-sm font-medium text-gray-600">
                    {compare === "1"
                      ? "Today vs Previous Day"
                      : compare === "7"
                        ? "This Week vs Previous Week"
                        : " This Month vs Previous Month"}
                  </div>
                </SelectTrigger>
                <SelectContent className="text-xs font-normal text-gray-700">
                  <SelectGroup>
                    <SelectItem value="1">Today vs Previous Day</SelectItem>
                    <SelectItem value="7">
                      This Week vs Previous Week
                    </SelectItem>
                    <SelectItem value="30">
                      This Month vs Previous Month
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
          {!isLoading &&
            (Students?.data && Students?.data.length > 0 ? (
              <StudentListHeaderWOFilter />
            ) : (
              <EmptyStudentList text="Empty! No Students Found!" />
            ))}

          <div>
            {isLoading && <ListSkeltons height={42} count={30} />}

            {Students?.data.map((student: tStudentL) => (
              <Student student={student} key={student.id} mutate={mutate} />
            ))}
          </div>
          <PageList
            handler={Action.UpdatePage}
            page={Students?.page}
            itemName="Students"
          />
        </div>
      </Sidebar>
    );
}
