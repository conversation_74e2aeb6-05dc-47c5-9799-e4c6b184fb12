@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --sidebar-width: 220px;
  --font-color-bright: white;
  --background-color-bright: #f9f9f9;
}

html,
body {
  padding: 0;
  margin: 0;
  min-height: 100vh;
  overflow: hidden;
  width: 100%;
  position: relative;
  font-family: Poppins;
  background-color: var(--background-color-bright);
  color: #222;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* width */
::-webkit-scrollbar {
  width: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: black;
  border-radius: 15px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: rgb(26, 25, 25);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes sidebarOpenAmination {
  0% {
    width: 60px;
  }
  100% {
    width: var(--sidebar-width);
  }
}

/* code for Hide scrollbar... */

.hide-scrollbar::-webkit-scrollbar {
  width: 0rem; /* You can adjust this width to control the scrollbar size */
}

.hide-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent; /* Make the scrollbar thumb transparent */
}

.editor {
  background-color: white;
  color: black;
}

.editor ol {
  list-style-type: lower-roman; /* Use lower-roman for i, ii, iii, etc. */
  padding-left: 20px; /* Adjust padding as needed */
}

.editor ol li {
  margin-bottom: 5px; /* Optional: Add some space between list items */
}

.editor ul {
  list-style-type: disc; /* Use disc for bullet points */
  padding-left: 20px; /* Adjust padding as needed */
}

.editor ul li {
  margin-bottom: 5px; /* Optional: Add some space between list items */
}

.lftEditor {
  max-height: 90vh;
  /* min-height: 120px; */
  background-color: white;
  color: black;
  font-size: small;
  padding: 10px;
  overflow-y: scroll;
}

.lftEditor::-webkit-scrollbar {
  width: 0rem; /* You can adjust this width to control the scrollbar size */
}

.lftEditor::-webkit-scrollbar-thumb {
  background-color: transparent; /* Make the scrollbar thumb transparent */
}
