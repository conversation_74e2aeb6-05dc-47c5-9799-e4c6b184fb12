"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import dynamic from "next/dynamic";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";
import useSWR from "swr";
import { tLesson, tPgLesson } from "types/dashboard";
import { pgfetcher } from "@fetchers/fetcher";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import PageList from "@components/PageList";
import EmptyStudentList from "@components/Student/EmptyStudentList";
import Lesson, { LessonListHeader } from "@components/lists/Lesson";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Lessons() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");

  // Data Fetching
  const {
    data: Lessons,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgLesson>(`/lesson?p=${page}${query}`, pgfetcher);

  const Action = {
    UpdateQuery: (newQuery: string) => {
      let queryWithPage = `?p=${page}${newQuery}`;
      router.push(`/lesson${queryWithPage}`);
      console.log("newQuery || Action", newQuery);
      setQuery(newQuery);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      router.push(`/lesson${queryWithPage}`);
      setPage(newPage);
    },

    PageReset: (newQuery: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${newQuery}`;
      router.push(`/lesson${queryWithPage}`);
      setQuery(newQuery);
      // console.log("newQuery || Action", newQuery);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Lessons | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />
        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-4 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Lesson List
            </div>

            <div className="flex flex-row items-center justify-end"></div>
          </div>

          <LessonListHeader
            updateQuery={Action.UpdateQuery}
            updatePage={Action.UpdatePage}
            pageReset={Action.PageReset}
          />

          {!isLoading && Lessons?.data && Lessons?.data.length === 0 && (
            <EmptyStudentList text="Empty! No lesson found!" />
          )}

          {isLoading ? (
            <ListSkeltons height={60} count={30} />
          ) : (
            Lessons?.data.map((lesson: tLesson) => (
              <Lesson key={lesson.id} lesson={lesson} mutate={mutate} />
            ))
          )}

          {Lessons?.data && Lessons?.data.length > 0 && (
            <PageList handler={Action.UpdatePage} page={Lessons?.page} />
          )}
        </div>
      </Sidebar>
    );
}
