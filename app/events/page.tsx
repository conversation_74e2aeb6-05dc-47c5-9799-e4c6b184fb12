"use client";

import Header from "@components/Header";
import PageList from "@components/PageList";
import Sidebar from "@components/Sidebar";
import { pgfetcher } from "@fetchers/fetcher";
import { tEventL, tPgEventL } from "types/type";
import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import Event, { EventLisHeader } from "@components/lists/Event";

import ListSkeltons from "@components/skeltons/ListSkeltons";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import { Input } from "@components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import * as yup from "yup";
import { Button } from "@components/ui/button";
import { yupResolver } from "@hookform/resolvers/yup";
import toast from "react-hot-toast";
import moment from "moment";
import api from "fetchers/BaseUrl";
import { Textarea } from "@components/ui/textarea";
import { Dots } from "react-activity";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Events() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  const {
    data: Events,
    error,
    isLoading,
    mutate,
  } = useSWR<tPgEventL>(`/event?p=${page}${query}`, pgfetcher);

  const Action = {
    UpdateQuery: (newQuery: string) => {
      let queryWithPage = `?p=${page}${newQuery}`;

      //   router.push("/events", queryWithPage, { shallow: true });
      router.push(`/events${queryWithPage}`);

      setQuery(newQuery);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/events", queryWithPage, { shallow: true });
      router.push(`/events${queryWithPage}`);
      setPage(newPage);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Events | MuseCool Dashboard";

    setIsActive(false);

    const search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className=" flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          {/* //Main Header Section... */}
          <div className="w-full">
            <div className="mb-3 flex w-full flex-row items-center justify-between">
              <div className="w-fit text-base font-medium text-gray-700">
                Event List
              </div>
              <AddEvents mutate={mutate} />
            </div>

            {/* // Event List... */}
            <EventLisHeader />
            {isLoading && <ListSkeltons height={45} count={15} />}
            {Events?.data.map((event: tEventL) => (
              <Event key={event.id} event={event} mutate={mutate} />
            ))}
          </div>

          {/* //Page Shown... */}
          <PageList
            handler={Action.UpdatePage}
            page={Events?.page}
            itemName="Events"
          />
        </div>
      </Sidebar>
    );
}

type AddEventProps = {
  mutate: () => void;
};

function AddEvents({ mutate }: AddEventProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  // Yup Validations
  var resolver = yup.object().shape({
    title: yup.string().required("Event title is required!"),
    date: yup.date().required("Please select date!"),
    visibility: yup.string().required("Visbilility is required!"),
    location: yup.string().required("Location is required!"),
    description: yup.string().required("Description is required!"),
  });

  // Default Values
  const defaultValues = {
    title: "",
    date: new Date(),
    visibility: "",
    location: "",
    description: "",
  };

  // Hook Form Setup
  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  // On Submit
  const onSubmit = async (data: any) => {
    setIsLoading(true);
    const { date } = data;

    try {
      const Session = await getSession();
      const token = Session?.token;
      const res = await api.post(
        "/event",
        {
          ...data,
          date: moment(date).format("YYYY-MM-DD"),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (res.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success("Event Added successfully!");
        reset();
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to added event!");
    }
  };

  // Reset Form
  const reset = (value?: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="flex h-9 cursor-pointer items-center justify-center rounded-md bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add Event
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Event</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {/* Title Field */}

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Event Title</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="Enter title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Filed */}
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Event Date</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      showTimeInput
                      dateFormat="Pp"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Visivility Field */}
            <FormField
              control={form.control}
              name="visibility"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel> Visibility </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select Visibility" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={null as any}>All</SelectItem>
                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location Field */}

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Event Location</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter location"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Description Field */}

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Event Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className=" my-2 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
