"use client";

import React, { useEffect, useState } from "react";

import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";
import { getSession, useSession } from "next-auth/react";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";

import { Button } from "@components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import { useForm } from "react-hook-form";
import useSWR from "swr";
import { Dots } from "react-activity";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import api from "fetchers/BaseUrl";
import toast from "react-hot-toast";
import { tReferralAmountUpdate } from "types/referral";
import { fetcher } from "@fetchers/fetcher";
import { formatCurrency } from "core/formatCurrency";
import { Skeleton } from "@components/ui/skeleton";
import Image from "next/image";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

import USFlag from "../../assets/US.png";
import UKFlag from "../../assets/Uk.png";

export default function Referral() {
  const router = useRouter();
  const { data: session, status } = useSession();

  //Data Fetching
  const {
    data: ReferralData,
    error,
    mutate,
    isLoading,
  } = useSWR<Array<tReferralAmountUpdate>>(`/ReferralConfig/list`, fetcher);

  console.log("ReferralData", ReferralData);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Referral | MuseCool Dashboard";
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll pb-16 pt-4 md:pt-6">
          <div className="container mx-auto flex h-full max-w-5xl flex-col items-center justify-center sm:max-w-xl md:max-w-5xl lg:max-w-2xl xl:max-w-3xl">
            <div className="flex w-auto flex-col items-center justify-center rounded-3xl bg-white p-10 shadow-md">
              <div>
                <div className="pb-5 text-center text-xl font-semibold text-slate-800">
                  Referral Rewards & Bonuses
                </div>

                <Tabs
                  defaultValue="United Kingdom"
                  className="w-[480px] rounded-3xl shadow-none"
                >
                  {/* <TabsList className="grid w-full grid-cols-2 border border-cyan-600 bg-cyan-600 py-0 text-white"> */}
                  <TabsList className="grid h-12 w-full grid-cols-2 rounded-3xl px-0 py-0">
                    <TabsTrigger
                      className="h-full rounded-3xl py-0 data-[state=active]:bg-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-none"
                      value="United Kingdom"
                    >
                      <Image
                        src={UKFlag}
                        alt="UK Flag"
                        className="mr-1.5 h-6 w-6"
                      />
                      United Kingdom
                    </TabsTrigger>
                    <TabsTrigger
                      className="h-full rounded-3xl py-0 data-[state=active]:bg-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-none"
                      value="United States"
                    >
                      <Image
                        src={USFlag}
                        alt="US Flag"
                        className="mr-1.5 h-6 w-6"
                      />
                      United States
                    </TabsTrigger>
                  </TabsList>

                  {["United States", "United Kingdom"].map((country, index) => (
                    <TabsContent key={country} value={country}>
                      <Card>
                        <CardContent className="space-y-5 py-6">
                          {isLoading || !ReferralData ? (
                            <>
                              <div className="flex flex-row items-center space-x-4">
                                <div className="w-1/2 space-y-1">
                                  <Skeleton className="h-4 w-28" />
                                  <Skeleton className="h-6 w-16" />
                                </div>
                                <div className="flex-1 space-y-1">
                                  <Skeleton className="h-4 w-28" />
                                  <Skeleton className="h-6 w-16" />
                                </div>
                              </div>
                              <div className="space-y-1">
                                <Skeleton className="h-4 w-40" />
                                <Skeleton className="h-6 w-16" />
                              </div>
                            </>
                          ) : (
                            <>
                              <div className="flex flex-row items-center">
                                <div className="w-1/2 space-y-1">
                                  <div className="text-sm font-medium text-slate-600">
                                    Referrer amount
                                  </div>
                                  <div className="text-sm font-semibold text-cyan-600">
                                    {formatCurrency(
                                      ReferralData[index]?.referredAmount,
                                      ReferralData[index]?.country ===
                                        "United Kingdom"
                                        ? "£"
                                        : "$",
                                    )}
                                  </div>
                                </div>
                                <div className="flex-1 space-y-1">
                                  <div className="text-sm font-medium text-slate-600">
                                    Referee amount
                                  </div>
                                  <div className="text-sm font-semibold text-cyan-600">
                                    {formatCurrency(
                                      ReferralData[index]?.acceptorAmount,
                                      ReferralData[index]?.country ===
                                        "United Kingdom"
                                        ? "£"
                                        : "$",
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="space-y-1">
                                <div className="text-sm font-medium text-slate-600">
                                  Extra bonus (Referee)
                                </div>
                                <div className="text-sm font-semibold text-cyan-600">
                                  {formatCurrency(
                                    ReferralData[index]?.acceptorExtraAmount,
                                    ReferralData[index]?.country ===
                                      "United Kingdom"
                                      ? "£"
                                      : "$",
                                  )}
                                </div>
                              </div>
                            </>
                          )}
                        </CardContent>

                        <CardFooter>
                          {ReferralData && !isLoading && (
                            <UpdatePrice
                              mutate={mutate}
                              ReferralInfo={ReferralData[index]}
                            />
                          )}
                        </CardFooter>
                      </Card>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            </div>
          </div>
        </div>
      </Sidebar>
    );
}

type updatePriceProps = {
  mutate: () => void;
  ReferralInfo: tReferralAmountUpdate;
};

function UpdatePrice({ mutate, ReferralInfo }: updatePriceProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    referredAmount: yup.string().required("Referrer amount is required!"),
    acceptorAmount: yup.string().required("Referee amountis required!"),
    acceptorExtraAmount: yup.string().required("Extra bonus is required!"),
  });

  // Default Values
  const defaultValues = {
    referredAmount: ReferralInfo.referredAmount,
    acceptorAmount: ReferralInfo.acceptorAmount,
    acceptorExtraAmount: ReferralInfo.acceptorExtraAmount,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.post(
        `/referralConfig/update`,
        { ...data, id: ReferralInfo.id, country: ReferralInfo.country },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success(
          `${ReferralInfo.country} referral amounts updated successfully!`,
        );
        reset();
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(`Update failed for ${ReferralInfo.country}`);
    }
  };

  const reset = (value?: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="flex h-10 cursor-pointer items-center justify-center rounded-2xl bg-cyan-600 px-5 text-xs font-medium text-white hover:border hover:border-cyan-600 hover:bg-cyan-50 hover:text-cyan-600">
          Update Price
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">{`Update ${ReferralInfo.country === "United Kingdom" ? "UK" : "US"} Pricing`}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-row items-center space-x-2.5 py-4">
              <FormField
                control={form.control}
                name="referredAmount"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Referrer amount</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter referrer amount"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="acceptorAmount"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Referee amount</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter referee amount"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="acceptorExtraAmount"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Extra bonus (Referee)</FormLabel>
                  <div className="pb-1.5 text-[10px] font-extralight text-slate-500">
                    If the referee submits the form within 15 minutes, they'll
                    receive this extra bonus!
                  </div>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter bonus amount"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="py-2.5 text-xs font-medium leading-normal text-slate-600">
              <span className="font-bold">Note:</span> These amounts will be
              applied as referral bonuses. Please double-check carefully before
              updating!
            </div>

            <div className="mb-2 mt-5 flex w-full flex-row justify-end">
              <Button
                type="submit"
                className="w-28 rounded-2xl bg-cyan-600 text-white hover:border hover:border-cyan-600 hover:bg-white hover:text-cyan-600 "
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
