"use client";

import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import { fetcher } from "@fetchers/fetcher";
import { tAssignedStudent, tTutorC, tTutorD } from "types/tutor";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import {
  AssignStudents,
  AssignStudentsHeader,
  TutorInfo,
  TutorStatsCard,
} from "@components/Tutors/TutorDetailsList";
import EmptyDetailsList from "@components/Student/EmtyChildList";

import { GoogleMap, Marker, useLoadScript } from "@react-google-maps/api";
import { getMarker } from "core/marker";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import TutorStatSK from "@components/skeltons/Tutor/TutorStatSK";
import TutorInfoSK from "@components/skeltons/Tutor/TutorInfoSK";
import { useSession } from "next-auth/react";

import Comment, { AddComment } from "@components/global/Comment";

import dynamic from "next/dynamic";
import { useParams, useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

const api = "AIzaSyCS4J4FaeUIyhGM_XaDielBHx20yo9dzSM";
const libraries: string[] | any = ["places"]; // Define libraries outside the component

export default function Details() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const params = useParams<any>();
  const { id } = params;

  // const [center, setcenter] = useState<any>({
  //   lat: 51.5059683,
  //   lng: -0.1327297,
  // });
  // const [zoom, setZoom] = useState<number>(11);

  const [map, setMap] = useState<any>();

  const { isLoaded } = useLoadScript({
    id: "a7b6e1c544e85663",
    googleMapsApiKey: api,
    libraries: libraries, // Use the constant here
    language: "en",
  });

  const {
    data: Tutor,
    error,
    isLoading,
    mutate,
  } = useSWR<tTutorD>(id ? `/tutor/${id}` : null, fetcher);

  const {
    data: Comments,
    error: ErrorC,
    isLoading: isLoadingC,
    mutate: mutateC,
  } = useSWR<Array<tTutorC>>(id ? `/comment/${id}` : null, fetcher);

  // Set page title ...
  useEffect(() => {
    document.title = `${Tutor?.fullName ? Tutor.fullName : "Tutor Details"} | MuseCool Dashboard`;
  }, [Tutor?.fullName]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />
        <div className="flex h-full w-full flex-col">
          <div className="my-2  flex h-full w-full flex-row bg-[#f9f9f9] p-4">
            {/* //Left & Middle Section Wrapper */}
            <div className="hide-scrollbar flex h-full w-full flex-row overflow-y-scroll bg-[#f9f9f9]">
              {/* //Left Section */}
              <div className="flex h-full w-64 flex-col bg-[#f9f9f9]">
                {Tutor && !isLoading ? (
                  <TutorInfo tutor={Tutor} mutate={mutate} />
                ) : (
                  <TutorInfoSK />
                )}
              </div>

              {/* //Middle Section  */}
              <div className=" h-fit w-[calc(100%-320px)]  overflow-hidden bg-[#f9f9f9] pl-4">
                {/* // Tutor Statistic Section */}
                <div className="mb-6">
                  <div className="text-base font-semibold text-slate-600">
                    Tutor Statistics
                  </div>

                  {Tutor && !isLoading ? (
                    <TutorStatsCard tutorStats={Tutor.stats} />
                  ) : (
                    <TutorStatSK />
                  )}
                </div>

                {/* //Location Section  */}
                <>
                  <div className="text-base font-semibold text-slate-600">
                    Location
                  </div>

                  <div className="mb-7 mt-2 h-56 w-full rounded border border-slate-200  bg-slate-200">
                    {isLoaded ? (
                      <GoogleMap
                        center={Tutor?.location}
                        zoom={11}
                        mapContainerStyle={{
                          width: "100%",
                          height: "100%",
                        }}
                        // onBoundsChanged={filterOnBounds}
                        options={{
                          streetViewControl: false,
                          mapTypeControl: false,
                          panControl: true,
                          zoomControl: true,
                          minZoom: 11,
                          mapId: "a7b6e1c544e85663",
                        }}
                        onLoad={(map) => setMap(map)}
                      >
                        {/* Child components, such as markers, info windows, etc. */}
                        <>
                          <Marker
                            position={{
                              lat: Tutor?.location.lat || 0,
                              lng: Tutor?.location.lng || 0,
                            }}
                            icon={getMarker(Tutor?.rank ? Tutor.rank : 0)}
                          />
                        </>
                      </GoogleMap>
                    ) : (
                      <div className="flex h-56  items-center justify-center">
                        <div className="flex items-center text-sm font-medium text-slate-600">
                          <AiOutlineLoading3Quarters
                            color="blue"
                            className="mr-2 h-4 w-4 animate-spin"
                          />
                          Loading Tutor Location
                        </div>
                      </div>
                    )}
                  </div>
                </>

                {/* //Assign Student List.. */}
                <>
                  <div className="pb-2 text-base font-semibold text-slate-600">
                    Assigned Students
                  </div>

                  {Tutor?.assignedStudents &&
                  Tutor.assignedStudents.length >= 1 ? (
                    <AssignStudentsHeader />
                  ) : (
                    <EmptyDetailsList text="No student assigned!" />
                  )}

                  {Tutor?.assignedStudents.map(
                    (assignedStudent: tAssignedStudent) => (
                      <AssignStudents
                        key={assignedStudent.id}
                        student={assignedStudent}
                        mutate={mutate}
                      />
                    ),
                  )}
                </>
              </div>
            </div>

            {/* //Right Section Wrapper  */}
            <div className="hide-scrollbar h-auto w-80  overflow-y-scroll px-1">
              <div className="mb-2 text-base font-semibold text-slate-600">
                Comments
              </div>

              {Comments && Tutor && (!isLoadingC || !isLoading) ? (
                <>
                  <AddComment id={Tutor.id} mutate={mutateC} />
                  {Comments !== undefined && Comments.length > 0 && (
                    <div className="mt-6 rounded border border-slate-200 bg-white  px-3 py-2">
                      {/* // All Comment */}
                      {Comments?.map((comment: tTutorC) => (
                        <Comment
                          key={comment.id}
                          comment={comment}
                          mutate={mutateC}
                        />
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <TutorInfoSK />
              )}
            </div>
          </div>
        </div>
      </Sidebar>
    );
}
