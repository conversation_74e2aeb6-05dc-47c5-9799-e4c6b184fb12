"use client";

import { tPgT<PERSON><PERSON><PERSON>, tTutor<PERSON> } from "types/tutor";
import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useS<PERSON> from "swr";
import { pgfetcher } from "@fetchers/fetcher";
import { useForm } from "react-hook-form";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";

import PageList from "@components/PageList";
import Tutor, { TutorListHeader } from "@components/lists/Tutor";
import ListSkeltons from "@components/skeltons/ListSkeltons";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Input } from "@components/ui/input";
import { Button } from "@components/ui/button";
import { Dots } from "react-activity";
import { yupResolver } from "@hookform/resolvers/yup";
import { checkEmailAddress } from "core/Checker";
import * as yup from "yup";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { Textarea } from "@components/ui/textarea";
import { DataGrabber } from "core/Excel/exportTutor";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Tutors() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [selectedTutor, setSelectedTutor] = useState<tTutorL>();

  const [query, setQuery] = useState<string>("");
  const [name, setName] = useState<string>("");

  const {
    data: Tutors,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgTutorL>(`/tutor/list?p=${page}${query}`, pgfetcher);
  // console.log(Tutors);

  const Action = {
    UpdateQuery: (query: string) => {
      let queryWithPage = `?p=${page}${query}`;
      //   router.push("/tutors", queryWithPage, { shallow: true });
      router.push(`/tutors${queryWithPage}`);
      setQuery(query);
    },
    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/tutors", queryWithPage, { shallow: true });
      router.push(`/tutors${queryWithPage}`);
      setPage(newPage);
    },
    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      //   router.push("/tutors", queryWithPage, { shallow: true });
      router.push(`/tutors${queryWithPage}`);
    },
  };
  const Handelar = {
    UpdateName: (e: any) => {
      e.preventDefault();
      setName(e.target.value);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Tutors | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-4 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Tutor List
            </div>

            <div className="flex flex-1 flex-row items-center justify-end space-x-3 ">
              <div
                onClick={() => DataGrabber(query)}
                className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-4 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                Export
              </div>
              <NotifyTutor />
              <AddTutor mutate={mutate} />
              <Input
                type="search"
                placeholder="Name/Email"
                className="bg-white md:w-[100px] lg:w-[300px]"
                value={name}
                onChange={Handelar.UpdateName}
              />
            </div>
          </div>

          <TutorListHeader
            query={query}
            updateQuery={Action.UpdateQuery}
            name={name}
            setName={setName}
            updatePage={Action.UpdatePage}
            pageReset={Action.PageReset}
          />

          {isLoading ? (
            <ListSkeltons height={45} count={15} />
          ) : (
            Tutors?.data.map((tutor: tTutorL) => (
              <Tutor key={tutor.id} Tutor={tutor} mutate={mutate} />
            ))
          )}

          <PageList
            handler={Action.UpdatePage}
            page={Tutors?.page}
            itemName="Tutors"
          />
        </div>
      </Sidebar>
    );
}

type AddTutorProps = {
  mutate: () => void;
};
function AddTutor({ mutate }: AddTutorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    fullname: yup.string().required("Full name is required!"),
    email: yup
      .string()
      .required("Email is required!")
      .email(
        "Invalid Email Address. Please check if there is any extra space added.",
      )
      .test(
        "",
        "This email is already connected to an account. Please Login or reset your password.",
        async (value, values) => checkEmailAddress(value),
      ),
    birthDate: yup
      .string()
      // .required("Birthdate is required!")
      .notRequired()
      .test(
        "is-valid-format",
        "Invalid format! Please enter as '6th March' or '06 March', and ensure no extra spaces before or after the date.",
        (value) =>
          !value ||
          /^(0?[1-9]|[12][0-9]|3[01])(th|rd|nd|st)?\s+(January|February|March|April|May|June|July|August|September|October|November|December)$/i.test(
            value,
          ),
      ),

    country: yup.string().required("Country is required!"),
    // workPermit: yup.string().required("Work Permit is required!"),
  });

  const defaultValues = {
    email: "",
    fullname: "",
    country: "",
    workPermit: "",
    birthDate: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    // console.log("data", data);
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.post("/admin/add-tutor", data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        mutate();
        setIsLoading(false);

        toast.success("Tutor Added successfully!");
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Faild to add a tutor!");
    }
  };

  const ModalControl = {
    reset: (value?: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },
    openModal: () => {
      setIsOpen(true);
    },
  };

  return (
    <Dialog onOpenChange={ModalControl.reset} open={isOpen}>
      <DialogTrigger onClick={ModalControl.openModal}>
        <div className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-4 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add Tutor
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle>Add Tutor</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-2.5">
              <FormField
                control={form.control}
                name="fullname"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter full name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="text" placeholder="Enter email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="birthDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Birthdate</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="e.g. 6th March or 06 March"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className="capitalize">
                            {field.value ? field.value : "Select country"}
                          </div>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="United Kingdom">
                          United Kingdom
                        </SelectItem>
                        <SelectItem value="United States">
                          United States
                        </SelectItem>
                        <SelectItem value="Ukraine">Ukraine</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="workPermit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work permit</FormLabel>
                    <FormDescription>
                      Proof required before any teaching commences
                    </FormDescription>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <div className="max-w-80 truncate">
                            {field.value
                              ? field.value === "Self employed"
                                ? "I have the full right to work as a self employed person"
                                : field.value === "Student"
                                  ? "I am on a student visa"
                                  : field.value
                              : "Select a permit"}
                          </div>
                        </SelectTrigger>
                      </FormControl>

                      <SelectContent>
                        <SelectItem value="Self employed">
                          I have the full right to work as a self employed
                          person
                        </SelectItem>
                        <SelectItem value="Student">
                          I am on a student visa
                        </SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-6 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function NotifyTutor() {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    title: yup.string().required("Title is required!"),
    description: yup.string().required("Description is required!"),
  });

  const defaultValues = { title: "", description: "" };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {};

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };
  return (
    <div>
      <Dialog onOpenChange={reset} open={isOpen}>
        <DialogTrigger onClick={openModal}>
          <div className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-4 text-xs font-medium text-blue-50 hover:bg-blue-600">
            Notify
          </div>
        </DialogTrigger>
        <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Notify Tutor</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="py-3">
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input type="text" placeholder="Enter Title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="py-3">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter description" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className=" my-2 flex w-full flex-row justify-end space-x-1">
                <Button
                  type="submit"
                  className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                >
                  {isLoading ? <Dots /> : "Submit"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
