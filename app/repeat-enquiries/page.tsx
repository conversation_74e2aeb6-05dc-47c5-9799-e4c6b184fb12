"use client";

import React, { useEffect, useState } from "react";
import Enquiry, { EnquiryListHeader } from "components/lists/Enquiry";
import Sidebar from "components/Sidebar";
import Header from "components/Header";
import useSWR from "swr";
import { pgfetcher } from "fetchers/fetcher";
import PageList from "components/PageList";
import ListSkeltons from "components/skeltons/ListSkeltons";
import { getSession, useSession } from "next-auth/react";
import { dataGrabber } from "core/Excel/export";
import toast from "react-hot-toast";
import { Input } from "@components/ui/input";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import EmptyList from "@components/EmptyList";
import { tEnquiryL, tPgEnquiryL } from "types/rEnquiry";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function RepeatEnquiries() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<Number>(1);
  const [query, setQuery] = useState<string>("");
  const [name, setName] = useState<string>("");

  const {
    data: Enquiries,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgEnquiryL>(`/student/inquery-list?p=${page}${query}`, pgfetcher);

  const UpdateName = (e: any) => {
    e.preventDefault();
    setName(e.target.value);
  };

  const Action = {
    UpdateQuery: (query: string) => {
      let queryWithPage = `?p=${page}${query}`;
      router.push(`/repeat-enquiries${queryWithPage}`);
      
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      router.push(`/repeat-enquiries${queryWithPage}`);
      setPage(newPage);
    },

    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      router.push(`/repeat-enquiries${queryWithPage}`);
    },
  };

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Repeat Enquiries | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-4 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Repeat Enquiries
            </div>

            <div className="flex flex-row items-center justify-end">
              <Input
                type="search"
                placeholder="Name / Email"
                className="bg-white md:w-[100px] lg:w-[300px]"
                value={name}
                onChange={UpdateName}
              />
            </div>
          </div>

          <EnquiryListHeader
            updateQuery={Action.UpdateQuery}
            name={name}
            setName={setName}
            updatePage={Action.UpdatePage}
            pageReset={Action.PageReset}
          />

          <div className="w-full">
            {isLoading ? (
              <ListSkeltons height={42} count={30} />
            ) : Enquiries?.data.length === 0 ? (
              <EmptyList title="No Enquiries Found!" />
            ) : (
              <>
                {Enquiries?.data.map((enquiry: tEnquiryL) => (
                  <Enquiry enquiry={enquiry} key={enquiry.id} mutate={mutate} />
                ))}

                <PageList handler={Action.UpdatePage} page={Enquiries?.page} />
              </>
            )}
          </div>
        </div>
      </Sidebar>
    );
}
