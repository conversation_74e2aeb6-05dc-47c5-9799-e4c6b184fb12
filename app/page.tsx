"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import Sidebar from "components/Sidebar";
import Header from "components/Header";

//Libarary
import useSWR from "swr";

//Others
import { pgfetcher } from "fetchers/fetcher";

import { tLesson, tPgLesson } from "types/dashboard";
import DashbordCards from "components/DashbordCards";

import PageList from "components/PageList";

import ListSkeltons from "components/skeltons/ListSkeltons";

import RecentActivities from "components/RecentActivities";

import dynamic from "next/dynamic";

import EmptyStudentList from "@components/Student/EmptyStudentList";
import Lesson, { LessonListHeader } from "@components/lists/Lesson";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

// import Loading from "@components/lottie/Loading";

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");

  // Data Fetching
  const {
    data: Lessons,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgLesson>(`/lesson?p=${page}${query}`, pgfetcher);

  const Action = {
    UpdateQuery: (newQuery: string) => {
      let queryWithPage = `?p=${page}${newQuery}`;
      router.push(`/${queryWithPage}`);
      console.log("newQuery || Action", newQuery);
      setQuery(newQuery);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      router.push(`/${queryWithPage}`);
      setPage(newPage);
    },

    PageReset: (newQuery: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${newQuery}`;
      router.push(`/${queryWithPage}`);
      setQuery(newQuery);
      // console.log("newQuery || Action", newQuery);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Dashboard | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  // Early returns for loading states
  if (status === "loading") return <Loading />;
  else if (!session)
    return <Loading />; // Additional safety check
  else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <DashbordCards />

          <div className="mt-10 grid grid-cols-[4.15fr_1fr] gap-5">
            <div className="h-auto">
              <LessonListHeader
                updateQuery={Action.UpdateQuery}
                updatePage={Action.UpdatePage}
                pageReset={Action.PageReset}
              />

              {!isLoading && Lessons?.data && Lessons?.data.length === 0 && (
                <EmptyStudentList text="Empty! No lesson found!" />
              )}

              {isLoading ? (
                <ListSkeltons height={60} count={30} />
              ) : (
                Lessons?.data.map((lesson: tLesson) => (
                  <Lesson key={lesson.id} lesson={lesson} mutate={mutate} />
                ))
              )}

              {Lessons?.data && Lessons?.data.length > 0 && (
                <PageList handler={Action.UpdatePage} page={Lessons?.page} />
              )}
            </div>

            <div className="h-auto w-full pt-9">
              <div className="pb-1.5 text-base font-medium text-slate-600">
                Recent Activities
              </div>

              <div>
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
                <RecentActivities />
              </div>
            </div>
          </div>
        </div>
      </Sidebar>
    );
}
