import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials) => {
        const response = await fetch("https://api.lycaeumapp.com/auth/login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(credentials),
        });
        if (response.status == 200) {
          const user = await response.json();
          if (user.role === "Admin") {
            var date = new Date();
            let admin = user;
            admin.expires = new Date().setDate(date.getDate() + 59);
            return admin;
          }
        }
        return null;
      },
    }),
  ],
  callbacks: {
    jwt: async ({ token, user, account }) => {
      var now: Date = new Date();
      if (user && user.expires > now) {
        if (user) {
          token.token = user.token;
          token.user = user.fullName;
          token.role = user.role;
          token.expires = user.expires;
        }
      }
      return token;
    },
    session: async ({ session, token }) => {
      session.token = token.token;
      session.user = token.user;
      session.role = token.role;
      session.expires = token.expires;
      return session;
    },
  },
  secret: "new super secret key without marinaa",
  pages: {
    signIn: "/login",
  },
  session: {
    maxAge: 58 * 24 * 60 * 60,
  },
});

export { handler as GET, handler as POST };

// export default NextAuth({
//   providers: [
//     CredentialsProvider({
//       name: "credentials",
//       credentials: {
//         email: { label: "Email", type: "email" },
//         password: { label: "Password", type: "password" },
//       },
//       authorize: async (credentials) => {
//         const response = await fetch("https://api.lycaeumapp.com/auth/login", {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify(credentials),
//         });
//         if (response.status == 200) {
//           const user = await response.json();
//           if (user.role === "Admin") {
//             var date = new Date();
//             let admin = user;
//             admin.expires = new Date().setDate(date.getDate() + 59);
//             return admin;
//           }
//         }
//         return null;
//       },
//     }),
//   ],
//   callbacks: {
//     jwt: async ({ token, user, account }) => {
//       var now: Date = new Date();
//       if (user && user.expires > now) {
//         if (user) {
//           token.token = user.token;
//           token.user = user.fullName;
//           token.role = user.role;
//           token.expires = user.expires;
//         }
//       }
//       return token;
//     },
//     session: async ({ session, token }) => {
//       session.token = token.token;
//       session.user = token.user;
//       session.role = token.role;
//       session.expires = token.expires;
//       return session;
//     },
//   },
//   secret: "new super secret key without rakib",
//   pages: {
//     signIn: "/login",
//   },
//   session: {
//     maxAge: 58 * 24 * 60 * 60,
//   },
// });
