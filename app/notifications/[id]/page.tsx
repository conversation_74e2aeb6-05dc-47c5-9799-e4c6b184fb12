"use client";

import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { getSession, useSession } from "next-auth/react";
import moment from "moment";
import { Dots } from "react-activity";
import api from "fetchers/BaseUrl";
import toast from "react-hot-toast";
import { DateRange } from "react-day-picker";
import useSWR from "swr";
import { fetcher } from "@fetchers/fetcher";

// Components
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs";
import { Card, CardContent } from "@components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@components/ui/popover";
import { Calendar } from "@components/ui/calendar";
import { Skeleton } from "@components/ui/skeleton";

// Icons and Images
import { SlCalender } from "react-icons/sl";
import Image from "next/image";
import USFlag from "@assets/US.png";
import UKFlag from "@assets/Uk.png";
import { tNotificationD } from "types/notifications";

// Dynamic imports
const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

// Types
type UserType = "Student" | "Tutor";

// Editor configuration
const editorConfig = {
  readonly: false,
  toolbar: true,
  spellcheck: true,
  language: "en",
  toolbarAdaptive: false,
  showCharsCounter: false,
  showWordsCounter: false,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  buttons: [
    "bold",
    "strikethrough",
    "underline",
    "italic",
    "|",
    "superscript",
    "subscript",
    "|",
    "align",
    "|",
    "ul",
    "ol",
    "outdent",
    "indent",
    "|",
    "font",
    "fontsize",
    "brush",
    "paragraph",
    "|",
    "image",
    "link",
    "table",
    "|",
    "hr",
    "eraser",
    "copyformat",
    "|",
    "fullsize",
    "selectall",
    "print",
    "|",
    "source",
    "|",
  ],
  uploader: {
    insertImageAsBase64URI: true,
    imagesExtensions: ["jpg", "png", "jpeg", "gif", "svg", "webp"],
  },
  width: "100%",
  minHeight: "300px",
  height: "100%",
  iframe: true,
  theme: "default",
};

export default function UpdateTemplate() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams<any>();
  const { id } = params;
  const tabParam = searchParams?.get("tab");

  // Loading states
  const [isSubmitLoading, setIsSubmitLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState(
    tabParam === "us" ? "United States" : "United Kingdom",
  );

  // Single UserType for both countries
  const [userType, setUserType] = useState<string | null>(null);

  // UK data
  const [ukTitle, setUkTitle] = useState("");
  const [ukContent, setUkContent] = useState("");
  const [ukDateRange, setUkDateRange] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  // US data
  const [usTitle, setUsTitle] = useState("");
  const [usContent, setUsContent] = useState("");
  const [usDateRange, setUsDateRange] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  // Fetch template details
  const {
    data: templateData,
    error,
    isLoading,
    mutate,
  } = useSWR<tNotificationD>(id ? `/popup/${id}` : null, fetcher);

  // Function to update the URL when tabs change manually
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Optionally update URL without refreshing the page
    const tabQueryParam = value === "United States" ? "us" : "uk";
    router.push(`/notifications/${id}?tab=${tabQueryParam}`);
  };

  // Body Update Functions for JoditEditor
  const handleUkContentChange = async (value: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(value, "text/html");
    setUkContent(doc.body.innerHTML);
  };

  const handleUsContentChange = async (value: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(value, "text/html");
    setUsContent(doc.body.innerHTML);
  };

  // Populate form when data is loaded
  useEffect(() => {
    if (templateData) {
      setUserType(templateData.userType);

      // Set UK data
      setUkTitle(templateData.ukTitle || "");
      setUkContent(templateData.ukContent || "");
      if (templateData.ukStartDate && templateData.ukEndDate) {
        setUkDateRange({
          from: templateData.ukStartDate
            ? new Date(templateData.ukStartDate)
            : null,
          to: templateData.ukEndDate ? new Date(templateData.ukEndDate) : null,
        });
      }

      // Set US data
      setUsTitle(templateData.usTitle || "");
      setUsContent(templateData.usContent || "");
      if (templateData.usStartDate && templateData.usEndDate) {
        setUsDateRange({
          from: templateData.usStartDate
            ? new Date(templateData.usStartDate)
            : null,
          to: templateData.usEndDate ? new Date(templateData.usEndDate) : null,
        });
      }
    }
  }, [templateData]);

  // Update template
  const onSubmit = async () => {
    if (!userType) {
      toast.error("Please select a user type!");
      return;
    }

    // Check UK data and provide specific error messages
    const ukHasData =
      ukTitle || ukContent || ukDateRange?.from || ukDateRange?.to;
    let ukIsComplete = false;

    if (ukHasData) {
      if (!ukTitle) {
        toast.error("UK: Title is required when providing other data!");
        return;
      }
      if (!ukDateRange?.from) {
        toast.error("UK: Start date is required!");
        return;
      }
      if (!ukDateRange?.to) {
        toast.error("UK: End date is required!");
        return;
      }
      ukIsComplete = true;
    }

    // Check US data and provide specific error messages
    const usHasData =
      usTitle || usContent || usDateRange?.from || usDateRange?.to;
    let usIsComplete = false;

    if (usHasData) {
      if (!usTitle) {
        toast.error("US: Title is required when providing other data!");
        return;
      }
      if (!usDateRange?.from) {
        toast.error("US: Start date is required!");
        return;
      }
      if (!usDateRange?.to) {
        toast.error("US: End date is required!");
        return;
      }
      usIsComplete = true;
    }

    // Either UK or US must have complete data to submit
    if (!ukIsComplete && !usIsComplete) {
      toast.error("Please complete at least one country's information!");
      return;
    }

    setIsSubmitLoading(true);

    // Format dates to match API expectations
    const formatDate = (date: Date | undefined) => {
      return date ? moment(date).format("YYYY-MM-DDTHH:mm:ss") : "";
    };

    // Prepare API payload - only include complete country data
    const payload: any = {
      userType,
    };

    // Add UK data if complete
    if (ukIsComplete) {
      payload.ukTitle = ukTitle;
      payload.ukContent = ukContent || "";
      payload.ukStartDate = formatDate(ukDateRange?.from);
      payload.ukEndDate = formatDate(ukDateRange?.to);
    }

    // Add US data if complete
    if (usIsComplete) {
      payload.usTitle = usTitle;
      payload.usContent = usContent || "";
      payload.usStartDate = formatDate(usDateRange?.from);
      payload.usEndDate = formatDate(usDateRange?.to);
    }

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.put(`/popup/${id}`, payload, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        // Success message based on what was updated
        let successMessage = "Template Updated Successfully!";
        if (ukIsComplete && usIsComplete) {
          successMessage = "UK and US Templates Updated Successfully!";
        } else if (ukIsComplete) {
          successMessage = "UK Template Updated Successfully!";
        } else if (usIsComplete) {
          successMessage = "US Template Updated Successfully!";
        }
        toast.success(successMessage);
        router.push("/notifications");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      toast.error("Failed to Update Template! Please Try Again.");
      console.error("Error:", error);
    } finally {
      setIsSubmitLoading(false);
    }
  };

  const resetForm = () => {
    if (templateData) {
      // Reset to original user type
      if (
        templateData.userType === "Student" ||
        templateData.userType === "Tutor"
      ) {
        setUserType(templateData.userType);
      }

      // Reset UK data to original values
      setUkTitle(templateData.ukTitle || "");
      setUkContent(templateData.ukContent || "");
      if (templateData.ukStartDate && templateData.ukEndDate) {
        setUkDateRange({
          from: templateData.ukStartDate
            ? new Date(templateData.ukStartDate)
            : null,
          to: templateData.ukEndDate ? new Date(templateData.ukEndDate) : null,
        });
      } else {
        setUkDateRange({
          from: null,
          to: null,
        });
      }

      // Reset US data to original values
      setUsTitle(templateData.usTitle || "");
      setUsContent(templateData.usContent || "");
      if (templateData.usStartDate && templateData.usEndDate) {
        setUsDateRange({
          from: templateData.usStartDate
            ? new Date(templateData.usStartDate)
            : null,
          to: templateData.usEndDate ? new Date(templateData.usEndDate) : null,
        });
      } else {
        setUsDateRange({
          from: null,
          to: null,
        });
      }
    } else {
      // Fallback to empty values if no template data
      setUserType(null);
      setUkTitle("");
      setUkContent("");
      setUkDateRange({
        from: null,
        to: null,
      });
      setUsTitle("");
      setUsContent("");
      setUsDateRange({
        from: null,
        to: null,
      });
    }
  };

  // Auth check
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Update Template | MuseCool Dashboard";
  }, []);

  if (status === "loading" || isLoading) return <Loading />;
  else if (!session) {
    return <Loading />;
  } else if (error) {
    return (
      <Sidebar>
        <Header />
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600">Error</h1>
            <p className="text-gray-600">Failed to load template data</p>
          </div>
        </div>
      </Sidebar>
    );
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar h-full w-full overflow-y-auto pb-16 pt-4 md:pt-6">
          <div className="container mx-auto flex min-h-full items-center justify-center px-4">
            <div className="w-full max-w-3xl">
              <div className="mx-auto w-auto overflow-hidden rounded-3xl bg-white shadow-md">
                <div className="flex flex-col">
                  <div className="bg-cyan-600 px-0 py-5 text-center text-lg font-bold text-white">
                    Update {templateData?.userType} Notification Message
                  </div>

                  <div className="space-y-4 p-6">
                    {/* UserType Selection - Before Tabs */}
                    <div className="mb-6 w-1/2">
                      <label className="text-sm font-semibold text-slate-600">
                        User Type
                      </label>
                      <Select
                        onValueChange={(value: string) => {
                          setUserType(value as UserType);
                        }}
                        value={userType || ""}
                      >
                        <SelectTrigger className="mt-1 w-full rounded-2xl bg-white text-center">
                          <SelectValue placeholder="Select user type" />
                        </SelectTrigger>
                        <SelectContent className="text-xs font-normal text-gray-700">
                          <SelectGroup>
                            <SelectItem value="Student">Student</SelectItem>
                            <SelectItem value="Tutor">Tutor</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>

                    <Tabs
                      value={activeTab}
                      onValueChange={handleTabChange}
                      className="w-full"
                    >
                      <TabsList className="grid h-12 w-full flex-shrink-0 grid-cols-2 rounded-3xl px-0 py-0">
                        <TabsTrigger
                          className="h-full rounded-3xl py-0 data-[state=active]:bg-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-none"
                          value="United Kingdom"
                        >
                          <Image
                            src={UKFlag}
                            alt="UK Flag"
                            className="mr-1.5 h-6 w-6"
                          />
                          United Kingdom
                        </TabsTrigger>
                        <TabsTrigger
                          className="h-full rounded-3xl py-0 data-[state=active]:bg-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-none"
                          value="United States"
                        >
                          <Image
                            src={USFlag}
                            alt="US Flag"
                            className="mr-1.5 h-6 w-6"
                          />
                          United States
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="United Kingdom" className="mt-4">
                        <Card className="border-0 shadow-none">
                          <CardContent className="space-y-4 p-4">
                            <div className="flex w-full flex-row items-center space-x-2.5">
                              {/* UK Title */}
                              <div className="w-1/2">
                                <label className="text-sm font-semibold text-slate-600">
                                  Title
                                </label>
                                <Input
                                  type="text"
                                  placeholder="Enter title"
                                  value={ukTitle}
                                  onChange={(e) => setUkTitle(e.target.value)}
                                  className="w-full bg-white text-xs font-normal text-slate-600"
                                />
                              </div>

                              {/* UK Date Range */}
                              <div className="flex-1">
                                <label className="text-sm font-semibold text-slate-600">
                                  Date range
                                </label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      id="date"
                                      variant={"outline"}
                                      className="w-full items-center justify-start bg-white px-4 text-left font-normal"
                                    >
                                      <SlCalender size={16} className="mr-2" />
                                      {ukDateRange?.from ? (
                                        ukDateRange.to ? (
                                          <>
                                            {moment(ukDateRange.from).format(
                                              "MMM D, YYYY",
                                            )}{" "}
                                            -{" "}
                                            {moment(ukDateRange.to).format(
                                              "MMM D, YYYY",
                                            )}
                                          </>
                                        ) : (
                                          moment(ukDateRange.from).format(
                                            "MMM D, YYYY",
                                          )
                                        )
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className="w-auto p-0"
                                    align="start"
                                  >
                                    <Calendar
                                      initialFocus
                                      mode="range"
                                      defaultMonth={ukDateRange?.from}
                                      selected={ukDateRange}
                                      onSelect={setUkDateRange}
                                      numberOfMonths={2}
                                    />
                                  </PopoverContent>
                                </Popover>
                              </div>
                            </div>

                            {/* UK Content Editor */}
                            <div>
                              <label className="text-sm font-semibold text-slate-600">
                                Notification message
                              </label>
                              <div
                                style={{
                                  maxWidth: editorConfig.width,
                                  minHeight: "300px",
                                  margin: "5px auto",
                                  fontSize: "13px",
                                  lineHeight: "250%",
                                  fontWeight: "400",
                                }}
                              >
                                <JoditEditor
                                  value={ukContent}
                                  config={editorConfig}
                                  onChange={handleUkContentChange}
                                  className="editor"
                                />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>

                      <TabsContent value="United States" className="mt-4">
                        <Card className="border-0 shadow-none">
                          <CardContent className="space-y-4 p-4">
                            <div className="flex w-full flex-row items-center space-x-2.5">
                              {/* US Title */}
                              <div className="w-1/2">
                                <label className="text-sm font-semibold text-slate-600">
                                  Title
                                </label>
                                <Input
                                  type="text"
                                  placeholder="Enter title"
                                  value={usTitle}
                                  onChange={(e) => setUsTitle(e.target.value)}
                                  className="w-full bg-white text-xs font-normal text-slate-600"
                                />
                              </div>

                              {/* US Date Range */}
                              <div className="flex-1">
                                <label className="text-sm font-semibold text-slate-600">
                                  Date range
                                </label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      id="date"
                                      variant={"outline"}
                                      className="w-full items-center justify-start bg-white px-4 text-left font-normal"
                                    >
                                      <SlCalender size={16} className="mr-2" />
                                      {usDateRange?.from ? (
                                        usDateRange.to ? (
                                          <>
                                            {moment(usDateRange.from).format(
                                              "MMM D, YYYY",
                                            )}{" "}
                                            -{" "}
                                            {moment(usDateRange.to).format(
                                              "MMM D, YYYY",
                                            )}
                                          </>
                                        ) : (
                                          moment(usDateRange.from).format(
                                            "MMM D, YYYY",
                                          )
                                        )
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent
                                    className="w-auto p-0"
                                    align="start"
                                  >
                                    <Calendar
                                      initialFocus
                                      mode="range"
                                      defaultMonth={usDateRange?.from}
                                      selected={usDateRange}
                                      onSelect={setUsDateRange}
                                      numberOfMonths={2}
                                    />
                                  </PopoverContent>
                                </Popover>
                              </div>
                            </div>

                            {/* US Content Editor */}
                            <div>
                              <label className="text-sm font-semibold text-slate-600">
                                Notification message
                              </label>
                              <div
                                style={{
                                  maxWidth: editorConfig.width,
                                  minHeight: "300px",
                                  margin: "5px auto",
                                  fontSize: "13px",
                                  lineHeight: "250%",
                                  fontWeight: "400",
                                }}
                              >
                                <JoditEditor
                                  value={usContent}
                                  config={editorConfig}
                                  onChange={handleUsContentChange}
                                  className="editor"
                                />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>
                    </Tabs>

                    {/* Button Section - After Tabs */}
                    <div className="mt-6 flex w-full flex-row justify-end space-x-2">
                      <Button
                        type="button"
                        onClick={resetForm}
                        className="border border-black bg-white text-black hover:bg-black hover:text-white"
                      >
                        Reset
                      </Button>
                      <Button
                        onClick={onSubmit}
                        className="ml-2 border border-cyan-600 bg-cyan-600 text-white hover:bg-cyan-700"
                        disabled={isSubmitLoading}
                      >
                        {isSubmitLoading ? <Dots /> : "Update Information"}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Sidebar>
    );
}
