"use client";

import Header from "@components/Header";
import PageList from "@components/PageList";
import Sidebar from "@components/Sidebar";

import { fetcher, pgfetcher } from "@fetchers/fetcher";
import { tPgTemplateL, tTemplateD, tTemplateL } from "types/template";
import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { useForm } from "react-hook-form";
import * as yup from "yup";

import { yupResolver } from "@hookform/resolvers/yup";
import { Textarea } from "@components/ui/textarea";
import { Dots } from "react-activity";

import api from "fetchers/BaseUrl";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs";

import { Button } from "@components/ui/button";
import Template, { TemplateListHeader } from "@components/lists/template";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { Input } from "@components/ui/input";

import dynamic from "next/dynamic";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

import { EditorProps } from "react-draft-wysiwyg";
import { useRouter } from "next/navigation";
import { tNotificationL, tPgNotificationL } from "types/notifications";
import Notification, {
  NotificationListHeader,
} from "@components/lists/Notification";
import moment from "moment";
import USFlag from "@assets/US.png";
import UKFlag from "@assets/Uk.png";
import NotificationNF from "@assets/notifaciton-nf.png";

import { Calendar } from "components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { SlCalender } from "react-icons/sl";
import Image from "next/image";
import { DateRange } from "react-day-picker"; // Add this import
import EmptyList from "@components/EmptyList";

const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });

export default function Notifications() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");

  // Data Fetching ...

  const {
    data: NotificationTemplates,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgNotificationL>(`/popup/list?p=${page}`, pgfetcher);

  const Action = {
    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;

      router.push(`/notifications${queryWithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;

      router.push(`/notifications${queryWithPage}`);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else {
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Notification | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-4 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Notification Templates
            </div>

            <div className="flex flex-row items-center justify-end">
              <AddTemplate mutate={mutate} />
            </div>
          </div>

          <NotificationListHeader />

          <div className="w-full">
            {isLoading ? (
              <ListSkeltons height={40} count={30} />
            ) : NotificationTemplates?.data.length === 0 ? (
              <EmptyList
                title="No templates available yet"
                des={`Click "Add Template" to create your first notification template.`}
                image={NotificationNF}
                imageAlt="MuseCool"
                imageHeight={200}
                imageWidth={250}
              />
            ) : (
              <>
                {NotificationTemplates?.data.map(
                  (notification: tNotificationL) => (
                    <Notification
                      key={notification.id}
                      template={notification}
                      mutate={mutate}
                    />
                  ),
                )}

                <PageList
                  handler={Action.UpdatePage}
                  page={NotificationTemplates?.page}
                />
              </>
            )}
          </div>

          {/* {isLoading ? (
            <ListSkeltons height={40} count={30} />
          ) : (
            NotificationTemplates?.data.map((notification: tNotificationL) => (
              <Notification
                key={notification.id}
                template={notification}
                mutate={mutate}
              />
            ))
          )}

          <PageList
            handler={Action.UpdatePage}
            page={NotificationTemplates?.page}
          /> */}
        </div>
      </Sidebar>
    );
}

type AddTempProps = {
  mutate: () => void;
};

// Types

type Country = "United Kingdom" | "United States";
type UserType = "student" | "tutor";

type CountryData = {
  body: string;
  title: string;
  date: {
    from: Date;
    to: Date;
  };
};

type TemplateData = {
  student: Record<Country, CountryData>;
  tutor: Record<Country, CountryData>;
};

const buttons = [
  // "undo",
  // "redo",
  // "|",
  "bold",
  "strikethrough",
  "underline",
  "italic",
  "|",
  "superscript",
  "subscript",
  "|",
  "align",
  "|",
  "ul",
  "ol",
  "outdent",
  "indent",
  "|",
  "font",
  "fontsize",
  "brush",
  "paragraph",
  "|",
  "image",
  "link",
  "table",
  "|",
  "hr",
  "eraser",
  "copyformat",
  "|",
  "fullsize",
  "selectall",
  "print",
  "|",
  "source",
  "|",
];

const editorConfig = {
  readonly: false,
  toolbar: true,
  spellcheck: true,
  language: "en",
  toolbarAdaptive: false,
  // toolbarButtonSize: "small", // Set to an appropriate size (e.g., "small", "medium", etc.)
  showCharsCounter: false,
  showWordsCounter: false,
  showXPathInStatusbar: false,
  askBeforePasteHTML: true,
  askBeforePasteFromWord: true,
  // defaultActionOnPaste: "insert_clear_html",
  buttons: buttons, // Make sure you define the 'buttons' array
  uploader: {
    insertImageAsBase64URI: true,
    imagesExtensions: ["jpg", "png", "jpeg", "gif", "svg", "webp"],
  },
  width: "100%",
  minHeight: "300px",
  height: "100%",
  iframe: true,

  theme: "default",
};
function AddTemplate({ mutate }: AddTempProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("United Kingdom");

  // Single UserType for both countries
  const [userType, setUserType] = useState<UserType | null>(null);

  // UK data
  const [ukTitle, setUkTitle] = useState("");
  const [ukContent, setUkContent] = useState("");
  const [ukDateRange, setUkDateRange] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  // US data
  const [usTitle, setUsTitle] = useState("");
  const [usContent, setUsContent] = useState("");
  const [usDateRange, setUsDateRange] = useState<DateRange | any>({
    from: null,
    to: null,
  });

  // Body Update Functions for JoditEditor
  const handleUkContentChange = async (value: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(value, "text/html");
    setUkContent(doc.body.innerHTML);
  };

  const handleUsContentChange = async (value: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(value, "text/html");
    setUsContent(doc.body.innerHTML);
  };

  // Single submit function for both countries
  const onSubmit = async () => {
    if (!userType) {
      toast.error("Please select a user type!");
      return;
    }

    // Check if user has started filling any UK data
    const ukHasAnyData =
      ukTitle || ukContent || ukDateRange?.from || ukDateRange?.to;

    // Check if user has started filling any US data
    const usHasAnyData =
      usTitle || usContent || usDateRange?.from || usDateRange?.to;

    // If user has started UK data, validate required fields
    if (ukHasAnyData) {
      if (!ukTitle) {
        toast.error("🇬🇧 UK: Please enter a title to continue");
        return;
      }
      if (!ukDateRange?.from) {
        toast.error("🇬🇧 UK: Please select a start date");
        return;
      }
      if (!ukDateRange?.to) {
        toast.error("🇬🇧 UK: Please select an end date");
        return;
      }
    }

    // If user has started US data, validate required fields
    if (usHasAnyData) {
      if (!usTitle) {
        toast.error("🇺🇸 US: Please enter a title to continue");
        return;
      }
      if (!usDateRange?.from) {
        toast.error("🇺🇸 US: Please select a start date");
        return;
      }
      if (!usDateRange?.to) {
        toast.error("🇺🇸 US: Please select an end date");
        return;
      }
    }

    // Check if user has complete data for at least one country
    const ukHasDataToSubmit = ukTitle && ukDateRange?.from && ukDateRange?.to;
    const usHasDataToSubmit = usTitle && usDateRange?.from && usDateRange?.to;

    // Either UK or US must have complete data to submit
    if (!ukHasDataToSubmit && !usHasDataToSubmit) {
      if (!ukHasAnyData && !usHasAnyData) {
        toast.error(
          "Please fill in information for at least one country (UK or US)",
        );
      } else {
        toast.error(
          "Please complete the required fields (title, start date, and end date) for at least one country",
        );
      }
      return;
    }

    setIsLoading(true);

    // Format dates to match API expectations
    const formatDate = (date: Date | undefined) => {
      return date ? moment(date).format("YYYY-MM-DDTHH:mm:ss") : "";
    };

    // Prepare API payload - only include complete country data
    const payload: any = {
      userType,
    };

    // Add UK data if complete
    if (ukHasDataToSubmit) {
      payload.ukTitle = ukTitle;
      payload.ukContent = ukContent || ""; // Include content even if empty
      payload.ukStartDate = formatDate(ukDateRange.from);
      payload.ukEndDate = formatDate(ukDateRange.to);
    }

    // Add US data if complete
    if (usHasDataToSubmit) {
      payload.usTitle = usTitle;
      payload.usContent = usContent || ""; // Include content even if empty
      payload.usStartDate = formatDate(usDateRange.from);
      payload.usEndDate = formatDate(usDateRange.to);
    }

    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post("/popup/", payload, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        mutate();
        // Success message based on what was submitted
        let successMessage = "Template Added Successfully!";
        if (ukHasDataToSubmit && usHasDataToSubmit) {
          successMessage = "UK and US Templates Added Successfully!";
        } else if (ukHasDataToSubmit) {
          successMessage = "UK Template Added Successfully!";
        } else if (usHasDataToSubmit) {
          successMessage = "US Template Added Successfully!";
        }
        toast.success(successMessage);
        setIsOpen(false); // Close modal on success
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      toast.error("Failed to Add Template! Please Try Again.");
      console.error("Error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setUserType(null);
    setUkTitle("");
    setUkContent("");
    setUkDateRange({
      from: null,
      to: null,
    });
    setUsTitle("");
    setUsContent("");
    setUsDateRange({
      from: null,
      to: null,
    });
    setIsLoading(false);
  };

  const reset = (value?: any) => {
    setIsOpen(value);
    resetForm();
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="mx-3 flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add Template
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-[90vh] overflow-y-auto sm:w-full sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="px-4 text-center text-xl font-bold text-cyan-600">
            Add Notification Message
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 p-6">
          {/* UserType Selection - Before Tabs */}
          <div className="mb-6 w-1/2">
            <label className="text-sm font-semibold text-slate-600">
              User Type
            </label>

            <Select
              onValueChange={(value: string) => {
                setUserType(value as UserType);
              }}
              value={userType || ""}
            >
              <SelectTrigger className="mt-1 w-full rounded-2xl bg-white">
                <SelectValue placeholder="Select user type" />
              </SelectTrigger>
              <SelectContent className="text-xs font-normal text-gray-700">
                <SelectGroup>
                  <SelectItem value="Student">Student</SelectItem>
                  <SelectItem value="Tutor">Tutor</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid h-12 w-full flex-shrink-0 grid-cols-2 rounded-3xl px-0 py-0">
              <TabsTrigger
                className="h-full rounded-3xl py-0 data-[state=active]:bg-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-none"
                value="United Kingdom"
              >
                <Image src={UKFlag} alt="UK Flag" className="mr-1.5 h-6 w-6" />
                United Kingdom
              </TabsTrigger>
              <TabsTrigger
                className="h-full rounded-3xl py-0 data-[state=active]:bg-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-none"
                value="United States"
              >
                <Image src={USFlag} alt="US Flag" className="mr-1.5 h-6 w-6" />
                United States
              </TabsTrigger>
            </TabsList>

            <TabsContent value="United Kingdom" className="mt-4">
              <Card className="border-0 shadow-none">
                <CardContent className="space-y-4 p-4">
                  {/* UK Section Header */}
                  {/* <div className="mb-4 rounded-lg bg-blue-50 p-3">
                    <p className="text-sm font-medium text-blue-800">
                      Fill in UK information (optional)
                    </p>
                    <p className="text-xs text-blue-600">
                      If you enter UK data, title and date range are required
                    </p>
                  </div> */}

                  <div className="flex flex-row items-center space-x-2.5">
                    {/* UK Title */}
                    <div className="w-1/2 flex-shrink-0">
                      <label className="text-sm font-semibold text-slate-600">
                        Title
                      </label>
                      <Input
                        type="text"
                        placeholder="Enter title"
                        value={ukTitle}
                        onChange={(e) => setUkTitle(e.target.value)}
                        className="w-full bg-white text-xs font-normal text-slate-600"
                      />
                    </div>

                    {/* UK Date Range */}
                    <div className="flex-1">
                      <label className="text-sm font-semibold text-slate-600">
                        Date range
                      </label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="date"
                            variant={"outline"}
                            className="w-full items-center justify-start bg-white px-4 text-left font-normal"
                          >
                            <SlCalender size={16} className="mr-2" />
                            {ukDateRange?.from ? (
                              ukDateRange.to ? (
                                <>
                                  {moment(ukDateRange.from).format(
                                    "MMM D, YYYY",
                                  )}{" "}
                                  -{" "}
                                  {moment(ukDateRange.to).format("MMM D, YYYY")}
                                </>
                              ) : (
                                moment(ukDateRange.from).format("MMM D, YYYY")
                              )
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            initialFocus
                            mode="range"
                            defaultMonth={ukDateRange?.from}
                            selected={ukDateRange}
                            onSelect={setUkDateRange}
                            numberOfMonths={2}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* UK Content Editor */}
                  <div>
                    <label className="text-sm font-semibold text-slate-600">
                      Notification message
                    </label>
                    <div
                      style={{
                        maxWidth: editorConfig.width,
                        minHeight: "300px",
                        margin: "5px auto",
                        fontSize: "13px",
                        lineHeight: "250%",
                        fontWeight: "400",
                      }}
                    >
                      <JoditEditor
                        value={ukContent}
                        config={editorConfig}
                        onChange={handleUkContentChange}
                        className="editor"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="United States" className="mt-4">
              <Card className="border-0 shadow-none">
                <CardContent className="space-y-4 p-4">
                  {/* US Section Header */}
                  {/* <div className="mb-4 rounded-lg bg-green-50 p-3">
                    <p className="text-sm font-medium text-green-800">
                      Fill in US information (optional)
                    </p>
                    <p className="text-xs text-green-600">
                      If you enter US data, title and date range are required
                    </p>
                  </div> */}

                  <div className="flex flex-row items-center space-x-2.5">
                    {/* US Title */}
                    <div className="w-1/2">
                      <label className="text-sm font-semibold text-slate-600">
                        Title
                      </label>
                      <Input
                        type="text"
                        placeholder="Enter title"
                        value={usTitle}
                        onChange={(e) => setUsTitle(e.target.value)}
                        className="w-full bg-white text-xs font-normal text-slate-600"
                      />
                    </div>

                    {/* US Date Range */}
                    <div className="flex-1">
                      <label className="text-sm font-semibold text-slate-600">
                        Date range
                      </label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="date"
                            variant={"outline"}
                            className="w-full items-center justify-start bg-white px-4 text-left font-normal"
                          >
                            <SlCalender size={16} className="mr-2" />
                            {usDateRange?.from ? (
                              usDateRange.to ? (
                                <>
                                  {moment(usDateRange.from).format(
                                    "MMM D, YYYY",
                                  )}{" "}
                                  -{" "}
                                  {moment(usDateRange.to).format("MMM D, YYYY")}
                                </>
                              ) : (
                                moment(usDateRange.from).format("MMM D, YYYY")
                              )
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            initialFocus
                            mode="range"
                            defaultMonth={usDateRange?.from}
                            selected={usDateRange}
                            onSelect={setUsDateRange}
                            numberOfMonths={2}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* US Content Editor */}
                  <div>
                    <label className="text-sm font-semibold text-slate-600">
                      Notification message
                    </label>
                    <div
                      style={{
                        maxWidth: editorConfig.width,
                        minHeight: "300px",
                        margin: "5px auto",
                        fontSize: "13px",
                        lineHeight: "250%",
                        fontWeight: "400",
                      }}
                    >
                      <JoditEditor
                        value={usContent}
                        config={editorConfig}
                        onChange={handleUsContentChange}
                        className="editor"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Single Button Section - After Tabs */}
          <div className="mt-6 flex w-full flex-row justify-end space-x-2">
            <Button
              type="button"
              onClick={() => setIsOpen(false)}
              className="border border-red-600 bg-white text-red-600 hover:bg-red-600 hover:text-white"
            >
              Cancel
            </Button>
            <Button
              onClick={onSubmit}
              className="ml-2 border border-cyan-600 bg-cyan-600 text-white hover:bg-cyan-700"
              disabled={isLoading}
            >
              {isLoading ? <Dots /> : "Add Information"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
