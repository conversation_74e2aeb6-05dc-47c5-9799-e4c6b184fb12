"use client";

/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import Sidebar from "components/Sidebar";
import { BiMapPin, BiRightArrow } from "react-icons/bi";
import {
  Autocomplete,
  Circle,
  GoogleMap,
  InfoBox,
  Marker,
  useLoadScript,
} from "@react-google-maps/api";
import useSWR from "swr";
import { fetcher } from "fetchers/fetcher";
import { tMapTutor, tMapTutorInfo } from "types/tutor";
import moment from "moment";
import toast from "react-hot-toast";
import { SendNotifications } from "fetchers/notification";
import { useForm } from "react-hook-form";
import { getMarker } from "core/marker";

import { getSession, useSession } from "next-auth/react";

import { RiDeleteBinFill } from "react-icons/ri";

import * as yup from "yup";
import { Textarea } from "@components/ui/textarea";
import { Dots } from "react-activity";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import { Checkbox } from "@components/ui/checkbox";
import { Label } from "@components/ui/label";
import { Input } from "@components/ui/input";
import { FaMinusSquare, FaPlusSquare, FaRegEdit } from "react-icons/fa";
import { yupResolver } from "@hookform/resolvers/yup";
import { Button } from "@components/ui/button";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { GeoCoding } from "core/Map/Map";
import Api from "@fetchers/BaseUrl";
import { checkPostCode } from "core/Map/ProfileValidators";
import { FaUserEdit } from "react-icons/fa";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

const api = "AIzaSyCS4J4FaeUIyhGM_XaDielBHx20yo9dzSM";
const libraries: string[] | any = ["places"]; // Define libraries

export default function TutorMap() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [map, setMap] = useState<any>();
  const [center, setcenter] = useState<any>({
    lat: 51.5059683,
    lng: -0.1327297,
  });
  const [hover, setHover] = useState<tMapTutor | any>();
  const [student, setStudent] = useState<any>();
  const [zoom, setZoom] = useState<number>(11);
  const [radius, setRadius] = useState<number>(5);
  const [isChecked, setIsChecked] = useState<boolean>(false);
  const [Instrument, setInstrument] = useState<any>("");
  const [query, setQuery] = useState<string>();
  const autocomplete = useRef<any>(undefined);

  const [selectedList, setSelectedList] = useState<Array<tMapTutor>>([]);

  const [tutorInfoM, setTutorInfoM] = useState(false);
  const [tutorId, setTutorId] = useState<string>("");

  // For Edit Tutor Profile

  const [isEditProfile, setIsEditProfile] = useState<boolean>(false);

  const [students, setStudents] = useState<number | null>(null);

  const [gender, setGender] = useState<string>("");

  const OpenEditProfile = () => {
    setIsEditProfile(true);
  };
  const CloseEditProfile = () => {
    setIsEditProfile(false);
  };

  // Data Fetching...
  const { data: tutors, mutate } = useSWR<Array<tMapTutor>>(
    `/map/tutors?${query}`,
    fetcher,
  );

  const { isLoaded } = useLoadScript({
    id: "a7b6e1c544e85663",
    googleMapsApiKey: api,
    libraries: libraries,
    language: "en",
  });

  const updateCenter = () => {
    if (autocomplete.current) {
      let lat = autocomplete.current.getPlace().geometry.location.lat();
      let lng = autocomplete.current.getPlace().geometry.location.lng();
      setcenter({ lat, lng });
      setStudent({ lat, lng });
      setZoom(13);
    }
  };

  // Tutor Infor Modal Controlar...
  const openTutorInfoM = async (id: string) => {
    setTutorId(id);
    setTutorInfoM(true);
    CloseEditProfile();
  };

  const copyEmailToClipboard = async () => {
    var emails = selectedList.map((item) => item.email);
    navigator.clipboard.writeText(emails.toString());
    toast.success("Emails copied to clipboard!");
  };

  // const filterOnBounds = () => {
  //     let mxLat = map.getBounds().getNorthEast().lat();
  //     let mxLng = map.getBounds().getNorthEast().lng();
  //     let mLat = map.getBounds().getSouthWest().lat();
  //     let mLng = map.getBounds().getSouthWest().lng();
  //     // console.log(aNord, aSud)
  //     // console.log(aEst, aOvest)
  //     const data = new URLSearchParams({ Instrument })
  //     setQuery(data.toString())
  // }

  const AddToSelected = (user: tMapTutor) => {
    setSelectedList((old) => {
      const included = old.includes(user);
      if (included) return old;
      return [...old, user];
    });
  };

  const RemoveFromSelected = (user: tMapTutor) => {
    setSelectedList((old) => old.filter((element) => element != user));
  };

  const handleRadiusInput = (e: any) => {
    setRadius(e.target.value);
  };

  const inRadius = () => {
    setRadius(Number(radius) + 1);
  };

  const deRadius = () => {
    if (radius > 1) setRadius(Number(radius) - 1);
  };

  useEffect(() => {
    if (tutors == undefined) {
      toast.loading("Loading Tutors...", {
        id: "tutormap",
        position: "top-center",
      });
    } else toast.remove("tutormap");
  }, [tutors]);

  useEffect(() => {
    if (!student) return;

    const params = new URLSearchParams();

    if (Instrument) {
      params.set("Instrument", Instrument);
    }

    if (gender) {
      params.set("gender", gender);
    }

    if (students !== null && students !== undefined) {
      params.set("students", students.toString());
    }

    setQuery(params.toString());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [Instrument, gender, students, student]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Tutor Maps | MuseCool Dashboard";
  }, []);

  if (!isLoaded) return null;

  //Tutor Info Handelar..
  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar isCollapsed>
        <div className="flex h-screen w-full">
          <div className=" h-full w-4/5">
            {/* Google Map View - Left Part */}
            <GoogleMap
              center={center}
              zoom={zoom}
              mapContainerStyle={{ width: "100%", height: "100%" }}
              // onBoundsChanged={filterOnBounds}
              options={{
                streetViewControl: false,
                mapTypeControl: false,
                panControl: true,
                zoomControl: true,
                minZoom: 11,
                mapId: "a7b6e1c544e85663",
              }}
              onLoad={(map) => setMap(map)}
            >
              {student && (
                <Marker
                  position={student}
                  icon="https://maps.google.com/mapfiles/kml/paddle/wht-stars.png"
                />
              )}

              {tutors &&
                tutors.map((data) => (
                  <Marker
                    key={data.id}
                    position={{ lat: data.lat, lng: data.lng }}
                    icon={getMarker(data.rank)}
                    onMouseOver={({ latLng }) => setHover({ ...data, latLng })}
                    onRightClick={(e) => AddToSelected(data)}
                    onClick={(e) => openTutorInfoM(data.id)}
                    onMouseOut={() => setHover(null)}
                  />
                ))}

              {/* Hover Component */}
              {hover && (
                <InfoBox position={hover.latLng} options={{ closeBoxURL: "" }}>
                  <div className="flex w-fit max-w-[250px] flex-col rounded-sm border border-black bg-white p-2 ">
                    <div className="m-0 mb-1 text-xs font-bold text-gray-800">
                      {hover.fullName}
                    </div>
                    <div>{hover.instruments}</div>
                    {new Date(hover.joined) > new Date("2022-01-01") && (
                      <div>{moment(new Date(hover.joined)).fromNow()}</div>
                    )}
                    <div className="mt-1 text-xs font-normal text-blue-600">
                      {hover.students}
                      {hover.students < 2 ? " Student" : " Students"}
                    </div>
                  </div>
                </InfoBox>
              )}

              {/* Circle */}
              {isChecked && <Circle center={student} radius={1000 * radius} />}
            </GoogleMap>
          </div>

          {/* Operation View - Right Part */}
          <div className=" relative flex h-screen w-1/5 flex-col overflow-y-scroll p-3">
            {/* Change Focus Buttons */}
            <div className="mb-3 border-b border-b-gray-300 pb-1 text-sm font-semibold text-gray-600">
              Change Focus
            </div>
            <div className="flex w-full flex-row justify-between space-x-2">
              <div
                onClick={() => setcenter({ lat: 51.5059683, lng: -0.1327297 })}
                className="mb-3 flex h-9 w-1/2 cursor-pointer items-center justify-center rounded-md border bg-white text-sm shadow-sm hover:border-slate-300 hover:shadow-md"
              >
                London
              </div>
              <div
                onClick={() => setcenter({ lat: 40.6976312, lng: -74.1444868 })}
                className="mb-3 flex h-9 w-1/2 cursor-pointer items-center justify-center rounded-md border bg-white text-sm shadow-sm hover:border-slate-300 hover:shadow-md"
              >
                New York
              </div>
            </div>

            {/* Student PostCode Input */}
            <div className="mb-3 border-b border-b-gray-300 pb-1 text-sm font-semibold text-gray-600">
              Student
            </div>
            <Autocomplete
              options={{ componentRestrictions: { country: ["uk", "us"] } }}
              onLoad={(auto) => (autocomplete.current = auto)}
              onPlaceChanged={updateCenter}
            >
              <div className="relative mb-2 flex h-9 w-full  items-center overflow-hidden rounded-sm border border-gray-300 bg-white">
                <div className="absolute left-1 top-1.5">
                  <BiMapPin size={20} />
                </div>
                <input
                  className="h-9 w-full border-0 border-white pl-7 text-sm  outline-none placeholder:text-sm focus:border-transparent"
                  placeholder="Student's Postcode"
                />
                <div className="flex h-full w-8 items-center justify-center bg-black text-white">
                  <BiRightArrow size={17} />
                </div>
              </div>
            </Autocomplete>

            {/* Filter: Instrument Select */}
            {student && (
              <>
                <div className="pt-1.5">
                  <Label>Instrument</Label>

                  <Select
                    onValueChange={(e: any) => setInstrument(e)}
                    defaultValue={Instrument}
                  >
                    <SelectTrigger className="mb-3 w-full rounded-md border border-slate-300 bg-white">
                      <div>{Instrument ? Instrument : "All"}</div>
                    </SelectTrigger>

                    <SelectContent className="hide-scrollbar h-52 overflow-y-scroll py-2 text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value={null as any}>All</SelectItem>
                        <SelectItem value="ABRSM Music Theory Grades">
                          ABRSM Music Theory Grades
                        </SelectItem>
                        <SelectItem value="Bass Guitar">Bass Guitar</SelectItem>
                        <SelectItem value="Cello">Cello</SelectItem>
                        <SelectItem value="Clarinet">Clarinet</SelectItem>
                        <SelectItem value="Double Bass">Double Bass</SelectItem>
                        <SelectItem value="Drums">Drums</SelectItem>
                        <SelectItem value="Flute">Flute</SelectItem>
                        <SelectItem value="French Horn">French Horn</SelectItem>
                        <SelectItem value="Guitar">Guitar</SelectItem>
                        <SelectItem value="Harp">Harp</SelectItem>
                        <SelectItem value="Jazz Piano">Jazz Piano</SelectItem>

                        <SelectItem value="Oboe">Oboe</SelectItem>
                        <SelectItem value="Piano - grades 1 to 5">
                          Piano - grades 1 to 5
                        </SelectItem>
                        <SelectItem value="Piano - grades 5 and above">
                          Piano - grades 5 and above
                        </SelectItem>
                        <SelectItem value="Sax">Sax</SelectItem>
                        <SelectItem value="Singing">Singing</SelectItem>
                        <SelectItem value="Trombone">Trombone</SelectItem>
                        <SelectItem value="Trumpet">Trumpet</SelectItem>
                        <SelectItem value="Viola">Viola</SelectItem>
                        <SelectItem value="Violin">Violin</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>

                <div className="pt-1.5">
                  <Label>Gender</Label>

                  <Select
                    onValueChange={(e: any) => setGender(e)}
                    defaultValue={gender}
                  >
                    <SelectTrigger className="mb-3 w-full rounded-md border border-slate-300 bg-white">
                      <div>{gender ? gender : "All"}</div>
                    </SelectTrigger>

                    <SelectContent className="hide-scrollbar h-auto overflow-y-scroll py-2 text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value={null as any}>All</SelectItem>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>

                <div className="pb-5 pt-1.5">
                  <Label>Active students</Label>

                  <Input
                    type="number"
                    placeholder="Enter active students"
                    // value={students === 0 ? "" : students} // Shows placeholder when value < 1
                    // onChange={(e) => setStudents(Number(e.target.value))} // Convert to number

                    value={students === null ? "" : students}
                    onChange={(e) => {
                      const value = e.target.value;
                      // Accept empty field
                      if (value === "") {
                        setStudents(null);
                      } else {
                        const numberValue = Number(value);
                        // Prevent negative numbers
                        if (numberValue >= 0) {
                          setStudents(numberValue);
                        }
                      }
                    }}
                    className="rounded-md bg-white text-xs font-normal text-slate-600"
                  />
                </div>
              </>
            )}

            {/* Radius Input and Checkbox */}
            {student && (
              <div className="mb-3 flex w-full flex-row items-center justify-between">
                <div className="flex items-center space-x-1.5">
                  <Checkbox
                    checked={isChecked}
                    onCheckedChange={(e: any) => setIsChecked(e)}
                  />
                  <Label>
                    {/* className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" */}
                    Circle (KM)
                  </Label>
                </div>

                <div className="flex h-6 flex-row items-center rounded-sm border border-gray-300">
                  <FaMinusSquare
                    size={20}
                    className=" cursor-pointer"
                    onClick={deRadius}
                  />

                  <input
                    type="text"
                    value={radius}
                    className="h-full  w-10 bg-white text-center text-sm font-semibold outline-none"
                    onChange={handleRadiusInput}
                  />

                  <FaPlusSquare
                    size={20}
                    className=" cursor-pointer"
                    onClick={inRadius}
                  />
                </div>
              </div>
            )}

            {/* Sortlisted Tutor */}
            <div className="mb-2 border-b border-b-gray-300 py-1 text-sm font-semibold text-gray-600">
              Sortlisted Tutor
            </div>
            {selectedList.length != 0 && (
              <div className="flex w-full flex-row justify-between space-x-2">
                <div className=" mb-3 flex h-9 w-1/2 cursor-pointer items-center justify-center rounded-md border border-blue-600 bg-blue-600 text-xs font-semibold text-white hover:bg-white hover:text-blue-600">
                  <SendNotification list={selectedList} />
                </div>
                <div
                  className="mb-3 flex h-9 w-1/2  cursor-pointer items-center justify-center rounded-md border border-blue-600 bg-blue-600 text-xs font-semibold text-white hover:bg-white hover:text-blue-600"
                  onClick={copyEmailToClipboard}
                >
                  Email
                </div>
              </div>
            )}

            <div className="flex h-full w-full flex-col overflow-y-auto">
              {selectedList.map((item) => (
                <div
                  key={item.id}
                  className="relative my-1 h-fit w-full rounded-md border border-gray-300 p-3"
                >
                  <div className="m-0 w-10/12 truncate p-0 text-xs font-semibold text-gray-800 ">
                    {item.fullName}
                  </div>
                  <div className="text-[11px] font-normal text-gray-800 ">
                    {item.postCode}
                  </div>
                  <div className="text-[11px] font-normal text-gray-800 ">
                    {item.email}
                  </div>
                  <div className="text-[11px] font-normal text-gray-800">
                    {item.phoneNumber}
                  </div>
                  <div className="flex flex-row items-center space-x-3">
                    {item.cv && (
                      <Link
                        href={item.cv}
                        prefetch={false}
                        className="hover text-[11px] font-medium text-blue-600 underline"
                        target="_blank"
                      >
                        CV
                      </Link>
                    )}
                    {item.dbs && (
                      <Link
                        href={item.dbs}
                        prefetch={false}
                        className="hover text-[11px] font-medium text-blue-600 underline"
                        target="_blank"
                      >
                        DBS
                      </Link>
                    )}
                  </div>
                  <div className="text-[11px] font-normal text-blue-600">
                    {item.students}
                    {item.students < 2 ? " Student" : " Students"}
                  </div>

                  <div
                    className="absolute right-2 top-2 flex h-5 w-5 cursor-pointer items-center justify-center rounded-sm border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                    onClick={() => RemoveFromSelected(item)}
                  >
                    <RiDeleteBinFill size={12} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tutor Info Modal */}
        <TutorInfo
          id={tutorId}
          isOpen={tutorInfoM}
          setIsOpen={setTutorInfoM}
          stuLoc={student ? `${student.lat},${student.lng}` : ""}
          mutate={mutate}
        />
      </Sidebar>
    );
}

type tSendNProps = {
  list: Array<tMapTutor>;
};

function SendNotification({ list }: tSendNProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  var resolver = yup.object().shape({
    title: yup.string().required("Title is required!"),
    body: yup.string().required("Body is required!"),
  });

  const defaultValues = {
    title: "A Student Near You.",
    body: "We have a new teaching opportunity for you. Please check your Email for details.",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    const tokens: any[] = list.filter((x) => x.token !== null);

    setIsLoading(true);

    if (tokens.length == 0) {
      setIsOpen(false);
      setIsLoading(false);
      return toast.success("Notifications sent!");
    }

    await SendNotifications(
      tokens.map((x) => x.token),
      toast,
      data,
    );
    setIsOpen(false);
    setIsLoading(false);
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div>Notification</div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Send Notification</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem className="py-3">
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="Enter Title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="body"
              render={({ field }) => (
                <FormItem className="py-3">
                  <FormLabel>Body</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter description here" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-gray-600 bg-white text-gray-800 hover:bg-gray-800 hover:text-white "
              >
                Default
              </Button>
              <Button
                type="submit"
                className="border border-blue-600 bg-blue-600 hover:bg-white hover:text-blue-600"
              >
                {isLoading ? <Dots /> : "Send"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

type tTutorInfoProps = {
  id: string;
  stuLoc: string;
  isOpen: boolean;
  setIsOpen: any;
  mutate: () => void;
};

const Ranking = (id: number | undefined) => {
  if (id === 1) {
    return "1 (Very Bad)";
  }
  if (id === 2) {
    return "2 (Bad)";
  }
  if (id === 3) {
    return "3 (Average)";
  }
  if (id === 4) {
    return "4 (Good)";
  }
  if (id === 5) {
    return "5 (Very Good)";
  } else {
    return "None";
  }
};

/**
 * Renders the tutor information dialog.
 * @param {tTutorInfoProps} props - The component props.
 * @returns {JSX.Element} The rendered TutorInfo component.
 */
function TutorInfo({ id, stuLoc, isOpen, setIsOpen, mutate }: tTutorInfoProps) {
  const {
    data: Tutor,
    error,
    isLoading,
    mutate: mutateTutorInfo,
  } = useSWR<tMapTutorInfo>(id ? `/map/tutor/${id}` : null, fetcher);

  const reset = (value: any) => {
    setIsOpen(value);
  };
  const openDirectionMap = () => {
    const destination = stuLoc;
    const origin = `${Tutor?.lat},${Tutor?.lng}`;
    const url = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>
            <div className="-mt-3 flex items-center justify-between">
              <div></div>
              <div className=" ml-4 w-40 truncate text-base font-semibold text-gray-600">
                {Tutor?.fullname}
              </div>
              {Tutor && (
                <EditTutorInfo
                  tutor={Tutor}
                  mutate={mutate}
                  mutateTutorInfo={mutateTutorInfo}
                />
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <>
          {isLoading && !Tutor ? (
            <ListSkeltons height={30} count={15} />
          ) : (
            <div className="mt-3 px-2">
              <div className=" text-sm font-medium text-gray-900">
                Full name
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.fullname ? Tutor?.fullname : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Email
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.email ? Tutor?.email : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Phone number
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.phoneNumber ? Tutor?.phoneNumber : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Postcode
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.postCode ? Tutor?.postCode : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Address
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.address ? Tutor?.address : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                University
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.university ? Tutor?.university : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Instruments
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.instruments ? Tutor?.instruments : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Note
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.note ? Tutor?.note : "--"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Rank
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Ranking(Tutor?.rank)}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">
                Assigned Students
              </div>
              <div className=" mb-2 text-xs font-normal text-gray-500">
                {Tutor?.students ? Tutor?.students : "0"}
              </div>

              <div className=" mt-3 text-sm font-medium text-gray-900">CV</div>
              {Tutor?.cv ? (
                <Link
                  href={Tutor?.cv}
                  target="_blank"
                  className="mb-2 text-[11px] font-light text-blue-600 underline"
                >
                  {Tutor.cv}
                </Link>
              ) : (
                <div className=" mb-2 text-xs font-normal text-gray-500">
                  {" "}
                  Not added yet
                </div>
              )}

              <div className=" mt-3 text-sm font-medium text-gray-900">DBS</div>
              {Tutor?.dbs ? (
                <Link
                  href={Tutor?.dbs}
                  target="_blank"
                  className="mb-2 text-[11px] font-light text-blue-600 underline"
                >
                  {Tutor.dbs}
                </Link>
              ) : (
                <div className=" mb-2 text-xs font-normal text-gray-500">
                  {" "}
                  Not added yet{" "}
                </div>
              )}
            </div>
          )}

          {/* //Button Section... */}
          <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
            <Button
              type="reset"
              onClick={openDirectionMap}
              className="border border-black bg-white text-black hover:bg-black hover:text-white "
            >
              Get Direction ↱
            </Button>
            <Button
              type="reset"
              className="border border-red-600 bg-red-600 hover:bg-white hover:text-red-600"
              onClick={() => setIsOpen(false)}
            >
              Close
            </Button>
          </div>
        </>
      </DialogContent>
    </Dialog>
  );
}

type tEtutorInfoProps = {
  tutor: tMapTutorInfo;
  mutateTutorInfo: () => void;
  mutate: () => void;
};

function EditTutorInfo({ tutor, mutate, mutateTutorInfo }: tEtutorInfoProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    fullname: yup.string().required("Full name is required!"),
    // Address: yup.string().required("Address is required!"),
    postCode: yup
      .string()
      .required("Postcode is required!")
      .test("", "Can not locate your Postcode!", async (value, values) =>
        checkPostCode(value),
      ),
  });

  const defaultValues = {
    fullname: tutor.fullname || "",
    Address: tutor.address || "",
    postCode: tutor.postCode || "",
    phoneNumber: tutor.phoneNumber || "",
    bankName: tutor.bankName || "",
    accNo: tutor.accNo || "",
    sc: tutor.sc || "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    // console.log("Fucking Clicked...");

    const loc: { lat: number; lng: number } = await GeoCoding(data.postCode);

    try {
      const Session = await getSession();
      const token = Session?.token;
      var response = await Api.put(
        `/tutor/${tutor.id}`,
        { ...data, lng: loc.lng, lat: loc.lat },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (response.status === 200) {
        mutate();
        mutateTutorInfo();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Tutor information Successfully Edited!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Faild to Edit tutor information!");
    }
  };

  const reset = (value: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={onOpen}>
        <div className="mr-4 cursor-pointer font-extrabold text-gray-800">
          <FaUserEdit size={16} color="gray" />
        </div>
        {/* <div className="mb-2 flex w-44 cursor-pointer flex-row items-center justify-start rounded-md  border-0 border-input bg-transparent px-4 py-2 shadow-sm hover:bg-accent hover:text-accent-foreground">
        <FaUserEdit size={15} className=" text-slate-600" />

        <span className="ml-1 text-xs font-medium text-slate-600">
          Update Profile
        </span>
      </div> */}
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Tutor Profile</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="fullname"
              render={({ field }) => (
                <FormItem className="pb-1">
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter full name"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="Address"
              render={({ field }) => (
                <FormItem className="py-1">
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter address"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postCode"
              render={({ field }) => (
                <FormItem className="py-1">
                  <FormLabel>Postcode</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter postCode"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem className="py-1">
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter phone number"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bankName"
              render={({ field }) => (
                <FormItem className="py-1">
                  <FormLabel>Bank Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter bank name"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accNo"
              render={({ field }) => (
                <FormItem className="py-1">
                  <FormLabel>Account Number</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter account number"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sc"
              render={({ field }) => (
                <FormItem className="py-1">
                  <FormLabel>Sort Code</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter sort code"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>

              <Button
                type="submit"
                // onClick={form.handleSubmit(onSubmit)}
                className="ml-2 border border-blue-600 bg-blue-600 hover:bg-white hover:text-blue-600"
              >
                {isLoading ? <Dots /> : "Update"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// Sent Notification OLD Modal...

// For Send Notification Handelar...
// const SubmitSendNotification = async (data: any) => {
//   const tokens: any[] = selectedList.filter((x) => x.token !== null);

//   if (tokens.length == 0) {
//     closeNModal();
//     return toast.success("Notifications sent!");
//   }

//   await SendNotifications(
//     tokens.map((x) => x.token),
//     toast,
//     data
//   );
//   closeNModal();
// };

// const CancelSendNotification = () => {
//   resetN({
//     title: "",
//     body: "",
//   });
//   closeNModal();
// };

{
  /* <CustomModal
          title="Send Notification"
          isOpen={notificationModal}
          onClose={closeNModal}
          WidthSize="30%"
        >
          <>
            <div className={inputStyles.inputContainer}>
              <div className={inputStyles.Title}>Title</div>
              <input
                type="text"
                className={inputStyles.Field}
                {...Nregister("title", { required: true })}
              />
            </div>
            <div className={inputStyles.inputContainer}>
              <div className={inputStyles.Title}>Body</div>
              <textarea
                className={inputStyles.commentField}
                rows={8}
                {...Nregister("body", { required: true })}
              />
            </div>
          </>
          <ModalButtons>
            <Button type="Cancel" handleClick={CancelSendNotification}>
              Cancel
            </Button>
            <Button
              type="Submit"
              handleClick={handleNSubmit(SubmitSendNotification)}
            >
              Send
            </Button>
          </ModalButtons>
    </CustomModal> */
}

// Tutor Info Modal....
{
  /* <CustomModal
        title="Tutor info"
        isOpen={tutorInfoModal}
        onClose={closeTutorIModal}
        WidthSize="40%"
      >
        <>
          {TutorIsValidating && !Tutor && (
            <ListSkeltons height={35} count={15} />
          )}
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Full name</div>
            <input
              type="text"
              className={inputStyles.Field}
              {...TIRegister("fullName", { required: true })}
            />

            {TIError.fullName && (
              <div className={inputStyles.errorMessage}>
                Full name is required!
              </div>
            )}
          </div>
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Email</div>
            <input
              type="text"
              className={inputStyles.Field}
              {...TIRegister("email", { required: true })}
            />

            {TIError.email && (
              <div className={inputStyles.errorMessage}>
                Email address is required!
              </div>
            )}
          </div>
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Address</div>
            <input
              type="text"
              className={inputStyles.Field}
              {...TIRegister("address", { required: true })}
            />

            {TIError.address && (
              <div className={inputStyles.errorMessage}>
                Address is required!
              </div>
            )}
          </div>
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Phone number</div>
            <input
              type="text"
              className={inputStyles.Field}
              {...TIRegister("phoneNumber", { required: true })}
            />

            {TIError.phoneNumber && (
              <div className={inputStyles.errorMessage}>
                Phone number is required!
              </div>
            )}
          </div>
          <div className={inputStyles.inputContainer}>
            <div className={inputStyles.Title}>Instrument</div>
            <input
              type="text"
              className={inputStyles.Field}
              placeholder="Ex. Piano"
              {...TIRegister("instrument", { required: true })}
            />
            {TIError.instrument && (
              <div className={inputStyles.errorMessage}>
                Please enter a instrument!
              </div>
            )}
          </div>
        </>
        <ModalButtons>
          <Button type="Cancel" handleClick={handleResetTutorInfo}>
            Reset
          </Button>
          <Button
            type="Submit"
            handleClick={handleTISubmit(handleSubmitTutoInfo)}
          >
            Submit
          </Button>
        </ModalButtons>
    </CustomModal> */
}
