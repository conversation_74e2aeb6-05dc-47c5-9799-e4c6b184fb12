"use client";

import { SessionProvider } from "next-auth/react";
import { Toaster } from "react-hot-toast";
import { Suspense } from "react";

import "./globals.css";

import "tailwindcss/tailwind.css";
import "react-activity/dist/Dots.css";
import "react-activity/dist/Levels.css";
import "react-activity/dist/Sentry.css";
import "react-activity/dist/Spinner.css";
import "react-activity/dist/Squares.css";
import "react-activity/dist/Digital.css";
import "react-activity/dist/Bounce.css";
import "react-activity/dist/Windmill";

export default function RootLayout({
  children,

  ...props
}: {
  children: React.ReactNode;
}) {
  // console.log("layout", { props }); // empty
  return (
    <html>
      <head></head>

      <body>
        <Suspense>
          <SessionProvider>
            {children}
            <Toaster />
          </SessionProvider>
        </Suspense>
      </body>
    </html>
  );
}
