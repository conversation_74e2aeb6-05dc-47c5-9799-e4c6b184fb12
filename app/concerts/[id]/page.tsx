"use client";

import { tConcertD } from "types/concerts";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { fetcher } from "@fetchers/fetcher";
import Sidebar from "@components/Sidebar";
import Header from "@components/Header";
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card";
import { Badge } from "@components/ui/badge";
import dynamic from "next/dynamic";
import { useParams, useRouter } from "next/navigation";
import {
  FiCalendar,
  FiClock,
  FiUsers,
  FiMail,
  FiEye,
  FiEyeOff,
} from "react-icons/fi";
import { MdExpandMore, MdExpandLess } from "react-icons/md";
import Image from "next/image";
import Musecool from "@assets/MuseCool.png";
import USFlag from "@assets/US.png";
import UKFlag from "@assets/Uk.png";
import ConcertDetailsSk from "@components/skeltons/ConcertDetailsSk";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

export default function ConcertDetails() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams<any>();
  const { id } = params;

  const [expandedStudents, setExpandedStudents] = useState<Set<string>>(
    new Set(),
  );
  const [showEmails, setShowEmails] = useState<Set<string>>(new Set());

  const {
    data: Concert,
    error,
    isLoading,
    mutate,
  } = useSWR<tConcertD>(id ? `/concert/${id}` : null, fetcher);

  const toggleStudentExpansion = (studentId: string) => {
    setExpandedStudents((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(studentId)) {
        newSet.delete(studentId);
      } else {
        newSet.add(studentId);
      }
      return newSet;
    });
  };

  const toggleEmailVisibility = (studentId: string) => {
    setShowEmails((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(studentId)) {
        newSet.delete(studentId);
      } else {
        newSet.add(studentId);
      }
      return newSet;
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      time: date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
    };
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = `${Concert?.concertTitle ? Concert.concertTitle : "Concert Details"} | MuseCool Dashboard`;
  }, [Concert]);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  }

  if (isLoading)
    return (
      <Sidebar>
        <Header />
        <div className="px-4 py-5 sm:px-8">
          <ConcertDetailsSk />
        </div>
      </Sidebar>
    );

  if (error || !Concert)
    return (
      <Sidebar>
        <Header />
        <div className="px-4 py-5 sm:px-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500">
                Concert not found or failed to load.
              </div>
            </CardContent>
          </Card>
        </div>
      </Sidebar>
    );

  const { date, time } = Concert.sessionDate
    ? formatDate(Concert.sessionDate)
    : { date: "", time: "" };

  return (
    <Sidebar>
      <Header />

      <div className="hide-scrollbar h-full w-full overflow-y-scroll bg-gradient-to-br from-slate-50 to-cyan-50 px-3.5 py-4 sm:px-4 md:px-6 lg:px-8">
        <div className="mx-auto max-w-6xl space-y-4 md:space-y-6">
          {/* Professional Header with Branding */}
          <div className="relative rounded-xl bg-gradient-to-r from-[#0094ba] to-[#0077a3] text-white shadow-xl sm:rounded-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div
                className="h-full w-full"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='6' cy='6' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                  backgroundRepeat: "repeat",
                }}
              ></div>
            </div>

            {/* Mobile Layout */}
            <div className="block p-3.5 sm:hidden">
              {/* Mobile Header */}
              <div className="relative z-10">
                <div className="mb-3 flex items-start justify-between">
                  <div className="flex flex-1 items-center space-x-2 pr-2">
                    <h1 className="text-xl font-bold leading-tight">
                      {Concert.concertTitle}
                    </h1>
                    {Concert.country && (
                      <Image
                        src={
                          Concert.country === "United Kingdom" ? UKFlag : USFlag
                        }
                        alt={Concert.country}
                        className="h-5 w-5 flex-shrink-0"
                      />
                    )}
                  </div>
                  <div className="flex flex-shrink-0 items-center space-x-2 rounded-full bg-white px-2.5 py-1">
                    <Image src={Musecool} alt="MuseCool" className="h-4 w-4" />
                    <span className="text-xs font-bold text-[#0094ba]">
                      MuseCool
                    </span>
                  </div>
                </div>

                {/* Mobile Info Pills */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 rounded-full bg-white/20 px-3 py-1 backdrop-blur-sm">
                    <FiCalendar size={14} />
                    <span className="text-xs font-semibold">{date}</span>
                  </div>
                  <div className="flex items-center space-x-2 rounded-full bg-white/20 px-3 py-1 backdrop-blur-sm">
                    <FiClock size={14} />
                    <span className="text-xs font-semibold">{time}</span>
                  </div>
                  <div className="flex items-center space-x-2 rounded-full bg-white/20 px-3 py-1 backdrop-blur-sm">
                    <FiUsers size={14} />
                    <span className="text-xs font-semibold">
                      {Concert.totalSubmission} Participants
                    </span>
                  </div>
                </div>

                <p className="mt-3 text-xs leading-relaxed text-cyan-100">
                  {Concert.details}
                </p>
              </div>
            </div>

            {/* Desktop/Tablet Layout */}
            <div className="hidden p-6 sm:block md:p-8">
              {/* MuseCool Logo */}
              <div className="absolute right-4 top-4 md:right-6 md:top-6">
                <div className="flex items-center space-x-3 rounded-full bg-white px-3 py-2 backdrop-blur-sm md:px-4 md:py-2">
                  <Image
                    src={Musecool}
                    alt="MuseCool"
                    className="h-8 w-8 md:h-10 md:w-10"
                  />
                  <span className="text-base font-bold text-cyan-600 md:text-lg">
                    MuseCool
                  </span>
                </div>
              </div>

              <div className="relative z-10 pr-24 md:pr-32">
                <div className="text-center lg:text-left">
                  <div className="mb-4 flex items-center justify-center space-x-3 lg:justify-start">
                    <h1 className="text-2xl font-bold md:text-3xl lg:text-4xl xl:text-5xl">
                      {Concert.concertTitle}
                    </h1>
                    {Concert.country && (
                      <Image
                        src={
                          Concert.country === "United Kingdom" ? UKFlag : USFlag
                        }
                        alt={Concert.country}
                        className="h-6 w-6 flex-shrink-0 md:h-8 md:w-8"
                      />
                    )}
                  </div>

                  <div className="mb-6 flex flex-col items-center justify-center space-y-2 sm:flex-row sm:space-x-4 sm:space-y-0 lg:justify-start">
                    <div className="flex items-center space-x-2 rounded-full bg-white/20 px-3 py-2 backdrop-blur-sm">
                      <FiCalendar size={16} className="md:h-5 md:w-5" />
                      <span className="text-sm font-semibold md:text-base">
                        {date}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 rounded-full bg-white/20 px-3 py-2 backdrop-blur-sm">
                      <FiClock size={16} className="md:h-5 md:w-5" />
                      <span className="text-sm font-semibold md:text-base">
                        {time}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 rounded-full bg-white/20 px-3 py-2 backdrop-blur-sm">
                      <FiUsers size={16} className="md:h-5 md:w-5" />
                      <span className="text-sm font-semibold md:text-base">
                        {Concert.totalSubmission} Participants
                      </span>
                    </div>
                  </div>

                  <p className="text-sm leading-relaxed text-cyan-100 md:text-base lg:text-lg">
                    {Concert.details}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Professional Participant List */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-cyan-50 py-4 sm:py-4 md:py-6">
              <CardTitle className="flex flex-row items-center justify-center space-x-3 text-lg font-bold text-gray-800 sm:text-xl md:text-2xl">
                <FiUsers
                  size={20}
                  className="text-[#0094ba] sm:h-6 sm:w-6 md:h-8 md:w-8"
                />
                <span>Concert Participants</span>
                <Badge className="bg-[#0094ba] px-2 py-1 text-xs font-bold text-white sm:px-3 sm:text-sm md:px-4 md:text-base">
                  {Concert.totalSubmission}
                </Badge>
              </CardTitle>
            </CardHeader>

            <CardContent className="p-4 sm:p-4 md:p-6">
              {Concert.studentList && Concert.studentList.length > 0 ? (
                <div className="space-y-4 sm:space-y-4 md:space-y-6">
                  {Concert.studentList.map((student, index) => (
                    <Card
                      key={student.studentId}
                      className="border-l-4 border-l-[#0094ba] bg-gradient-to-r from-white to-cyan-50/30 shadow-md transition-all duration-300 hover:shadow-lg"
                    >
                      <CardContent className="p-4 sm:p-4 md:p-6">
                        <div className="flex flex-col gap-4 sm:gap-4 lg:flex-row lg:items-center lg:justify-between lg:gap-6">
                          {/* Participant Info */}
                          <div className="flex items-center space-x-4 sm:space-x-4 md:space-x-6">
                            {/* Rank Badge */}
                            <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-[#0094ba] to-[#0077a3] text-base font-bold text-white shadow-md sm:h-10 sm:w-10 sm:text-base md:h-12 md:w-12 md:text-lg">
                              {index + 1}
                            </div>

                            {/* Details */}
                            <div className="min-w-0 flex-1">
                              <h3 className="truncate text-lg font-bold text-gray-800 sm:text-lg md:text-xl lg:text-2xl">
                                {student.name}
                              </h3>
                              <div className="mt-1 flex items-center space-x-2 text-gray-600">
                                <FiMail
                                  size={14}
                                  className="flex-shrink-0 sm:h-4 sm:w-4"
                                />
                                {showEmails.has(student.studentId) ? (
                                  <span className="truncate text-sm sm:text-sm md:text-base">
                                    {student.email}
                                  </span>
                                ) : (
                                  <span className="truncate text-sm text-gray-400 sm:text-sm md:text-base">
                                    ••••••••••••••••
                                  </span>
                                )}
                                <button
                                  onClick={() =>
                                    toggleEmailVisibility(student.studentId)
                                  }
                                  className="ml-2 rounded-full p-1 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
                                  title={
                                    showEmails.has(student.studentId)
                                      ? "Hide email"
                                      : "Show email"
                                  }
                                >
                                  {showEmails.has(student.studentId) ? (
                                    <FiEyeOff size={14} />
                                  ) : (
                                    <FiEye size={14} />
                                  )}
                                </button>
                              </div>
                            </div>
                          </div>

                          {/* Referral Section */}
                          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0 md:space-x-6">
                            <Badge
                              variant={
                                student.totalRefer > 0 ? "default" : "secondary"
                              }
                              className="w-fit px-3 py-1 text-sm font-bold sm:px-3 sm:text-sm md:px-4 md:text-base"
                              style={{
                                backgroundColor:
                                  student.totalRefer > 0
                                    ? "#0094ba"
                                    : undefined,
                              }}
                            >
                              {student.totalRefer} Referrals
                            </Badge>

                            {student.totalRefer > 0 && (
                              <button
                                onClick={() =>
                                  toggleStudentExpansion(student.studentId)
                                }
                                className="flex w-fit items-center space-x-2 rounded-full bg-cyan-100 px-3 py-2 text-[#0094ba] transition-colors hover:bg-cyan-200 sm:space-x-2 sm:px-3 sm:py-2"
                              >
                                <span className="text-sm font-semibold sm:text-sm">
                                  View Referrals
                                </span>
                                {expandedStudents.has(student.studentId) ? (
                                  <MdExpandLess
                                    size={18}
                                    className="sm:h-5 sm:w-5"
                                  />
                                ) : (
                                  <MdExpandMore
                                    size={18}
                                    className="sm:h-5 sm:w-5"
                                  />
                                )}
                              </button>
                            )}
                          </div>
                        </div>

                        {/* Referral List */}
                        {expandedStudents.has(student.studentId) &&
                          student.referList &&
                          student.referList.length > 0 && (
                            <div className="mt-4 rounded-lg bg-gradient-to-r from-green-50 to-cyan-50 p-4 sm:mt-6 sm:rounded-xl sm:p-4 md:p-6">
                              <h4 className="mb-4 flex items-center space-x-3 text-base font-bold text-gray-800 sm:mb-4 sm:space-x-3 sm:text-base md:mb-6 md:text-lg">
                                <div className="rounded-full bg-green-500 p-2 sm:p-2">
                                  <FiUsers
                                    size={14}
                                    className="text-white sm:h-4 sm:w-4 md:h-5 md:w-5"
                                  />
                                </div>
                                <span>
                                  Referred Participants ({student.totalRefer})
                                </span>
                              </h4>

                              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-3 md:gap-4 lg:grid-cols-3">
                                {student.referList.map((referral, refIndex) => (
                                  <div
                                    key={`${student.studentId}-referral-${refIndex}`}
                                    className="flex items-center space-x-3 rounded-lg bg-white p-3 shadow-sm transition-transform hover:scale-105 sm:space-x-3 sm:p-3 md:p-4"
                                  >
                                    <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-500 text-sm font-bold text-white sm:h-8 sm:w-8 sm:text-sm md:h-10 md:w-10 md:text-base">
                                      {refIndex + 1}
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <p className="truncate text-xs text-gray-600 sm:text-sm">
                                        {referral.email}
                                      </p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="py-12 text-center sm:py-12 md:py-16">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 sm:mb-4 sm:h-16 sm:w-16 md:mb-6 md:h-20 md:w-20">
                    <FiUsers
                      size={28}
                      className="text-gray-400 sm:h-8 sm:w-8 md:h-10 md:w-10"
                    />
                  </div>
                  <h3 className="mb-2 text-xl font-bold text-gray-600 sm:mb-2 sm:text-xl md:text-2xl">
                    No Participants Yet
                  </h3>
                  <p className="text-base text-gray-500 sm:text-base md:text-lg">
                    Participants will appear here once they register for the
                    concert
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Professional Footer */}
          <div className="rounded-xl bg-gradient-to-r from-gray-800 to-gray-900 p-4 text-center text-white sm:rounded-2xl sm:p-4 md:p-6">
            <div className="flex flex-col items-center space-y-3 sm:flex-row sm:justify-between sm:space-y-0 md:space-y-0">
              <div className="flex items-center space-x-3 sm:space-x-3 md:space-x-4">
                <Image
                  src={Musecool}
                  alt="MuseCool"
                  className="h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12"
                />
                <div className="text-center sm:text-left">
                  <h3 className="text-sm font-bold sm:text-base md:text-lg">
                    MuseCool
                  </h3>
                  <p className="text-xs text-gray-300 sm:text-sm">
                    Empowering Musical Excellence
                  </p>
                </div>
              </div>
              <div className="text-center sm:text-right">
                <p className="text-sm font-semibold sm:text-base md:text-lg">
                  Concert Dashboard
                </p>
                <p className="text-xs text-gray-300 sm:text-sm">
                  Real-time Participant Tracking
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Sidebar>
  );
}
