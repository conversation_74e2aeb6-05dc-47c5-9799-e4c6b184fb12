"use client";

import Header from "@components/Header";
import PageList from "@components/PageList";
import Sidebar from "@components/Sidebar";

import { fetcher, pgfetcher } from "@fetchers/fetcher";
import { tPgConcertL, tConcertD } from "types/concerts";
import { getSession, useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { useForm } from "react-hook-form";
import * as yup from "yup";

import { yupResolver } from "@hookform/resolvers/yup";
import { Dots } from "react-activity";

import api from "fetchers/BaseUrl";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Button } from "@components/ui/button";
import Concert, { ConcertListHeader } from "@components/lists/concert";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { Input } from "@components/ui/input";
import { Textarea } from "@components/ui/textarea";
import { GenerateQuery } from "core/Query";

import dynamic from "next/dynamic";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

import { useRouter } from "next/navigation";

export default function Concerts() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [concertTitle, setConcertTitle] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);

  // Data Fetching
  const {
    data: Concerts,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgConcertL>(`/concert/list?p=${page}${query}`, pgfetcher);

  const Handler = {
    UpdateConcertTitle: (e: any) => {
      e.preventDefault();
      setConcertTitle(e.target.value);
    },
  };

  const Action = {
    UpdateQuery: (query: string) => {
      // Construct a new query string by combining the current "page" value with the provided "query"
      let queryWithPage = `?p=${page}${query}`;
      // Use the router to update the URL to "/concert/list" with the new query string
      router.push(`/concerts${queryWithPage}`);
      // Update the "query" state with the provided "query"
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      router.push(`/concerts${queryWithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      router.push(`/concerts${queryWithPage}`);
    },

    generateQuery: () => {
      const queryParams = {
        concertTitle: concertTitle.length > 2 ? concertTitle : undefined,
      };
      Action.UpdateQuery(GenerateQuery(queryParams));
    },
  };

  useEffect(() => {
    if (isActive) {
      Action.generateQuery();
    }
  }, [concertTitle]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Page Operation & Navigation
  useEffect(() => {
    document.title = "Concerts | MuseCool Dashboard";

    setIsActive(false);

    // Create a URLSearchParams object from the current URL's search parameters
    const search = new URLSearchParams(window.location.search);
    // Check if the "p" parameter exists in the URL
    if (search.get("p")) {
      // If "p" parameter exists, convert it to a number and set it as the "page" state
      setPage(Number(search.get("p")));
    }
    // Check if concertTitle parameter exists
    if (search.get("concertTitle")) {
      setConcertTitle(search.get("concertTitle") || "");
    }

    search.delete("p");
    if (search.toString() !== "") {
      Action.UpdateQuery("&" + search.toString());
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-4 py-5 sm:px-8">
          <div className="mb-4 flex w-full flex-col items-center justify-between gap-4 sm:flex-row">
            <div className="w-fit text-base font-medium text-gray-700">
              Concert List
            </div>

            <div className="flex w-full flex-col items-center justify-end gap-2 sm:w-auto sm:flex-row">
              <Input
                type="search"
                placeholder="Search by concert title..."
                className="w-full bg-white text-xs sm:w-[200px] md:w-[250px] lg:w-[300px]"
                value={concertTitle}
                onChange={Handler.UpdateConcertTitle}
              />
              <AddConcert mutate={mutate} />
            </div>
          </div>

          <ConcertListHeader />

          {isLoading ? (
            <ListSkeltons height={60} count={20} />
          ) : (
            Concerts?.data.map((concert) => (
              <Concert key={concert.id} concert={concert} mutate={mutate} />
            ))
          )}

          <PageList handler={Action.UpdatePage} page={Concerts?.page} />
        </div>
      </Sidebar>
    );
}

type AddConcertProps = {
  mutate: () => void;
};

function AddConcert({ mutate }: AddConcertProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    concertTitle: yup.string().required("Concert title is required!"),
    country: yup.string().required("Country is required!"),
    sessionDate: yup.string().required("Session date is required!"),
  });

  const defaultValues = {
    concertTitle: "",
    country: "",
    sessionDate: "",
    details: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;

      // Convert date to ISO format
      const formattedData = {
        ...data,
        sessionDate: new Date(data.sessionDate).toISOString(),
      };

      const response = await api.post("/concert", formattedData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        mutate();
        setIsLoading(false);
        toast.success("Concert Added Successfully!");
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to Add Concert! Please Try Again.");
    }
  };

  const reset = (value?: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-4 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add Concert
        </div>
      </DialogTrigger>
      <DialogContent className="hide-scrollbar max-h-full overflow-y-scroll sm:w-full sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Concert</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="concertTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Concert Title</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter concert title"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white text-xs font-normal text-slate-600">
                        {field.value ? field.value : "Select Country"}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-slate-600">
                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="United States">
                        United States
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sessionDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Session Date & Time</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter concert details"
                      {...field}
                      className="min-h-20 text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex w-full flex-row justify-end space-x-2 pt-4">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white"
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
