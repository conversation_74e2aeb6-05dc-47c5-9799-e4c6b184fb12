"use client";

import Header from "components/Header";
import Sidebar from "components/Sidebar";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { fetcher } from "fetchers/fetcher";
import {
  tAssignedTutor,
  tPurchaseSchedule,
  tRentScheduleL,
  tSdChildL,
  tStuChilds,
  tStudentC,
  tStudentD,
} from "types/student";

import { getSession, useSession } from "next-auth/react";
import api from "../../../fetchers/BaseUrl";
import toast from "react-hot-toast";
// import DeleteModal from "components/modals/DeleteModal";
import { useForm } from "react-hook-form";
import { tChildF } from "types/dashboard";
import ListSkeltons from "components/skeltons/ListSkeltons";
import Head from "next/head";
import { MdAddCircle } from "react-icons/md";
import EmptyDetailsList from "@components/Student/EmtyChildList";
import { tGoalList } from "types/goal";
import Goal, { StuGoalListHeader } from "@components/lists/Goal/Student";

import { StudentInfo } from "@components/Student/StudentDetailsList";

import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { checkPostCode } from "core/Checker";
import { Button } from "@components/ui/button";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Calendar } from "components/ui/calendar";

import { Popover, PopoverContent, PopoverTrigger } from "components/ui/popover";
import { SlCalender } from "react-icons/sl";

import { Input } from "@components/ui/input";
import { Dots } from "react-activity";
import Child, { ChildHeader } from "@components/Student/Child";
import RentSchedule, {
  RentScheduleHeader,
} from "@components/Student/RentSchedule";
import PurchaseSchedule, {
  PurchaseScheduleHeader,
} from "@components/Student/PurchaseSchedule";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import AssignedTutor, {
  AssignTutorHeader,
} from "@components/Student/AssignTutor";
import InputSelectorForModal from "@components/modals/InputSelectorForModal";
import { tTutorL } from "types/tutor";
import TutorInfoSK from "@components/skeltons/Tutor/TutorInfoSK";
import BoxSkeleton from "@components/skeltons/BoxSkeleton";
import Comment, { AddComment } from "@components/global/Comment";

import dynamic from "next/dynamic";

import { useParams, useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Details() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const params = useParams<any>();
  const { id } = params;

  // Data Fetching..

  const {
    data: Student,
    error,
    isLoading,
    mutate,
  } = useSWR<tStudentD>(id ? `/student/${id}` : null, fetcher);
  const {
    data: Comments,
    error: errorC,
    mutate: mutateC,
    isLoading: isLoadingC,
  } = useSWR<Array<tStudentC>>(id ? `/student/${id}/comments` : null, fetcher);

  //  Children API Call
  const {
    data: Children,
    error: ChildLError,
    mutate: ChildLMutate,
  } = useSWR<Array<tChildF>>(
    Student?.id ? `/student/${id}/children` : null,
    fetcher,
  );

  const {
    data: Goals,
    error: GoalsError,
    isLoading: GoalIsLoading,
    mutate: GoalsMutate,
  } = useSWR<Array<tGoalList>>(id ? `/student/${id}/goals` : null, fetcher);

  // Set page title ...
  useEffect(() => {
    document.title = `${Student?.fullName ? Student?.fullName : "Student Details"} | MuseCool Admin`;
  }, [Student?.fullName]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        {/* MAIN FUNCTION... */}

        <div className="flex h-full w-full flex-col">
          <div className="my-2 flex h-full w-full flex-row bg-[#f9f9f9] p-4">
            {/* //Left & Middle Section Wrapper */}
            <div className="hide-scrollbar flex h-full w-full flex-row overflow-y-scroll bg-[#f9f9f9]">
              {/* //Left Section */}
              <div className="flex h-full w-64 flex-col bg-[#f9f9f9]">
                {isLoading ? (
                  <TutorInfoSK />
                ) : (
                  <StudentInfo student={Student} mutate={mutate} />
                )}
              </div>

              {/* //Middle Section  */}
              <div className="h-fit w-[calc(100%-320px)]  overflow-hidden bg-[#f9f9f9] pl-4">
                {/* //Children List */}
                <div className="mb-6">
                  <div className="flex flex-row items-center">
                    <div className="text-base font-semibold text-slate-600">
                      Children
                    </div>
                    {Student && (
                      <AddChild
                        student={Student}
                        mutate={mutate}
                        childMutate={ChildLMutate}
                      />
                    )}
                  </div>

                  {isLoading ? (
                    <ListSkeltons height={50} count={4} />
                  ) : (
                    <>
                      {Student?.children && Student?.children.length >= 1 ? (
                        //OLD <ChildListHeader />
                        <ChildHeader />
                      ) : (
                        <EmptyDetailsList text="Empty! No child added!" />
                      )}
                      {Student?.children.map((child: tStuChilds | any) => {
                        return (
                          <Child
                            key={child.id}
                            child={child}
                            mutate={mutate}
                            childMutate={ChildLMutate}
                          />
                        );
                      })}
                    </>
                  )}
                </div>

                {/* Assigned Tutor */}
                <div className="my-6">
                  <div className="flex flex-row items-center">
                    <div className="text-base font-semibold text-slate-600">
                      Assigned Tutors
                    </div>
                    {Children && Student && (
                      <AddAssignedTutor
                        stuId={Student.id}
                        Children={Children}
                        country={Student.country}
                        mutate={mutate}
                        childMutate={ChildLMutate}
                      />
                    )}
                  </div>

                  {isLoading ? (
                    <ListSkeltons height={50} count={4} />
                  ) : (
                    <>
                      {/* Header */}
                      {Student?.assignedTutors &&
                      Student.assignedTutors.length >= 1 ? (
                        <AssignTutorHeader />
                      ) : (
                        <EmptyDetailsList text="No tutor assigned!" />
                      )}

                      {/* List */}

                      {Student?.assignedTutors.map(
                        (assignTutor: tAssignedTutor) => {
                          return (
                            <AssignedTutor
                              key={assignTutor.id}
                              aTutor={assignTutor}
                              mutate={mutate}
                            />
                          );
                        },
                      )}
                    </>
                  )}
                </div>

                {/* //Rent Schedule */}
                <div className="my-6">
                  <div className="flex flex-row items-center ">
                    <div className="text-base font-semibold text-slate-600">
                      Rent Schedules
                    </div>
                    {Children && (
                      <AddRentSchedule
                        child={Children}
                        mutate={mutate}
                        childMutate={ChildLMutate}
                      />
                    )}
                  </div>

                  {isLoading ? (
                    <ListSkeltons height={50} count={4} />
                  ) : (
                    <>
                      {Student?.rentSchedules &&
                      Student.rentSchedules.length >= 1 ? (
                        // OLD <RentScheduleHeader />
                        <RentScheduleHeader />
                      ) : (
                        <EmptyDetailsList text="No Rent Schedule avaiable" />
                      )}

                      {Student?.rentSchedules.map(
                        (rentSchedule: tRentScheduleL | any) => {
                          return (
                            <RentSchedule
                              key={rentSchedule.id}
                              rSchedule={rentSchedule}
                              mutate={mutate}
                            />
                          );
                        },
                      )}
                    </>
                  )}
                </div>

                {/* //Purchase Schedules */}
                <div className="my-6">
                  {/* //Header */}
                  <div className="flex flex-row items-center ">
                    <div className="text-base font-semibold text-slate-600">
                      Purchase Schedule
                    </div>
                    {Children && (
                      <AddPurchaseSchedule
                        child={Children}
                        mutate={mutate}
                        childMutate={ChildLMutate}
                      />
                    )}
                  </div>

                  {isLoading ? (
                    <ListSkeltons height={50} count={4} />
                  ) : (
                    <>
                      {Student?.purchaseSchedules &&
                      Student.purchaseSchedules.length >= 1 ? (
                        <PurchaseScheduleHeader />
                      ) : (
                        <EmptyDetailsList text="No purchase schedule avaiable" />
                      )}

                      {Student?.purchaseSchedules.map(
                        (purchaseSchedule: tPurchaseSchedule) => {
                          return (
                            <PurchaseSchedule
                              key={purchaseSchedule.id}
                              pSchedule={purchaseSchedule}
                              mutate={mutate}
                            />
                          );
                        },
                      )}
                    </>
                  )}
                </div>

                {/* // Goal List .....  */}
                <div className="my-6">
                  <div className="pb-1 text-base font-semibold text-slate-600">
                    Goal List
                  </div>
                  {(isLoading || GoalIsLoading) && (
                    <ListSkeltons height={50} count={4} />
                  )}

                  {Goals && Goals.length >= 1 ? (
                    <StuGoalListHeader />
                  ) : (
                    <EmptyDetailsList text="No goal list avaiable" />
                  )}

                  {Goals?.map((goal: tGoalList) => {
                    return (
                      <Goal key={goal.id} goal={goal} mutate={GoalsMutate} />
                    );
                  })}
                </div>
              </div>
            </div>

            {/* //Right Section Wrapper  */}

            <div className="hide-scrollbar h-auto w-80 overflow-y-scroll px-1">
              <div className="pb-1 text-base font-semibold text-slate-600">
                Comments
              </div>

              {Comments && Student && (!isLoading || !isLoadingC) ? (
                <>
                  <AddComment id={Student.id} mutate={mutateC} />

                  {Comments !== undefined && Comments.length > 0 && (
                    <div className="mt-6 rounded border border-slate-200  bg-white px-3 py-2">
                      {Comments?.map((comment: tStudentC) => {
                        return (
                          <Comment
                            key={comment.id}
                            comment={comment}
                            mutate={mutateC}
                          />
                        );
                      })}
                    </div>
                  )}
                </>
              ) : (
                <BoxSkeleton height={500} width={240} count={1} />
              )}
            </div>
          </div>
        </div>
      </Sidebar>
    );
}

type tAddChildProps = {
  student: tStudentD;
  mutate: () => void;
  childMutate?: () => void;
};

function AddChild({ student, mutate, childMutate }: tAddChildProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [birthDate, setBirthDate] = useState<Date | null>();

  var resolver = yup.object().shape({
    name: yup.string().required("Child name is required!"),

    age: yup
      .string()
      .required("Age is required!")
      .matches(
        /^[a-zA-Z0-9]+$/,
        "Invalid age format. Use only letters and numbers, no special characters.",
      ),

    postCode: yup.string().required("Postcode is required!"),

    status: yup.string().required("Status is required!"),

    gender: yup.string().required("Gender is required!"),
  });

  const defaultValues = {
    name: "",
    age: "",
    postCode: "",
    status: "",
    instrument: "",
    gender: null,
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      setBirthDate(null);
      form.reset(defaultValues);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  // Handler to properly convert the Calendar's selection
  const handleDateSelect = (date: Date | undefined) => {
    setBirthDate(date || null); // Convert undefined to null
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const session = await getSession();
      const token = session?.token;
      const response = await api.post(
        `/student/addchild?id=${student.id}`,
        {
          ...data,
          dob: birthDate ? moment(birthDate).format("YYYY-MM-DD") : null,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();
        if (childMutate) childMutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Child successfully added !");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      if (error.response.data === "cae")
        toast.error("Child already exist! Please check the PostCode!");
      setIsLoading(false);
      toast.error("Faild to add a children!");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={ModalControl.reset}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="ml-1 cursor-pointer text-slate-500">
          <MdAddCircle size={22} />
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Child</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Child Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter name"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="age"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Age</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter age"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postCode"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Postcode</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter postCode"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex w-full flex-row items-center space-x-2.5 py-2">
              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Gender</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          {form.getValues("gender")
                            ? form.getValues("gender")
                            : "Select gender"}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Male">Male</SelectItem>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="w-1/2">
                <FormLabel>Birthdate</FormLabel>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="birthDate"
                      variant={"outline"}
                      className="mt-1 flex w-full items-center justify-start bg-white px-2.5 font-normal"
                    >
                      <SlCalender size={16} className="mr-2" />

                      {birthDate ? (
                        moment(birthDate).format("MMM D, YYYY")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="single"
                      defaultMonth={birthDate ?? new Date()}
                      selected={birthDate ?? undefined} // Convert null to undefined
                      onSelect={handleDateSelect} // Use the handler instead of setBirthDate directly
                      numberOfMonths={1}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Status </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="capitalize">
                          {form.getValues("status")
                            ? form.getValues("status")
                            : "Select status"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value="Regular">Regular</SelectItem>
                        <SelectItem value="New">New</SelectItem>
                        <SelectItem value="Looking for tutor">
                          Looking for tutor
                        </SelectItem>
                        <SelectItem value="Green">Green</SelectItem>
                        <SelectItem value="Pending 1st lesson">
                          Pending 1st lesson
                        </SelectItem>
                        <SelectItem value="Group course only">
                          Group course only
                        </SelectItem>

                        <SelectItem value="Call much later">
                          Call much later
                        </SelectItem>
                        <SelectItem value="Call later">Call later</SelectItem>
                        <SelectItem value="Not active">Not active</SelectItem>
                        <SelectItem value="Old new">Old new</SelectItem>
                        <SelectItem value="Old green">Old green</SelectItem>
                        <SelectItem value="Gave up">Give Up</SelectItem>
                        <SelectItem value="Get to restart">
                          Get to restart
                        </SelectItem>
                        <SelectItem value="Bus event">Bus event</SelectItem>
                        <SelectItem value="Multiple Children">
                          Multiple Children
                        </SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="instrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Sent Instrument</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <div className="capitalize">
                          {form.getValues("instrument") === "Y" ? "Yes" : "No"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-normal text-gray-700">
                      <SelectGroup>
                        <SelectItem value={null as any}>No</SelectItem>
                        <SelectItem value="Y">Yes</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

type tAssignedTutorProps = {
  stuId: string;
  Children: Array<tChildF>;
  country: string;
  mutate: () => void;
  childMutate: () => void;
};

function AddAssignedTutor({
  Children,
  country,
  mutate,
  childMutate,
  stuId,
}: tAssignedTutorProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  const [UAFill, setUAFill] = useState<boolean>(false);
  const [UKFill, setUKFill] = useState<boolean>(false);

  const [USFill, setUSFill] = useState<boolean>(false);

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [tutor, setTutor] = useState<string>("");
  const [seletedTutorId, setSeletedTutorId] = useState<string>("");

  const [tutorFound, setTutorFound] = useState<boolean>(false);

  var resolver = yup.object().shape({
    cid: yup.string().required("Child name is required!"),
    instrument: yup.string().required("Instrument name is required!"),
    type: yup.string().required("Type name is required!"),
  });

  const defaultValues = {
    cid: "",
    type: "",
    instrument: "",
    prices: "",
    commissions: "",
    onlinePrices: "",
    onlineCommissions: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);

      setSeletedTutorId("");
      setTutor("");
      setUAFill(false);
      setUKFill(false);
      setUSFill(false);

      setTutorFound(false);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  const SearchHandelar = {
    Select: (tutor: tTutorL) => {
      setTutor(tutor.fullName);
      setSeletedTutorId(tutor.id);
      setIsFocused(false);
      setTutorFound(false);
    },
    Search: (e: any) => {
      setTutor(e.target.value);
    },
  };

  useEffect(() => {
    if (!tutor) {
      setSeletedTutorId("");
    }
  }, [tutor]);

  // define an array of valid types

  useEffect(() => {
    if (country === "United States") {
      if (USFill) {
        form.setValue("onlinePrices", "42,50,58,72,80,90,100");
        form.setValue("onlineCommissions", "20,20,20,20,20,20,20");

        form.setValue("prices", "50,58,66,80,88,98,108");
        form.setValue("commissions", "26,26,26,28,30,30,30");
      } else if (UAFill) {
        form.setValue("onlinePrices", "17,21,25,29,33,37,41");
        form.setValue("onlineCommissions", "12,15,18,22,25,28,31");
        form.setValue("prices", "");
        form.setValue("commissions", "");
      } else {
        form.setValue("onlinePrices", "");
        form.setValue("onlineCommissions", "");
        form.setValue("prices", "");
        form.setValue("commissions", "");
      }
    } else {
      if (UKFill) {
        form.setValue("onlinePrices", "24,29,34,44,54,59,64");
        form.setValue("onlineCommissions", "14,14,14,14,14,14,14");
        form.setValue("prices", "30,36,42,51,60,68,76");
        form.setValue("commissions", "18,20,20,20,20,20,22");
      } else if (UAFill) {
        form.setValue("onlinePrices", "11,13.5,16,18.5,21,23.5,26");
        form.setValue("onlineCommissions", "8,10.5,13,18.5,21,23.5,25");
        form.setValue("prices", "");
        form.setValue("commissions", "");
      } else {
        form.setValue("onlinePrices", "");
        form.setValue("onlineCommissions", "");
        form.setValue("prices", "");
        form.setValue("commissions", "");
      }
    }
  }, [USFill, UAFill, UKFill]);

  const Action = {
    Cancel: () => {
      form.reset(defaultValues);
      setSeletedTutorId("");
      setTutor("");
      setUAFill(false);
      setUKFill(false);
      setTutorFound(false);

      setIsOpen(false);
    },
    onSubmit: async (data: any) => {
      const onlineCommissions = form.getValues("onlineCommissions");
      const onlinePrices = form.getValues("onlinePrices");
      const prices = form.getValues("prices");
      const commissions = form.getValues("commissions");

      if (!seletedTutorId) {
        setTutorFound(true);
      }
      if (!UAFill && !UKFill && !USFill) {
        toast.error(
          "Price is required! Please ensure the automatic price is selected.",
        );
      } else {
        setIsLoading(true);

        // console.log(data, stuId, seletedTutorId);

        try {
          const Session = await getSession();
          const token = Session?.token;
          const response = await api.post(
            `/student/assign`,
            {
              ...data,
              tid: seletedTutorId,
              sid: stuId,
            },
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            },
          );

          if (response.status === 200) {
            mutate();
            childMutate();
            setIsLoading(false);
            setIsOpen(false);

            toast.success(
              `${tutor} has been successfully assigned as a tutor for ${
                Children &&
                Children.find((c) => c.id === form.getValues("cid"))?.name
              }.`,
            );
          } else {
            toast.error("Unexpected response from the server!");
          }
        } catch (error: any) {
          setIsLoading(false);
          toast.error("Faild to assign a Tutor!");
          console.log(error);
        }
      }
    },
  };

  return (
    <Dialog open={isOpen} onOpenChange={ModalControl.reset}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="ml-1 cursor-pointer text-slate-500">
          <MdAddCircle size={22} />
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Assigned Tutor</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(Action.onSubmit)}>
            <div className="py-2">
              <div
                className={`pb-1 text-sm font-medium  ${
                  tutorFound ? "text-destructive" : "text-gray-800"
                }`}
              >
                Find A Tutor
              </div>
              <Input
                type="search"
                value={tutor}
                onChange={SearchHandelar.Search}
                onFocus={() => setIsFocused(true)}
                className="text-xs text-gray-800"
              />
              {tutor && (
                <InputSelectorForModal
                  isOpen={isFocused}
                  searchValue={tutor}
                  onChange={SearchHandelar.Select}
                  apiRoute="/tutor/search"
                />
              )}

              {tutorFound && (
                <div className="mt-1.5 text-xs font-medium text-destructive">
                  Tutor is required!
                </div>
              )}
            </div>

            <FormField
              control={form.control}
              name="cid"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Select Child</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full  bg-white">
                        <div className="text-xs font-normal text-gray-700">
                          {form.getValues("cid")
                            ? Children &&
                              Children.find(
                                (c) => c.id === form.getValues("cid"),
                              )?.name
                            : "Select Child"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-medium text-gray-700">
                      <SelectGroup>
                        {Children && Children.length === 0 && (
                          <div className="p-1 text-[10px] font-medium text-blue-600">
                            No children found. Please add a child first!
                          </div>
                        )}
                        {Children.map((child: any) => {
                          return (
                            <SelectItem key={child.id} value={child.id}>
                              {child?.name}
                            </SelectItem>
                          );
                        })}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem className="py-1.5">
                  <FormLabel>Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-fullbg-white">
                        {form.getValues("type")
                          ? form.getValues("type")
                          : "Select type"}
                      </SelectTrigger>
                    </FormControl>

                    <SelectContent className="text-xs font-medium text-gray-700">
                      <SelectGroup>
                        <SelectItem value="Online">Online</SelectItem>
                        <SelectItem value="Both">Both</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {country === "United States" ? (
              <div className="flex items-center space-x-2.5 pb-1.5 pt-3.5">
                <div
                  onClick={() => {
                    setUSFill(false), setUAFill(!UAFill);
                  }}
                  className={`${
                    UAFill ? "bg-gray-200" : "bg-white"
                  } flex h-8 w-fit cursor-pointer items-center justify-center rounded border border-gray-500  px-2.5 text-xs font-medium text-gray-700`}
                >
                  Fill With UA-Prices
                </div>

                <div
                  onClick={() => {
                    setUAFill(false);
                    setUSFill(!USFill);
                  }}
                  className={`${
                    USFill ? "bg-gray-200" : "bg-white"
                  } flex h-8 w-fit cursor-pointer items-center justify-center rounded border border-gray-500  px-2.5 text-xs font-medium text-gray-700`}
                >
                  Fill With USA-Prices
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2.5 pb-1.5 pt-3.5">
                <div
                  onClick={() => {
                    setUKFill(false), setUAFill(!UAFill);
                  }}
                  className={`${
                    UAFill ? "bg-gray-200" : "bg-white"
                  } flex h-8 w-fit cursor-pointer items-center justify-center rounded border border-gray-500  px-2.5 text-xs font-medium text-gray-700`}
                >
                  Fill With UA-Prices
                </div>

                <div
                  onClick={() => {
                    setUAFill(false);
                    setUKFill(!UKFill);
                  }}
                  className={`${
                    UKFill ? "bg-gray-200" : "bg-white"
                  } flex h-8 w-fit cursor-pointer items-center justify-center rounded border border-gray-500  px-2.5 text-xs font-medium text-gray-700`}
                >
                  Fill With UK-Prices
                </div>
              </div>
            )}

            {UKFill || USFill ? (
              <>
                <FormField
                  control={form.control}
                  name="onlinePrices"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>
                        {country === "United States"
                          ? "US In-Person "
                          : "UK In-Person "}
                        Online Prices
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter online prices"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="onlineCommissions"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>
                        {country === "United States"
                          ? "US In-Person "
                          : "UK In-Person "}
                        Online Commissions
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter online commissions"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="prices"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>In-person Prices</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter In-person prices"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="commissions"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>In-Person Commissions</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter In-person commissions"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            ) : (
              <>
                <FormField
                  control={form.control}
                  name="onlinePrices"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>
                        {country === "United States"
                          ? "US Online "
                          : "UK Online "}
                        Prices
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter online prices"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="onlineCommissions"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>
                        {country === "United States"
                          ? "US Online "
                          : "UK Online "}
                        Commissions
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter online commissions"
                          {...field}
                          className=" text-xs font-normal text-slate-600"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name="instrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Instrument Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Ex. Piano"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                variant="destructive"
                onClick={Action.Cancel}
                className="border border-destructive bg-white text-destructive hover:bg-destructive hover:text-white "
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

type tAddRentSchProps = {
  child: Array<tChildF>;
  mutate: () => void;
  childMutate: () => void;
};

function AddRentSchedule({ child, mutate, childMutate }: tAddRentSchProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    childId: yup.string().required("Child name is required!"),
    startFrom: yup.date().required("Start date is required!"),
    nextPay: yup.date().required("Next pay date is required!"),
    instrument: yup.string().required("Instrument name is required!"),
    amount: yup
      .number()
      .typeError(
        "Please enter a valid number for the amount. This field is required!",
      )
      .required("Amount is required!"),
  });

  const defaultValues = {
    childId: "",
    startFrom: new Date(),
    nextPay: new Date(),
    instrument: "",
    amount: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    const { startFrom, nextPay } = data;

    setIsLoading(true);
    //   console.log(data);
    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `/instrument/rent-schedule`,
        {
          ...data,
          startFrom: moment(startFrom).format("YYYY-MM-DD"),
          nextPay: moment(nextPay).format("YYYY-MM-DD"),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();
        childMutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Rent schedule added successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to added rent schedule information!");
    }
  };

  const watchStart = form.watch("startFrom");
  const watchNext = form.watch("nextPay");

  useEffect(() => {
    if (
      form.getValues("nextPay") !== new Date() &&
      form.getValues("startFrom")
    ) {
      const newNextPay = new Date(form.getValues("startFrom"));
      newNextPay.setMonth(newNextPay.getMonth() + 1);
      form.setValue("nextPay", newNextPay);
    }
  }, [watchStart || watchNext]);

  return (
    <Dialog open={isOpen} onOpenChange={ModalControl.reset}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="ml-1 cursor-pointer text-slate-500">
          <MdAddCircle size={22} />
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Rent Schedule</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="childId"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Select Child</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full  bg-white">
                        <div className="text-xs font-normal text-gray-700">
                          {form.getValues("childId")
                            ? child &&
                              child.find(
                                (c) => c.id === form.getValues("childId"),
                              )?.name
                            : "Select Child"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-medium text-gray-700">
                      <SelectGroup>
                        {child && child.length === 0 && (
                          <div className="p-1 text-[10px] font-medium text-blue-600">
                            No children found. Please add a child first!
                          </div>
                        )}
                        {child.map((child: any) => {
                          return (
                            <SelectItem key={child.id} value={child.id}>
                              {child?.name}
                            </SelectItem>
                          );
                        })}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="startFrom"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nextPay"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Next Pay</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="instrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Instrument Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Ex. Piano"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function AddPurchaseSchedule({ child, mutate, childMutate }: tAddRentSchProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    childId: yup.string().required("Child name is required!"),
    startFrom: yup.date().required("Start date is required!"),
    nextPay: yup.date().required("Next pay date is required!"),
    instrument: yup.string().required("Instrument name is required!"),
    amount: yup
      .number()
      .typeError(
        "Please enter a valid number for the amount. This field is required!",
      )
      .required("Amount is required!"),
    noOfInstalments: yup
      .number()
      .typeError(
        "Please enter a valid number for the number of installments. This field is required!",
      )
      .required("Number of installments is required!")
      .positive("Number of installments must be a positive number")
      .min(1, "Number of installments cannot be less than 1"),
  });

  const defaultValues = {
    childId: "",
    startFrom: new Date(),
    nextPay: new Date(),
    instrument: "",
    amount: "",
    noOfInstalments: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const ModalControl = {
    reset: (value: any) => {
      setIsOpen(value);
      form.reset(defaultValues);
    },

    onOpen: () => {
      setIsOpen(true);
    },
  };

  const onSubmit = async (data: any) => {
    const { startFrom, nextPay } = data;

    setIsLoading(true);
    //   console.log(data);
    try {
      const Session = await getSession();
      const token = Session?.token;

      const response = await api.post(
        `/instrument/purchase-schedule`,
        {
          ...data,
          startFrom: moment(startFrom).format("YYYY-MM-DD"),
          nextPay: moment(nextPay).format("YYYY-MM-DD"),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      if (response.status === 200) {
        mutate();
        childMutate();
        setIsLoading(false);
        setIsOpen(false);
        toast.success("Purchase schedule added successfully!");
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Failed to added purchase schedule information!");
    }
  };

  const watchStart = form.watch("startFrom");
  const watchNext = form.watch("nextPay");

  useEffect(() => {
    if (
      form.getValues("nextPay") !== new Date() &&
      form.getValues("startFrom")
    ) {
      const newNextPay = new Date(form.getValues("startFrom"));
      newNextPay.setMonth(newNextPay.getMonth() + 1);
      form.setValue("nextPay", newNextPay);
    }
  }, [watchStart || watchNext]);

  return (
    <Dialog open={isOpen} onOpenChange={ModalControl.reset}>
      <DialogTrigger onClick={ModalControl.onOpen}>
        <div className="ml-1 cursor-pointer text-slate-500">
          <MdAddCircle size={22} />
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Purchase Schedule</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="childId"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Select Child</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full  bg-white">
                        <div className="text-xs font-normal text-gray-700">
                          {form.getValues("childId")
                            ? child &&
                              child.find(
                                (c) => c.id === form.getValues("childId"),
                              )?.name
                            : "Select Child"}
                        </div>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="text-xs font-medium text-gray-700">
                      <SelectGroup>
                        {child && child.length === 0 && (
                          <div className="p-1 text-[10px] font-medium text-blue-600">
                            No children found. Please add a child first!
                          </div>
                        )}
                        {child.map((child: any) => {
                          return (
                            <SelectItem key={child.id} value={child.id}>
                              {child?.name}
                            </SelectItem>
                          );
                        })}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="startFrom"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nextPay"
              render={({ field }) => (
                <FormItem className="flex flex-col space-y-2 py-2">
                  <FormLabel>Next Pay</FormLabel>
                  <FormControl>
                    <ReactDatePicker
                      dateFormat="yyyy-MM-dd"
                      selected={field.value}
                      onChange={field.onChange}
                      className="h-9 w-full cursor-pointer rounded-md border border-gray-200 px-3 text-xs font-medium text-gray-600 focus:outline-none"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="instrument"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Instrument Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Ex. Piano"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      {...field}
                      className=" text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="noOfInstalments"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>No Of Instalments</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter no of instalments"
                      {...field}
                      className="text-xs font-normal text-slate-600"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* //Button Section... */}
            <div className=" mt-4 flex w-full flex-row justify-end space-x-2">
              <Button
                type="reset"
                onClick={() => form.reset(defaultValues)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white "
              >
                Reset
              </Button>
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
