"use client";

import React, { useEffect, useState } from "react";
import { tPgStudentL, tStudentL } from "../../types/student";
import Student, { StudentListHeader } from "components/lists/Student";
import Sidebar from "components/Sidebar";
import Header from "components/Header";
import useS<PERSON> from "swr";
import { pgfetcher } from "fetchers/fetcher";
import PageList from "components/PageList";
import ListSkeltons from "components/skeltons/ListSkeltons";
import { useForm } from "react-hook-form";
import { getSession, useSession } from "next-auth/react";

import { dataGrabber } from "core/Excel/export";
import api from "fetchers/BaseUrl";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@components/ui/dialog";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

import { Input } from "@components/ui/input";
import { Textarea } from "@components/ui/textarea";
import { Dots } from "react-activity";
import * as yup from "yup";
import { Button } from "@components/ui/button";
import { yupResolver } from "@hookform/resolvers/yup";
import { checkEmailAddress, checkPostCode } from "core/Checker";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import EmptyList from "@components/EmptyList";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Students() {
  // Routing Hooks
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<Number>(1);
  const [query, setQuery] = useState<string>("");

  const [name, setName] = useState<string>("");

  //Data Fetching
  const {
    data: Students,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgStudentL>(`/student/list?p=${page}${query}`, pgfetcher);

  // For Searching
  const UpdateName = (e: any) => {
    e.preventDefault();
    setName(e.target.value);
  };

  const Action = {
    UpdateQuery: (query: string) => {
      let queryWithPage = `?p=${page}${query}`;
      // router.push("/students", querywithPage, { shallow: true });
      router.push(`/students${queryWithPage}`);
      setQuery(query);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      // router.push("/students", querywithPage, { shallow: true });
      router.push(`/students${queryWithPage}`);
      setPage(newPage);
    },

    // When Reset Apply Then Page Also be reset (Go to page = 1)
    PageReset: (query: string) => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      // router.push("/students", querywithPage, { shallow: true });
      router.push(`/students${queryWithPage}`);
    },
  };

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Students | MuseCool Dashboard";

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />; // Additional safety check
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-4 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-gray-700">
              Student List
            </div>

            <div className="flex flex-row items-center justify-end">
              <div
                onClick={() => dataGrabber(query)}
                className="flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600"
              >
                <p>Export</p>
              </div>

              <AddStudents mutate={mutate} />

              <Input
                type="search"
                placeholder="Name / Email / Phone"
                className="bg-white md:w-[100px] lg:w-[300px]"
                value={name}
                onChange={UpdateName}
              />
            </div>
          </div>

          <StudentListHeader
            updateQuery={Action.UpdateQuery}
            name={name}
            setName={setName}
            updatePage={Action.UpdatePage}
            pageReset={Action.PageReset}
          />

          <div className="w-full">
            {isLoading ? (
              <ListSkeltons height={42} count={30} />
            ) : Students?.data.length === 0 ? (
              <EmptyList
                title="No Student Found!"
                // trigger={() => {
                //   setQuery("");
                //   Action.PageReset("");
                //   mutate();
                // }}
              />
            ) : (
              <>
                {Students?.data.map((student: tStudentL) => (
                  <Student student={student} key={student.id} mutate={mutate} />
                ))}

                <PageList handler={Action.UpdatePage} page={Students?.page} />
              </>
            )}
          </div>
        </div>
      </Sidebar>
    );
}

type AddStuProps = {
  mutate: () => void;
};

function AddStudents({ mutate }: AddStuProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);

  var resolver = yup.object().shape({
    email: yup
      .string()
      .required("Email is required!")
      .email(
        "Invalid Email Address. Please check if there is any extra space added.",
      )
      .test(
        "",
        "This email is already connected to an account. Please Login or reset your password.",
        async (value, values) => checkEmailAddress(value),
      ),
    fullname: yup.string().required("Full name is required!"),
    country: yup.string().required("Country is required!"),
    type: yup.string().required("Company name is required!"),
  });

  // Default Values
  const defaultValues = {
    email: "",
    fullname: "",
    address: "",
    postcode: "",
    instrument: "",
    age: "",
    phone: "",
    message: "",
    status: "Looking for tutor",
    country: "",
    type: "",
  };

  const form = useForm<any>({ defaultValues, resolver: yupResolver(resolver) });

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      const Session = await getSession();
      const token = Session?.token;
      const response = await api.post(`/admin/add-new-student`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        await mutate();
        setIsLoading(false);
        toast.success("Student Added successfully!");
        reset();
        setIsOpen(false);
      } else {
        toast.error("Unexpected response from the server!");
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error("Faild to add a Student!");
    }
  };

  const reset = (value?: any) => {
    setIsOpen(value);
    form.reset(defaultValues);
  };

  const openModal = () => {
    setIsOpen(true);
  };

  return (
    <Dialog onOpenChange={reset} open={isOpen}>
      <DialogTrigger onClick={openModal}>
        <div className="mx-3 flex h-9 cursor-pointer items-center justify-center rounded bg-blue-500 px-3 text-xs font-medium text-blue-50 hover:bg-blue-600">
          Add Student
        </div>
      </DialogTrigger>
      <DialogContent className="sm:scrol hide-scrollbar max-h-full  overflow-y-scroll sm:w-full sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Student</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="fullname"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter full name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="Enter email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postcode"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Postcode</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter postcode"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="Enter address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      placeholder="Enter phone number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-row items-center space-x-2 py-2">
              <FormField
                control={form.control}
                name="instrument"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Instrument</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter instrument"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="age"
                render={({ field }) => (
                  <FormItem className="w-1/2">
                    <FormLabel>Age</FormLabel>
                    <FormControl>
                      <Input type="text" placeholder="Enter age" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex w-full flex-row items-center space-x-2">
              <div className=" w-1/2">
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>Country </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue>
                              {field.value ? field.value : "Select Country"}
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="United Kingdom">
                            United Kingdom
                          </SelectItem>
                          <SelectItem value="United States">
                            United States
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className=" w-1/2">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem className="py-2">
                      <FormLabel>Company </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue>
                              {field.value ? field.value : "Select Company"}
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="UK">UK</SelectItem>
                          <SelectItem value="US">US</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Status </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        {field.value ? field.value : "Select Status"}
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="New">New</SelectItem>
                      <SelectItem value="Looking for tutor">
                        Looking for tutor
                      </SelectItem>
                      <SelectItem value="Pending 1st lesson">
                        Pending 1st lesson
                      </SelectItem>
                      <SelectItem value="Regular">Regular</SelectItem>
                      <SelectItem value="Green">Green</SelectItem>
                      <SelectItem value="Call much later">
                        Call much later
                      </SelectItem>
                      <SelectItem value="Call later">Call later</SelectItem>
                      <SelectItem value="Gave up">Give Up</SelectItem>
                      <SelectItem value="Group course only">
                        Group course only
                      </SelectItem>
                      <SelectItem value="Not active">Not active</SelectItem>
                      <SelectItem value="Old new">Old new</SelectItem>
                      <SelectItem value="Old green">Old green</SelectItem>
                      <SelectItem value="Get to restart">
                        Get to restart
                      </SelectItem>
                      <SelectItem value="Bus event">Bus event</SelectItem>
                      <SelectItem value="Multiple Children">
                        Multiple Children
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="py-2">
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter message" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className=" my-2 flex w-full flex-row justify-end space-x-1">
              <Button
                type="submit"
                className="ml-2 border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
