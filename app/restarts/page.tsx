"use client";

import Header from "@components/Header";

import PageList from "@components/PageList";
import Sidebar from "@components/Sidebar";
import { pgfetcher } from "@fetchers/fetcher";
import { tPgRestartL, tRestartL } from "types/restart";
import { GenerateQuery } from "core/Query";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import Restart, { RestartListHeader } from "@components/lists/Restart";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { Input } from "@components/ui/input";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Restarts() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [isActive, setIsActive] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");

  const [name, setName] = useState<string>("");
  const [tutor, setTutor] = useState<string>("");

  //Data Fetching ...
  const {
    data: Restarts,
    error,
    mutate,
    isLoading,
  } = useSWR<tPgRestartL>(`/student/restart?p=${page}${query}`, pgfetcher);
  // console.log("Restarts ", Restarts);

  const FilterHandelar = {
    UpdateName: (e: any) => {
      e.preventDefault();
      setName(e.target.value);
    },

    UpdateTutor: (e: any) => {
      e.preventDefault();
      setTutor(e.target.value);
    },
  };

  const Action = {
    UpdateQuery: (newQuery: string) => {
      let queryWithPage = `?p=${page}${newQuery}`;
      //router.push("/restart", queryWithPage, { shallow: true });
      router.push(`/restarts${queryWithPage}`);
      setQuery(newQuery);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/restart", queryWithPage, { shallow: true });
      router.push(`/restarts${queryWithPage}`);
      setPage(newPage);
    },

    generateQuery: () => {
      const queryParams = {
        name: name.length > 2 ? name : undefined,
        tutor: tutor.length > 2 ? tutor : undefined,
      };
      Action.UpdateQuery(GenerateQuery(queryParams));
    },
  };

  //For Filtering..
  useEffect(() => {
    if (isActive) {
      Action.generateQuery();
    }
  }, [name, tutor]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  useEffect(() => {
    document.title = "Restarts | MuseCool Dashboard";

    setIsActive(false);

    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }

    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-8 mt-1.5 flex w-full flex-row items-center justify-between">
            <div className="w-fit text-base font-medium text-slate-600 ">
              Restarts
            </div>
            <div className="flex flex-row items-center space-x-2.5">
              <Input
                type="search"
                placeholder="Tutor Name/Email"
                className="border border-slate-300 bg-white placeholder:text-[10px] md:w-36 lg:w-56 lg:placeholder:text-xs xl:w-64 2xl:w-72"
                value={tutor}
                onChange={FilterHandelar.UpdateTutor}
              />

              <Input
                type="search"
                placeholder="Student Name/Email"
                className="border border-slate-300 bg-white placeholder:text-[10px] md:w-36 lg:w-56 lg:placeholder:text-xs xl:w-64 2xl:w-72"
                value={name}
                onChange={FilterHandelar.UpdateName}
              />
            </div>
          </div>

          <RestartListHeader />

          {isLoading ? (
            <ListSkeltons height={50} count={20} />
          ) : (
            Restarts?.data.map((restart: tRestartL, index: number) => (
              <Restart
                // key={restart.studentId}
                key={`${restart.studentId}-${index}`}
                restart={restart}
                mutate={mutate}
              />
            ))
          )}

          <PageList page={Restarts?.page} handler={Action.UpdatePage} />
        </div>
      </Sidebar>
    );
}
