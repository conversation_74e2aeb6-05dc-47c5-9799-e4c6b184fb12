"use client";

import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import Feedback, { FeedbackListHeader } from "@components/lists/Feedback";

import { tFeedbackL, tFeedbackStat, tPgFeedbackL } from "types/feedback";
import { useSession } from "next-auth/react";
import React, { useEffect, useState } from "react";
import useSWR from "swr";
import { fetcher, pgfetcher } from "@fetchers/fetcher";
import PageList from "@components/PageList";
import { ToggleGroup, ToggleGroupItem } from "@components/ui/toggle-group";
import { GenerateQuery } from "core/Query";
import { Card, CardContent, CardHeader, CardTitle } from "components/ui/card";
import { MdRateReview } from "react-icons/md";
import { FaRegCalendarAlt, FaHeadSideVirus } from "react-icons/fa";
import { TbDeviceMobileStar } from "react-icons/tb";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from "components/ui/select";

import CardSk from "@components/skeltons/CardSk";
import ListSkeltons from "@components/skeltons/ListSkeltons";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false, // Disable SSR for this component
});

export default function Feedbacks() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);
  const [rating, setRating] = useState<string>("All");
  const [range, setRange] = useState<string>("7");

  const {
    data: Feedbacks,
    error,
    isLoading,
    mutate,
  } = useSWR<tPgFeedbackL>(`/lesson/feedbacks?p=${page}${query}`, pgfetcher);

  const {
    data: FeedbackStat,
    error: StatError,
    isLoading: StatLoading,
    mutate: StatMutate,
  } = useSWR<tFeedbackStat>(`/lesson/feedback-stat?range=${range}`, fetcher);

  const Handelar = {
    UpdateRading: (e: any) => {
      setRating(e);
    },
    UpdateRange: (e: any) => {
      setRange(e);
    },
  };

  const Action = {
    UpdateQuery: (newQuery: string) => {
      let queryWithPage = `?p=${page}${newQuery}`;
      //   router.push("/feedback", queryWithPage, { shallow: true });
      router.push(`/feedback${queryWithPage}`);
      setQuery(newQuery);
    },

    UpdatePage: (newPage: number) => {
      let queryWithPage = `?p=${newPage}${query}`;
      //   router.push("/feedback", queryWithPage, { shallow: true });
      router.push(`/feedback${queryWithPage}`);
      setPage(newPage);
    },

    PageReset: () => {
      setPage(1);
      let queryWithPage = `?p=${1}${query}`;
      //   router.push("/feedback", queryWithPage, { shallow: true });
      router.push(`/feedback${queryWithPage}`);
    },
  };

  const generateQuery = () => {
    const queryParams = {
      rating: rating !== "All" ? rating : "",
    };
    Action.UpdateQuery(GenerateQuery(queryParams));
  };

  useEffect(() => {
    if (isActive) {
      generateQuery();
    }
  }, [rating]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // PageOperation & Navigation....
  useEffect(() => {
    document.title = "Feedback | MuseCool Dashboard";

    setIsActive(false);
    var search = new URLSearchParams(window.location.search);
    if (search.get("p")) {
      setPage(Number(search.get("p")));
    }
    setIsActive(true);
  }, []);

  if (status === "loading") return <Loading />;
  else if (!session) {
    return <Loading />;
  } else
    return (
      <Sidebar>
        <Header />

        <div className="hide-scrollbar flex h-full w-full flex-col overflow-y-scroll px-8 py-5">
          <div className="mb-10">
            <div className="flex w-full items-center justify-between pb-2.5">
              <div className="w-fit text-base font-semibold text-gray-700">
                Feedback Statistics
              </div>

              <Select onValueChange={Handelar.UpdateRange} defaultValue={range}>
                <SelectTrigger className="w-40 rounded-sm border-0 px-0 shadow-none focus:outline-none focus:ring-0">
                  <div className="flex flex-row items-center text-sm  font-semibold text-gray-700">
                    <FaRegCalendarAlt
                      // color="#737373"
                      size={17}
                      className="mb-0.5 mr-1.5 text-muted-foreground"
                    />

                    {range === "7"
                      ? "Last Week"
                      : range === "15"
                        ? "Last 15 Days"
                        : "Last Month"}
                  </div>
                </SelectTrigger>
                <SelectContent className="text-xs font-normal text-gray-700">
                  <SelectGroup>
                    <SelectItem value="7">Last Week</SelectItem>
                    <SelectItem value="15">Last 15 Days</SelectItem>
                    <SelectItem value="30">Last Month</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {StatLoading ? (
              <CardSk />
            ) : (
              FeedbackStat && <FeedbackStatistics state={FeedbackStat} />
            )}
          </div>

          <div className="flex w-full items-start justify-between pb-5">
            <div className="text-base font-semibold text-gray-700">
              Feedback List
            </div>
            <div>
              <div className="mb-1 text-sm font-semibold text-gray-700">
                Sort By Rating
              </div>
              <ToggleGroup
                type="single"
                defaultValue={rating}
                onValueChange={Handelar.UpdateRading}
                className="h-fit w-fit rounded-md border border-slate-200 bg-white"
              >
                <ToggleGroupItem value="All">
                  <div className="text-xs font-normal text-slate-600">All</div>
                </ToggleGroupItem>
                <ToggleGroupItem value="1">
                  <div className="text-xs font-normal text-slate-600">1</div>
                </ToggleGroupItem>
                <ToggleGroupItem value="2">
                  <div className="text-xs font-normal text-slate-600">2</div>
                </ToggleGroupItem>
                <ToggleGroupItem value="3">
                  <div className="text-xs font-normal text-slate-600">3</div>
                </ToggleGroupItem>
                <ToggleGroupItem value="4">
                  <div className="text-xs font-normal text-slate-600">4</div>
                </ToggleGroupItem>
                <ToggleGroupItem value="5">
                  <div className="text-xs font-normal text-slate-600">5</div>
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
          </div>

          <FeedbackListHeader />

          {isLoading ? (
            <ListSkeltons height={42} count={30} />
          ) : (
            Feedbacks?.data.map((feedback: tFeedbackL) => (
              <Feedback key={feedback.id} feedback={feedback} mutate={mutate} />
            ))
          )}

          <PageList handler={Action.UpdatePage} page={Feedbacks?.page} />
        </div>
      </Sidebar>
    );
}

type tState = {
  state: tFeedbackStat;
};

function FeedbackStatistics({ state }: tState) {
  return (
    <div className="grid gap-2.5 md:grid-cols-2 md:gap-2.5 lg:grid-cols-4">
      <Card className="h-24 py-2.5">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 py-0 pb-1">
          <CardTitle className="text-base font-medium text-slate-600">
            Reviews
          </CardTitle>
          <MdRateReview size={20} className="text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg font-bold text-slate-700">
            {state.totalReviews.toFixed(0)}
          </div>
          <p className="text-[10px] font-light text-slate-600">
            in total out of {state.totalUsage} lessons
          </p>
        </CardContent>
      </Card>

      <Card className="h-24 py-2.5">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 py-0 pb-1">
          <CardTitle className="text-base font-medium text-slate-600">
            Rating
          </CardTitle>
          <TbDeviceMobileStar size={20} className="text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg font-bold text-slate-700">
            {state.average.toFixed(2)}
          </div>
          <p className="text-[10px] font-light text-slate-600">average</p>
        </CardContent>
      </Card>

      <Card className="h-24 py-2.5">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 py-0 pb-1">
          <CardTitle className="text-base font-medium  text-slate-600">
            Lessons
          </CardTitle>
          <FaHeadSideVirus size={20} className=" text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg font-bold text-slate-700">
            {state.lessonPercentage.toFixed(2)}%
          </div>
          <p className="text-[10px] font-light text-slate-600">
            {state.aiLessons} out of {state.totalLessons} lessons
          </p>
        </CardContent>
      </Card>

      <Card className="h-24 py-2.5">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 py-0 pb-1">
          <CardTitle className="text-base font-medium  text-slate-600">
            Tutors
          </CardTitle>
          <FaHeadSideVirus size={20} className=" text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-lg font-bold text-slate-700">
            {state.tutorPercentage.toFixed(2)}%
          </div>
          <p className="text-[10px] font-light text-slate-600">
            {state.aiTutor} out of {state.totalTutor} active tutors
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
