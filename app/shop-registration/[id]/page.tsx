"use client";

import React, { useEffect, useState } from "react";
import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import { tShopD, tReferredUser } from "types/shop";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import Avatar from "react-avatar";
import moment from "moment";
import { <PERSON><PERSON> } from "@components/ui/button";
import { IoArrowBack } from "react-icons/io5";
import Link from "next/link";
import {
  FiShoppingBag,
  FiMail,
  FiUser,
  FiDollarSign,
  FiUsers,
  FiCalendar,
  FiMapPin,
} from "react-icons/fi";
import { Card, CardContent, CardHeader, CardTitle } from "@components/ui/card";
import { Badge } from "@components/ui/badge";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

// Dummy data for shop details since API is not ready
const dummyShopDetails: tShopD = {
  id: "1",
  shopLogo:
    "https://t4.ftcdn.net/jpg/02/41/39/05/360_F_241390593_L3fnDipXel7j38DQKWXLRzpGPuGQ1mYD.jpg",
  shopName: "Music World",
  email: "<EMAIL>",
  country: "uk",
  referralCount: 15,
  referralAmount: 1250.5,
  fullName: "John Smith",
  addedOn: "2024-01-15",
  referredUsers: [
    {
      id: "1",
      name: "Alice Johnson",
      age: 25,
      instrument: "Piano",
      status: "Active",
      referralDate: "2024-01-20",
    },
    {
      id: "2",
      name: "Bob Wilson",
      age: 30,
      instrument: "Guitar",
      status: "Active",
      referralDate: "2024-01-25",
    },
    {
      id: "3",
      name: "Carol Davis",
      age: 22,
      instrument: "Violin",
      status: "Inactive",
      referralDate: "2024-02-01",
    },
    {
      id: "4",
      name: "David Brown",
      age: 28,
      instrument: "Drums",
      status: "Active",
      referralDate: "2024-02-05",
    },
    {
      id: "5",
      name: "Emma Taylor",
      age: 26,
      instrument: "Flute",
      status: "Active",
      referralDate: "2024-02-10",
    },
  ],
};

type Props = {
  params: Promise<{
    id: string;
  }>;
};

export default function ShopDetails({ params }: Props) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [shop, setShop] = useState<tShopD | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Unwrap params using React.use()
  const { id } = React.use(params);

  useEffect(() => {
    // Simulate API call
    const fetchShopDetails = async () => {
      setIsLoading(true);
      try {
        // In real implementation:
        // const response = await api.get(`/shop/${id}`);
        // setShop(response.data);

        // For now, use dummy data
        await new Promise((resolve) => setTimeout(resolve, 500));
        setShop(dummyShopDetails);
      } catch (error) {
        console.error("Failed to fetch shop details:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchShopDetails();
  }, [id]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading" || isLoading) {
    return <Loading />;
  }

  if (status === "unauthenticated") {
    return null;
  }

  if (!shop) {
    return (
      <Sidebar>
        <div className="w-full">
          <Header title="Shop Details" />
          <div className="text-center text-gray-500">Shop not found</div>
        </div>
      </Sidebar>
    );
  }

  return (
    <Sidebar>
      <Header />

      <div className="hide-scrollbar h-full w-full overflow-y-scroll bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/40">
        {/* Professional Header Section */}

        {/* Content Section */}
        <div className="px-4 py-6 sm:px-8">
          <div className="mx-auto max-w-6xl space-y-6">
            {/* Navigation */}
            {/* <div className="flex items-center justify-between">
              <Link href="/shop-registration">
                <button className="inline-flex items-center gap-2 rounded-lg border border-white/50 bg-white/80 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm backdrop-blur-sm transition-all hover:bg-white/95 hover:shadow-md">
                  <IoArrowBack className="h-4 w-4" />
                  Back to Shop List
                </button>
              </Link>
              <div className="rounded-lg bg-white/80 px-3 py-1 text-xs font-medium text-gray-500 backdrop-blur-sm">
                Registered on {moment(shop.addedOn).format("MMM DD, YYYY")}
              </div>
            </div> */}

            {/* Shop Profile Card */}
            <Card className="overflow-hidden border-0 shadow-lg">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-cyan-50 py-6">
                <div className="flex flex-col items-center space-y-4 text-center sm:flex-row sm:space-x-6 sm:space-y-0 sm:text-left">
                  <div className="relative">
                    <Avatar
                      src={shop.shopLogo}
                      name={shop.shopName}
                      size="80"
                      round={true}
                      className="border-4 border-white shadow-lg"
                    />
                  </div>

                  <div className="flex-1 space-y-3">
                    <div>
                      <CardTitle className="text-2xl font-bold text-gray-800 sm:text-3xl">
                        {shop.shopName}
                      </CardTitle>
                      <p className="text-lg font-medium text-gray-600">
                        {shop.fullName}
                      </p>
                    </div>

                    <div className="flex flex-wrap justify-center gap-2 sm:justify-start">
                      <div className="flex items-center space-x-2 rounded-full bg-white/80 px-3 py-1.5 shadow-sm backdrop-blur-sm">
                        <FiMail size={14} className="text-[#0094ba]" />
                        <span className="text-sm font-medium text-gray-700">
                          {shop.email}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 rounded-full bg-white/80 px-3 py-1.5 shadow-sm backdrop-blur-sm">
                        <FiMapPin size={14} className="text-emerald-600" />
                        <span className="text-sm">
                          {shop.country === "uk" ? "🇬🇧" : "🇺🇸"}
                        </span>
                        <span className="text-sm font-medium text-gray-700">
                          {shop.country === "uk"
                            ? "United Kingdom"
                            : "United States"}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 rounded-full bg-white/80 px-3 py-1.5 shadow-sm backdrop-blur-sm">
                        <FiCalendar size={14} className="text-purple-600" />
                        <span className="text-sm font-medium text-gray-700">
                          Since {moment(shop.addedOn).format("MMM YYYY")}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Performance Stats */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="border-0 bg-white/80 shadow-sm backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-[#0094ba]/10 to-[#0077a3]/10">
                      <FiUsers size={24} className="text-[#0094ba]" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-[#0094ba]">
                        {shop.referralCount}
                      </div>
                      <div className="text-sm font-medium text-gray-600">
                        Total Referrals
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-white/80 shadow-sm backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-emerald-500/10 to-teal-500/10">
                      <FiDollarSign size={24} className="text-emerald-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-emerald-600">
                        ${shop.referralAmount.toFixed(0)}
                      </div>
                      <div className="text-sm font-medium text-gray-600">
                        Total Revenue
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-white/80 shadow-sm backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-purple-500/10 to-pink-500/10">
                      <FiDollarSign size={24} className="text-purple-600" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        $
                        {shop.referralCount > 0
                          ? (shop.referralAmount / shop.referralCount).toFixed(
                              0,
                            )
                          : "0"}
                      </div>
                      <div className="text-sm font-medium text-gray-600">
                        Avg per Referral
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Referred Users Section */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="border-b bg-gradient-to-r from-gray-50 to-cyan-50 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-r from-[#0094ba]/10 to-[#0077a3]/10">
                      <FiUsers size={20} className="text-[#0094ba]" />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-bold text-gray-800">
                        Referred Users
                      </CardTitle>
                      <p className="text-sm text-gray-500">
                        {shop.referredUsers.length} users referred by this shop
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-[#0094ba] px-3 py-1 text-sm font-bold text-white">
                    {shop.referredUsers.length}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="p-4">
                {shop.referredUsers && shop.referredUsers.length > 0 ? (
                  <div className="space-y-4">
                    {shop.referredUsers.map(
                      (user: tReferredUser, index: number) => (
                        <Card
                          key={user.id}
                          className="border border-cyan-600 bg-gradient-to-r from-white to-cyan-50/30 shadow-sm transition-all duration-300 hover:shadow-md"
                        >
                          <CardContent className="p-4">
                            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                              {/* User Info */}
                              <div className="flex items-center space-x-4">
                                {/* Rank Badge */}
                                <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-[#0094ba] to-[#0077a3] text-sm font-bold text-white shadow-md">
                                  {index + 1}
                                </div>

                                {/* Details */}
                                <div className="min-w-0 flex-1">
                                  <h3 className="truncate text-lg font-bold text-gray-800">
                                    {user.name}
                                  </h3>
                                  <div className="mt-1 flex items-center space-x-4 text-sm text-gray-600">
                                    <div className="flex items-center space-x-1">
                                      <FiUser
                                        size={12}
                                        className="text-gray-400"
                                      />
                                      <span>Age {user.age}</span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <FiShoppingBag
                                        size={12}
                                        className="text-gray-400"
                                      />
                                      <span>{user.instrument}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Status & Date Section */}
                              <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
                                <Badge
                                  variant={
                                    user.status === "Active"
                                      ? "default"
                                      : "secondary"
                                  }
                                  className={`w-fit px-3 py-1 text-sm font-bold ${
                                    user.status === "Active"
                                      ? "bg-emerald-500 text-white"
                                      : "bg-gray-400 text-white"
                                  }`}
                                >
                                  {user.status}
                                </Badge>

                                <div className="flex items-center space-x-1 text-sm text-gray-600">
                                  <FiCalendar
                                    size={12}
                                    className="text-gray-400"
                                  />
                                  <span>
                                    {moment(user.referralDate).format(
                                      "MMM DD, YYYY",
                                    )}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ),
                    )}
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-[#0094ba]/10 to-[#0077a3]/10">
                      <FiUsers size={32} className="text-[#0094ba]/60" />
                    </div>
                    <h3 className="mb-2 text-lg font-semibold text-gray-600">
                      No Referred Users Yet
                    </h3>
                    <p className="text-sm text-gray-500">
                      Referred users will appear here once this shop starts
                      referring customers
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Sidebar>
  );
}
