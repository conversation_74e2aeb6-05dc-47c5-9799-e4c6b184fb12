"use client";

import React, { useEffect, useState } from "react";
import Header from "@components/Header";
import Sidebar from "@components/Sidebar";
import { tShopL, tPgShopL, tShopRegistration } from "types/shop";
import useSWR from "swr";
import { pgfetcher } from "@fetchers/fetcher";
import PageList from "@components/PageList";
import Shop, { ShopListHeader } from "@components/lists/Shop";
import { useSession } from "next-auth/react";
import ListSkeltons from "@components/skeltons/ListSkeltons";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import api from "fetchers/BaseUrl";
import { GenerateQuery } from "core/Query";
import { Input } from "@components/ui/input";
import { FiShoppingBag, FiPlus, FiSearch } from "react-icons/fi";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import EmptyList from "@components/EmptyList";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@components/ui/dialog";
import { Button } from "@components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import { Dots } from "react-activity";

const Loading = dynamic(() => import("components/lottie/Loading"), {
  ssr: false,
});

// Dummy data for shops since API is not ready
const dummyShops: tShopL[] = [
  {
    id: "1",
    shopLogo:
      "https://t4.ftcdn.net/jpg/02/41/39/05/360_F_241390593_L3fnDipXel7j38DQKWXLRzpGPuGQ1mYD.jpg",
    shopName: "Music World",
    email: "<EMAIL>",
    country: "uk",
    referralCount: 15,
    referralAmount: 1250.5,
    fullName: "John Smith",
    addedOn: "2024-01-15",
  },
  {
    id: "2",
    shopLogo:
      "https://t4.ftcdn.net/jpg/02/41/39/05/360_F_241390593_L3fnDipXel7j38DQKWXLRzpGPuGQ1mYD.jpg",
    shopName: "Harmony Store",
    email: "<EMAIL>",
    country: "usa",
    referralCount: 8,
    referralAmount: 680.25,
    fullName: "Sarah Johnson",
    addedOn: "2024-02-10",
  },
];

const dummyPaginatedShops: tPgShopL = {
  data: dummyShops,
  page: {
    current: 1,
    total: 1,
    hasNext: false,
    hasPrev: false,
  },
};

export default function ShopRegistration() {
  const router = useRouter();
  const { data: session, status } = useSession();

  const [page, setPage] = useState<number>(1);
  const [query, setQuery] = useState<string>("");
  const [name, setName] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);

  // Using dummy data instead of API call
  const [shops, setShops] = useState<tPgShopL>(dummyPaginatedShops);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const Action = {
    UpdatePage: (page: number) => {
      setPage(page);
    },
    UpdateQuery: (query: string) => {
      setQuery(query);
    },
    PageReset: (query: string) => {
      setPage(1);
      setQuery(query);
    },
    generateQuery: () => {
      const queryParams = {
        name: name.length > 2 ? name : undefined,
      };
      Action.UpdateQuery(GenerateQuery(queryParams));
    },
  };

  const Handler = {
    UpdateName: (e: any) => {
      e.preventDefault();
      setName(e.target.value);
    },
  };

  useEffect(() => {
    if (isActive) {
      Action.generateQuery();
    }
  }, [name]);

  // Authentication check & Client-side redirect inside useEffect
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  if (status === "loading") {
    return <Loading />;
  }

  if (status === "unauthenticated") {
    return null;
  }

  return (
    <Sidebar>
      <Header />

      <div className="hide-scrollbar h-full w-full overflow-y-scroll bg-gradient-to-br from-slate-50 via-blue-50/30 to-cyan-50/40">
        {/* Professional Header Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-[#0094ba]/95 to-[#0077a3]/95 px-4 py-8 sm:px-8">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div
              className="h-full w-full"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='9' cy='9' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundRepeat: "repeat",
              }}
            ></div>
          </div>

          <div className="relative z-10">
            <div className="flex flex-col items-center space-y-4 text-center sm:flex-row sm:justify-between sm:space-y-0 sm:text-left">
              <div className="flex items-center space-x-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm">
                  <FiShoppingBag size={24} className="text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white sm:text-3xl">
                    Shop Management
                  </h1>
                  <p className="text-sm text-blue-100 sm:text-base">
                    Manage registered music shops and partnerships
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="rounded-full bg-white/20 px-4 py-2 backdrop-blur-sm">
                  <span className="text-sm font-semibold text-white">
                    {shops?.data?.length || 0} Registered Shops
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="px-4 py-6 sm:px-8">
          {/* Search and Actions Bar */}
          <div className="mb-6 rounded-xl border border-white/50 bg-white/80 p-4 shadow-sm backdrop-blur-sm">
            <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
              <div className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-[#0094ba]/10 to-[#0077a3]/10">
                  <FiSearch size={18} className="text-[#0094ba]" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-800">
                    Shop Directory
                  </h2>
                  <p className="text-sm text-gray-500">
                    Search and manage registered shops
                  </p>
                </div>
              </div>

              <div className="flex w-full flex-col items-center gap-3 sm:w-auto sm:flex-row">
                <div className="relative w-full sm:w-auto">
                  <FiSearch
                    size={16}
                    className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                  />
                  <Input
                    type="search"
                    placeholder="Search shops..."
                    className="w-full border-gray-200 bg-white/90 pl-10 text-sm shadow-sm transition-all focus:border-[#0094ba]/50 focus:ring-[#0094ba]/20 sm:w-[280px]"
                    value={name}
                    onChange={Handler.UpdateName}
                  />
                </div>
                <AddShopDialog
                  isOpen={isDialogOpen}
                  setIsOpen={setIsDialogOpen}
                  onSuccess={() => {
                    toast.success("Shop registered successfully!");
                  }}
                />
              </div>
            </div>
          </div>

          {/* Shop List Container */}
          <div className="">
            <ShopListHeader />

            <div className="">
              {isLoading ? (
                <div className="p-4">
                  <ListSkeltons height={60} count={15} />
                </div>
              ) : shops?.data && shops.data.length > 0 ? (
                shops.data.map((shop: tShopL) => (
                  <Shop key={shop.id} shop={shop} mutate={() => {}} />
                ))
              ) : (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-[#0094ba]/10 to-[#0077a3]/10">
                    <FiShoppingBag size={32} className="text-[#0094ba]/60" />
                  </div>
                  <h3 className="mb-2 text-lg font-semibold text-gray-600">
                    No shops registered yet
                  </h3>
                  <p className="mb-4 text-sm text-gray-500">
                    Start by registering your first music shop
                  </p>
                  <AddShopDialog
                    isOpen={isDialogOpen}
                    setIsOpen={setIsDialogOpen}
                    onSuccess={() => {
                      toast.success("Shop registered successfully!");
                    }}
                  />
                </div>
              )}
            </div>

            {/* Pagination */}
            {shops?.data && shops.data.length > 0 && (
              <div className="border-t border-gray-100/50 bg-white/40 px-4 py-3">
                <PageList
                  handler={Action.UpdatePage}
                  page={shops.page}
                  itemName="Shops"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </Sidebar>
  );
}

// Shop Registration Dialog Component
type AddShopDialogProps = {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  onSuccess: () => void;
};

function AddShopDialog({ isOpen, setIsOpen, onSuccess }: AddShopDialogProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);

  const form = useForm({
    defaultValues: {
      fullName: "",
      email: "",
      shopName: "",
      shopLogo: "",
      password: "",
      country: "",
    },
  });

  const handleFileChange = (e: any) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    try {
      // For now, just simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // In real implementation, you would:
      // const formData = new FormData();
      // formData.append("fullName", data.fullName);
      // formData.append("email", data.email);
      // formData.append("shopName", data.shopName);
      // formData.append("password", data.password);
      // formData.append("country", data.country);
      // if (selectedFile) {
      //   formData.append("shopLogo", selectedFile);
      // }
      // const response = await api.post("/auth/register-shop", formData);

      toast.success("Shop registered successfully!");
      form.reset();
      setSelectedFile(null);
      setPreview(null);
      setIsOpen(false);
      onSuccess();
    } catch (error) {
      toast.error("Failed to register shop");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger onClick={() => setIsOpen(true)}>
        <div className="flex h-10 cursor-pointer items-center justify-center space-x-2 rounded-lg bg-gradient-to-r from-[#0094ba] to-[#0077a3] px-4 text-sm font-semibold text-white shadow-sm transition-all hover:from-[#0077a3] hover:to-[#005f85] hover:shadow-md">
          <FiPlus size={16} />
          <span>Register Shop</span>
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Register New Shop</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter full name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="Enter email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="shopName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Shop Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter shop name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>Shop Logo</FormLabel>
              <input
                type="file"
                onChange={handleFileChange}
                accept="image/*"
                className="block w-full text-sm text-gray-500 file:mr-4 file:h-9 file:cursor-pointer file:rounded file:border-0 file:border-gray-400 file:bg-white file:px-3 file:text-sm file:font-medium file:text-gray-600"
              />
              {preview && (
                <img
                  src={preview}
                  alt="Preview"
                  width="100"
                  height="100"
                  className="mt-4 rounded"
                />
              )}
            </div>

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectGroup>
                        <SelectItem value="uk">United Kingdom</SelectItem>
                        <SelectItem value="usa">United States</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex w-full flex-row justify-end space-x-2 pt-4">
              <Button
                type="reset"
                onClick={() => setIsOpen(false)}
                className="border border-black bg-white text-black hover:bg-black hover:text-white"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="border border-blue-800 bg-blue-800 hover:bg-white hover:text-blue-800"
                disabled={isLoading}
              >
                {isLoading ? <Dots /> : "Submit"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
