export type tCommissions = {
  totalUsEarnings: number;
  totalUkEarnings: number;
  uaTutorEarningsUsd: number;
  uaTutorEarningsGbp: number;
  ukTutorEarningsUsd: number;
  ukTutorEarningsGbp: number;
  usTutorEarningsUsd: number;
  usTutorEarningsGbp: number;
  averageCommissionPerStudentUsd: number;
  averageCommissionPerStudentGbp: number;
  averageCommissionPerLessonUsd: number;
  averageCommissionPerLessonGbp: number;
  earnedFromCoursesUsd: number;
  earnedFromCoursesGbp: number;
  earnedFromExamsUsd: number;
  earnedFromExamsGbp: number;

  // Removed .. OLD
  // totalEarned: number;
  // ukCommission: number;
  // usCommission: number;
  // uaCommission: number;
};

export type tPayments = {
  paidTotal: number;
  paidTotalUs: number;
  noOfPayments: number;
  noOfPaymentsUs: number;
  averagePaidPerStudentThisMonth: number;
  averagePaidPerStudentThisMonthUs: number;
  averagePayment: number;
  averagePaymentUs: number;

  //Removed
  // averagePaidPerStudent: number;
  // averagePaidPerStudentUs: number;
  // tenPaymentsPercentage: number;
  // tenPaymentsPercentageUs: number;
  // fourPaymentsPercentage: number;
  // fourPaymentsPercentageUs: number;
  // singlePaymentsPercentage: number;
  // singlePaymentsPercentageUs: number;
  // totalRemainingAmount: number;
  // totalRemainingAmountUs: number;
};

export type tStudentsStat = {
  negativeBalanceUs: number;
  negativeBalanceUk: number;
  positiveBalanceUs: number;
  positiveBalanceUk: number;
  negativeBalanceCountUs: number;
  negativeBalanceCountUk: number;
  positiveBalanceCountUs: number;
  positiveBalanceCountUk: number;
  zeroBalanceCountUs: number;
  zeroBalanceCountUk: number;
  gaveUpNegativeBalanceUs: number;
  gaveUpNegativeBalanceUk: number;
  gaveUpNegativeBalanceCountUs: number;
  gaveUpNegativeBalanceCountUk: number;
};

export type tLessonsStat = {
  totalPaidAmountUs: number;
  totalPaidAmountUk: number;
  unpaidAmountUs: number;
  unpaidAmountUk: number;
  unpaidToUsTutorUsd: number;
  unpaidToUsTutorGbp: number;
  unpaidToUkTutorUsd: number;
  unpaidToUkTutorGbp: number;
  unpaidToUkrainianTutorUsd: number;
  unpaidToUkrainianTutorGbp: number;
};

export type tTutorStat = {
  activeUs: number;
  activeUk: number;
  activeUa: number;
  avgStudentsUs: number;
  avgStudentsUk: number;
  avgStudentsUa: number;
  rankOneUs: number;
  rankOneUk: number;
  rankOneUa: number;
  avgLessonsUs: number;
  avgLessonsUk: number;
  avgLessonsUa: number;
};

export type tGaveUpsStat = {
  totalGaveUpsUs: number;
  totalGaveUpsUk: number;
  gaveUpsOnlineUs: number;
  gaveUpsOnlineUk: number;
  gaveUpsInPersonUs: number;
  gaveUpsInPersonUk: number;
  onlyFourUs: number;
  onlyFourUk: number;
  onlyOneUs: number;
  onlyOneUk: number;
  averageLessonsUs: number;
  averageLessonsUk: number;
};

export type tContinuity = {
  averageLessonsUs: number;
  averageLessonsUk: number;
  moreThanTenUs: number;
  moreThanTenUk: number;
  moreThanTwentyUs: number;
  moreThanTwentyUk: number;
  moreThanFiftyUs: number;
  moreThanFiftyUk: number;
  moreThanHundredUs: number;
  moreThanHundredUk: number;
  averageCommissionGaveUpUs: number;
  averageCommissionGaveUpUk: number;
};

export type tConversionRates = {
  ukAverageConversionDays: number;
  usAverageConversionDays: number;
  ukStudentsWithLessons: number;
  usStudentsWithLessons: number;
};
