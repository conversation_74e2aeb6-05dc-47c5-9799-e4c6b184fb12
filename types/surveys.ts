export type tSurveyQuestion = {
  question: string;
  answer: string;
};

export type tSurveyReferred = {
  email: string;
};

export type tSurveyL = {
  studentId: string;
  name: string;
  option: string;
  totalReferred: number;
  surveyReferred: Array<tSurveyReferred>;
  surveyQuestions: Array<tSurveyQuestion>;
};

export type tPgSurveyL = {
  data: Array<tSurveyL>;
  page: any;
};

export type tSurveyD = {
  studentId: string;
  name: string;
  option: string;
  totalReferred: number;
  surveyReferred: Array<tSurveyReferred>;
  surveyQuestions: Array<tSurveyQuestion>;
};
