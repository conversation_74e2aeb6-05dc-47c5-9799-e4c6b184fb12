// Shop registration and management types

export type tShopL = {
  id: string;
  shopLogo: string;
  shopName: string;
  email: string;
  country: string;
  referralCount: number;
  referralAmount: number;
  fullName: string;
  addedOn: string;
};

export type tPgShopL = {
  data: Array<tShopL>;
  page: any;
};

export type tShopD = {
  id: string;
  shopLogo: string;
  shopName: string;
  email: string;
  country: string;
  referralCount: number;
  referralAmount: number;
  fullName: string;
  addedOn: string;
  referredUsers: Array<tReferredUser>;
};

export type tReferredUser = {
  id: string;
  name: string;
  age: number;
  instrument: string;
  status: string;
  referralDate: string;
};

export type tShopRegistration = {
  fullName: string;
  email: string;
  shopName: string;
  shopLogo: string;
  password: string;
  country: string;
};

export type tCountryOption = {
  value: string;
  label: string;
};
