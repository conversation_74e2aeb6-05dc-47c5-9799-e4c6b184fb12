export type tUserS = {
  id: string;
  fullname: string;
  fullName: string;
};

export type tTutorL = {
  id: string;
  fullName: string;
  address: string;
  country: string;
  instrument: string;

  type: string | null;
  email: string;
  phoneNumber: string;

  haveGoodCv: boolean;
  haveDbs: boolean;
  isActive: boolean;

  balance: number;
  rank: number;
  cv: string;
  dbs: string | null;
  postCode: string;
  assigned: number;
  joined: Date;
};

export type tPgTutorL = {
  data: Array<tTutorL>;
  page: any;
};

export type tMapTutor = {
  id: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  postCode: string;
  instruments: string;
  token: string;
  rank: number;
  students: number;
  joined: Date;
  lat: number | any;
  lng: number | any;
  cv: string;
  dbs: string;
};

export type tMapTutorInfo = {
  id: string;
  fullname: string;
  email: string;
  phoneNumber: string;
  address: string;
  note: string;
  postCode: string;
  instruments: string;
  rank: number | undefined;
  students: number;
  university: string;
  cv: string;
  dbs: string;
  lat: number;
  lng: number;
  bankName: string;
  accNo: string;
  sc: string;
};

export type tTutorD = {
  id: string;
  fullName: string;
  email: string;
  gender: string;
  phoneNumber: string;
  postCode: string;
  address: string;
  accNo: string;
  sc: string;
  instruments: Array<string>;
  university: string;

  cv: string;
  dbs: string;
  adminCv: string;
  preferred: string;

  canTeachAtHome: boolean;

  country: string;
  workPermit: string;
  dob: Date; // Not added yet in API...

  location: tTutorDLocation;
  joined: Date;
  rank: number;
  token: string;
  category: string;
  students: number;
  stats: tTutorStats;
  assignedStudents: Array<tAssignedStudent>;
};

export type tAssignedStudent = {
  id: string;
  studentName: string;
  studentId: string;
  childName: string;
  commissions: number;
  instrument: string;
  type: string;
  lessons: number;
  started: Date;
  ended: Date;
  isActive: boolean;
  payments: number;
  average: number;
};

export type tTutorDLocation = {
  lat: number;
  lng: number;
};

export type tTutorStats = {
  lastLesson: Date;
  lessons: number;
  cancelled: number;
  success: number;
  earning: number;

  commission: number;
  commissionLastMonth: number;
  commissionLastYear: number;

  average: number;
  currency: string;

  activeStudents: number;
  students: number;
  eightInTime: number;
  moreThanTwenty: number;
};

export type tTutorC = {
  id: string;
  adminName: string;
  dateTime: any;
  body: string;
  isOwner: boolean;
};
