export type tFeedbackL = {
  id: string;
  tutor: tFeedbackTutor;
  student: tFeedbackStu;
  dateTime: string;
  rating: number;
  feedback: string;
};

export type tPgFeedbackL = {
  data: Array<tFeedbackL>;
  page: any;
};

export type tFeedbackStu = {
  id: string;
  fullName: string;
  email: string;
  country: string;
  postCode: string;
  phoneNumber: string;
};

export type tFeedbackTutor = {
  id: string;
  fullName: string;
  email: string;
  gender: string;
};

export type tFeedbackStat = {
  totalUsage: number;

  totalReviews: number;
  average: number;
  lessonPercentage: number;
  tutorPercentage: number;

  totalTutor: number;
  aiTutor: number;
  totalLessons: number;
  aiLessons: number;
};
