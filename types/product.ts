export type tProductL = {
  id: string;
  thumbnail: string;
  title: string;
  type: string;
  category: string;
  country: string;
  start: Date;
  end: Date | null;
  price: string;
  tickets: number;
};

export type tPgProductL = {
  data: Array<tProductL>;
  page: any;
};

export type tProductD = {
  id: string;
  thumbnail: string;
  title: string;
  type: string;
  category: string;
  country: string;
  start: Date;
  end: Date | null;
  details: string;
  tickets: Array<tTicket>;
};

export type tTicket = {
  id: string;
  title: string;
  price: number;
  quantity: number;
  total: number;
};

export type tPgConfirmBookingL = {
  data: Array<tConfirmBookingL>;
  page: any;
};

export type tConfirmBookingL = {
  id: string;
  email: string;
  amount: number;
  intent: string;
  country: string;
  isConfirmed: boolean;
  dateTime: string;
  quantity: number;
  fields: tEventBooker;
  product: string;
  ticket: string;
};

export type tEventBooker = {
  name: string;
  phone: string;
  street: string;
  city: string;
  region: string;
  postCode: string;
  child: string;
  age: string;
  currentlyLearning: string;
  instrumentTheyPlay: string;
  musicExperience: string;
  dwPermission: boolean;
};
