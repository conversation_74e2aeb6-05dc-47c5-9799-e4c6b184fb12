export type tPflL = {
  // General Lesson Info

  id: string;
  student: string;
  studentId: string;
  child: string;
  email: string;
  address: string;
  phoneNumber: string;
  postCode: string;

  admin: any;
  added: string;
  country: string;

  // Finding Tutor Stage

  isTutorFound: boolean;
  proposedDate: any;
  lessonType: any;
  actionTaken: any;
  tutor: string;
  tutors: string;

  // Confirming Date Stage
  isMatched: boolean;
  timeIssue: string;
  timePreference: string;
  confirmedDate: Date | null;

  // Instrument Stage
  needInstrument: boolean;
  isDelivered: boolean;
  instrumentName: string;
  aboutInstrument: string;

  deliveryDate: Date | null;
  acquisitionType: string;
  instrumentPrice: number;
  noOfInstalments: number;

  hasInContractSigned: boolean;

  // Lesson Prepetraction Stage
  havePaid: boolean;
  haveAddress: boolean;
  aboutStudent: string;
  proposedDurations: any;
  pricesForTutor: any;
  isAdult: boolean;
  zoomLink: string;
  meetingId: string;
  passcode: string;

  dob: Date | null;
  gender: string | null;

  // Tutor Info Stage
  category: string;
  tutorType: any;
  hasSigned: boolean;
  isIntroduced: boolean;
  tutorName: string;
  tutorEmail: string;

  isReferral: boolean;
  isClCompleted: boolean;

  //Different Tutor Stage
  isTutorDifferent: boolean;
  haveGoodCv: boolean;
  hasClientInformed: boolean;
  hasClientAccepted: boolean;

  // Final
  isConfirmed: boolean;
  mailedToStudent: boolean;
  mailedToTutor: boolean;
  studentOpinion: string;
  tutorOpinion: string;
  confirmedInApp: boolean;
  scheduledSec: boolean;

  matchedForTutor: boolean;
  matchedForStudent: boolean;
  isClientOk: boolean;
  isTutorOk: boolean;
};

export type tPgPflL = {
  data: Array<tPflL>;
  page: any;
};

export type tClientEmail = {
  id: string;
  fullName: string;
  childName: string;
  instrument: string;
  lessonDate: string;
  lessonTime: string;
  lessonType: string;

  isAdult: boolean;
  zoomLink: string;
  meetingId: string;
  passcode: string;
};

export type tTutorEmail = {
  id: string;
  lessonDate: string;
  lessonTime: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  lessonType: string;
  address: string;
  childName: string;
  childAge: string;
  aboutChild: string;
  lessonDuration: string;

  category: string;

  tutorName: string;
  tutorEmail: string;
  isAdult: boolean;

  instrument: string;
};
