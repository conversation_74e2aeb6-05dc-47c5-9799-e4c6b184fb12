export type tConcertL = {
  id: string;
  concertTitle: string;
  sessionDate: string;
  url: string;
  details: string;
  country: string;
};

export type tPgConcertL = {
  data: Array<tConcertL>;
  page: any;
};

export type tConcertD = {
  id: string;
  concertTitle: string;
  sessionDate: string;
  details: string;
  url: string;
  totalSubmission: number;
  country: string;
  studentList: Array<{
    studentId: string;
    name: string;
    email: string;
    totalRefer: number;
    referList: Array<{
      email: string;
    }>;
  }>;
};
