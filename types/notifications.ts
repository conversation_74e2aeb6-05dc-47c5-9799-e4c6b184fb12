export type tNotificationL = {
  id: string;
  userType: string;
  ukTitle: string;
  ukContent: string;
  ukStartDate: Date | null;
  ukEndDate: Date | null;
  usTitle: string;
  usContent: string;
  usStartDate: Date | null;
  usEndDate: Date | null;
};

export type tPgNotificationL = {
  data: Array<tNotificationL>;
  page: any;
};

export type tNotificationD = {
  id: string;
  userType: string;
  ukTitle: string;
  ukContent: string;
  ukStartDate: null;
  ukEndDate: null;
  usTitle: string;
  usContent: string;
  usStartDate: Date | null;
  usEndDate: Date | null;
  addedBy: string | null;
  addedDate: Date | null;
  updatedBy: string;
  updatedDate: Date | null;
  isActive: boolean;
};
