export type tStudentL = {
  id: string;
  fullname: string;
  email: string;
  status: string;
  postCode: string;
  age: string;
  phone: string;
  instrument: string;
  addedOn: string;
  lastModified: string;
  tutor: string;
  upcoming: string | Date;
  lastLesson: string | Date;
  lessons: number;
  courses: number;
  balance: number;
  gupComment: string | undefined;
  gupReasons: string | undefined;
  isUnsubscribed: boolean;
  isUsingApp: boolean;
  message: string | undefined;
  note: string | undefined;
  children: string;
  type: string;
  country: string;
  currency: string;
  referral: string;

  hasGoal: boolean;

  //new value
  reference: string;

  isChild: boolean;
  enquiryStatus: string;
};

export type tPgStudentL = {
  data: Array<tStudentL>;
  page: any;
};

export type tStudentD = {
  id: string | any;
  fullName: string | any;
  postCode: string | any;
  email: string | any;
  address: string | any;
  phone: string | any;
  status: string | any;
  instrument: string;
  message: string;
  note: string;
  pageTitle: string;
  addedOn: string;
  lastModified: string;
  age: string;
  balance: string;
  userName: string;
  gupComment: string | undefined;
  gupReasons: string | undefined;
  isUnsubscribed: boolean;
  isUsingApp: boolean;
  isFrozen: boolean;
  admin: string;
  type: string;
  country: string;
  referral: string | null;
  gender: string;

  isChild: boolean;

  enquiryStatus: string;

  formSubmitStep: string | null;
  firstLessonAvailabilityDate: Date | null;
  isFlexibleFirstLesson: boolean;

  assignedTutors: Array<tAssignedTutor>;
  children: Array<tSdChildL>;
  rentSchedules: Array<tRentScheduleL>;
  purchaseSchedules: Array<tPurchaseSchedule>;
};

export type tStudentC = {
  id: string;
  adminName: string;
  dateTime: any;
  body: string;
  isOwner: boolean;
};

export type tSdChildL = {
  id: string;
  name: string;
  age: string;
  postCode: string;
  status: string;
  instrument: string;
  commission: number;
  expense: number;
  bill: number;
  balance: number;
  totoalLessons: number;
  paid: number;
};

export type tStuChilds = {
  id: string;
  name: string;
  age: string;
  postCode: string;
  status: string;
  instrument: string;
  lessons: number;
  courses: number;
  exams: number;
  expense: number;

  gender: string;
  dob: Date;
};

export type tAddStu = {
  email: string;
  fullname: string;
  address: string;
  postcode: string;
  instrument: string;
  age: string;
  phone: number;
  message: string;
  status: string;
};

export type tAssignedTutor = {
  id: string;
  tutorId: string;
  tutorName: string;
  childName: string;
  prices: string | null;
  commissions: string | null;
  onlinePrices: string | null;
  onlineCommissions: string | null;
  type: string;
  instrument: string;
};

export type tRentScheduleL = {
  id: string;
  instrument: string;
  child: string;
  startFrom: Date | null;
  nextPay: Date | null;
  endedAt: Date | null;
  amount: number;
  isActive: boolean;
  rents: number;
};
export type tPurchaseSchedule = {
  id: string;
  instrument: string;
  child: string;
  startFrom: Date | null;
  nextPay: Date | null;
  endedAt: Date | null;
  amount: number;
  isActive: boolean;
  noOfInstalments: number;
  instalments: number;
};
