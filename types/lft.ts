export type tLftL = {
  id: string;
  lessonType: string;

  student: tLftStu;
  homeTutor: tHomeTutorL | null;
  onlineTutor: tOnlieTutorL | null;
  admin: tLftAdmin;

  addedOn: Date;

  learnerType: string;
  instrument: string;
  isReady: boolean;
};

export type tPgLftL = {
  data: Array<tLftL>;
  page: any;
};

export type tHomeTutorL = {
  id: string;
  fullName: string;
  email: string;
  gender: string;
};

export type tOnlieTutorL = {
  id: string;
  fullName: string;
  email: string;
  gender: string;
};

export type tLftStu = {
  id: string;
  fullName: string;
  email: string;
  country: string;
  phoneNumber: string;

  postCode: string;
};
export type tLftChild = {
  id: string;
  name: string;
  pronouns: string;
  age: string;
};

export type tLftAdmin = {
  id: string;
  fullName: string;
  email: string;
  position: string;
};

export type tSuggestedL = {
  id: string;
  fullName: string;
  email: string;
  rank: number;
  students: number;
};

export type tLftD = {
  id: string;
  instrument: string;
  lastSpoken: string;
  lessonType: string;
  homeTutor: tHomeTutorD | null;
  onlineTutor: tOnlineTutorD | null;
  learnerType: string;
  learnerName: string;
  // offerInstrumentRent: boolean;
  offerInstrument: boolean;
  launchDate: string;

  student: tLftStu;
  child: tLftChild | null;
  admin: tLftAdmin;
  sendEvents: boolean;
  // sendCourses: boolean;
  isReady: boolean;
  content: string | null;

  addedOn: Date;
};

export type tHomeTutorD = {
  id: string;
  fullname: string;
  email: string;
  country: string;
  gender: string;

  cv: string;
  dbs: string;

  cvPath: string;
  adminCv: string;
  adminCvPath: string;
  preferredCv: string;
  lastCvUpload: string;
};

export type tOnlineTutorD = {
  id: string;
  fullname: string;
  email: string;
  country: string;
  gender: string;

  cv: string;
  dbs: string;

  cvPath: string;
  adminCv: string;
  adminCvPath: string;
  preferredCv: string;
  lastCvUpload: string;
};

export type tLftStatus = {
  problems: Array<string>;
  canBeMarkedAsDone: boolean;
  canUpdateOnlineTutor: boolean;
  canUpdateHomeTutor: boolean;
};
