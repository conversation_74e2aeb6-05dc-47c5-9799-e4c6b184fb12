export type tDashboard = {
  students: number;
  tutors: number;
  lessons: number;
  lessonToday: number;

  leadsNow: number;
  leadsStat: number;

  callsNow: number;
  callsStat: number;

  conversionsNow: number;
  conversionsStat: number;

  fourNow: number;
  fourStat: number;

  nineNow: number;
  nineStat: number;

  gaveUpsNow: number;
  gaveUpsStat: number;
};
export type tLesson = {
  id: string;
  student: string;
  studentId: string;
  postCode: string;
  tutor: string;
  tutorId: string;
  status: string;
  dateTime: string;
  length: number;
  price: number;
  discount: number;
  commission: number;
  child: string;
  childId: string;
  comment: string;
  instrument: string;
  type: string;
  tPayment: boolean;
  isFixed: boolean;
};

export type tPgLesson = {
  data: Array<tLesson>;
  page: any;
};

export type PgObject = {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
};

export type dCoordinate = {
  x: number | any;
  y: number | any;
};

export type tChildF = {
  id: string;
  name: string;
  postCode: string;
};
