export type tEnquiryL = {
  id: string;
  name: string;
  instrument: string | null;
  postCode: string;
  age: string;
  number: string | null;
  email: string;
  country: string;
  referral: string | null;
  message: string | null;
  addedOn: string;
  formSubmitStartDate: string;
  formSubmitStep1CompleteDate: string | null;
  formSubmitStep2CompleteDate: string | null;
  formSubmitStep3CompleteDate: string | null;
  formSubmitStep4CompleteDate: string | null;
  formSubmitLastUpdate: string;
  formSubmitStep: string;
  googleAnalyticsId: string;
  source: string;
  firstLessonAvailabilityDate: string | null;
  isFlexibleFirstLesson: boolean;
};

export type tPgEnquiryL = {
  data: Array<tEnquiryL>;
  page: any;
};
