.main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px 20px;
  overflow-y: scroll;
}

.main .headers {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 15px 0px;
}

.headers .left {
  width: 100%;
  display: flex;
  flex-direction: row;

  /* justify-content: flex-end; */
}

.headers .right {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.input {
  width: 20%;
  margin-right: 10px;
}
.search {
  width: 35%;
}

.field {
  padding: 5px;
  height: 35px;
  width: 100%;
  border: 1px solid #a6a6a6;
  border-radius: 5px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(104, 104, 104);

  outline: none;
}
.field:focus {
  border: 1px solid rgb(80, 80, 80);
}

.container {
  background-color: white;
  height: fit-content;
  display: grid;
  grid-template-columns: 1fr 0.9fr 0.9fr 0.9fr 1.1fr 0.85fr 0.9fr 1fr;
  gap: 5px;
  border: 1px solid #e6e6e6;
  border-radius: 5px;
  align-items: center;
  margin-bottom: 5px;
  padding: 10px 15px;
}

.sContainer {
  background-color: #a5a5a52a;
  height: fit-content;
  display: grid;
  grid-template-columns: 1fr 0.9fr 0.9fr 0.9fr 1.1fr 0.85fr 0.9fr 1fr;
  gap: 5px;
  border: 1px solid #e6e6e6;
  border-left: 1px solid rgb(100, 100, 100);
  border-right: 1px solid rgb(100, 100, 100);
  margin-bottom: 5px;
  padding: 10px 15px;
  align-items: center;
}

.flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.profile {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.profile .top {
  display: flex;
  justify-content: flex-start;
  /* margin-top: 5px; */
}

.top .flex {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.top .text {
  color: #383737ef;
  font-size: 9px;
  font-weight: 500;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
.top .date {
  width: 25%;
  color: #5a5a5a;
  font-size: 8px;
  font-weight: 450;
}

.profile .bottom {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bottom .name {
  color: #313131;
  font-size: 14px;
  font-weight: 600;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  cursor: pointer;
}

.name:hover {
  text-decoration: underline;
}

.bottom .child {
  color: #747272;
  font-size: 11px;
  font-weight: 550;
  cursor: pointer;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.child:hover {
  text-decoration: underline;
}

.bottom .text {
  color: #747272;
  font-size: 11px;
  font-weight: 450;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.container .item {
  height: 100%;
  border-left: 1px solid #cdcdcd;
  padding: 10px 10px;
}

.sContainer .item {
  height: 100%;
  border-left: 1px solid #cdcdcd;
  padding: 10px 10px;
}
.header {
  font-size: 12px;
  font-weight: 550;
  color: #5a5a5a;
  padding-bottom: 10px;
  text-align: center;
  cursor: pointer;
}
.header:hover {
  text-decoration: underline;
}

.redHeader {
  font-size: 12px;
  font-weight: 550;
  color: #d90416;
  padding-bottom: 10px;
  text-align: center;
  cursor: pointer;
}

.redHeader:hover {
  text-decoration: underline;
}

.flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 1px 0px;
}

.text {
  color: #5a5a5a;
  font-size: 10px;
  font-weight: 450;
  padding: 2px 0px;
  padding-left: 5px;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.zoomLink {
  color: #5a5a5a;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 0px;
  padding-left: 5px;

  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 0px;
}

.center .text {
  color: #5a5a5a;
  font-size: 11px;
  font-weight: 450;
  text-align: center;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.checkbox {
  display: flex;
  align-items: center;
  margin: 5px 0;
  font-weight: 550;
  font-size: 13px;
  margin-bottom: 2px;
  margin-left: -3px;
}

.checkbox .field {
  height: 15px;
  width: 15px;
  accent-color: black;
  margin-right: 5;
}

.inputContainer {
  margin-bottom: 10px;
  width: 100%;
  position: relative;
}

.inputContainer .Field {
  padding: 5px;
  height: 38px;
  width: 100%;
  border: 1px solid rgb(204, 204, 204);
  border-radius: 4px;
  font-weight: 550;
  font-size: 12px;
  color: rgb(122, 120, 120);
  outline: none;
}

.inputContainer .Field:focus {
  border: 1px solid rgb(80, 80, 80);
}
