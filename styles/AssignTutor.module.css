.Header {
  display: grid;
  grid-template-columns: 0.9fr 0.5fr 0.5fr 0.9fr 0.9fr 0.3fr;
  padding: 5px 10px;
}

.Header .tTitle {
  font-size: 12px;
  font-weight: 450;
  color: #848484;
}
.Header .mTitle {
  font-size: 10px;
  font-weight: 400;
  color: #848484;
}
.Header .cTitle {
  font-size: 12px;
  font-weight: 450;
  text-align: center;
  color: #848484;
}
.infoCN {
  height: fit-content;

  display: grid;
  grid-template-columns: 0.9fr 0.5fr 0.5fr 0.9fr 0.9fr 0.3fr;
  padding: 10px 10px;
  align-items: center;
  background-color: white;
  margin-bottom: 5px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  /* cursor: pointer; */
}

/* .infoCN:hover {
  border: 1px solid #84848460;
  padding: 10px 10px;
  background-color: #84848420;
} */

.rFlex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.Name {
  font-size: 12px;
  font-weight: 600;
  color: #494949;
  cursor: pointer;
  overflow: hidden;

  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.Name:hover {
  text-decoration: underline;
}

.Item {
  color: #494949;
  font-size: 12px;
  font-weight: 450;

  /* overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical; */
}

.middleItem {
  color: #494949;
  font-size: 10px;
  font-weight: 400;

  /* overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical; */
}

.itemDes {
  font-size: 9px;
  font-weight: 300;
  color: #494949;

  /* overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical; */
}

.cItem {
  font-size: 13px;
  font-weight: normal;
  text-align: center;
  color: #494949;
  cursor: pointer;

  /* overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical; */
}

.itemIcon {
  cursor: pointer;
  padding-top: 2px;

  display: flex;
  align-items: center;
  justify-content: center;
}
