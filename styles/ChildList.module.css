.Header {
  display: grid;
  grid-template-columns: 1fr 0.4fr 0.6fr 0.9fr 0.3fr 1fr 0.3fr;
  padding: 5px 10px;
}

.Header .tTitle {
  font-size: 12px;
  font-weight: 450;
  color: #848484;
}
.Header .mTitle {
  font-size: 10px;
  font-weight: 400;
  color: #848484;
}
.Header .bTitle {
  font-size: 9px;
  font-weight: 380;
  color: #848484;
}

.Header .cTitle {
  font-size: 12px;
  font-weight: 450;
  text-align: center;
  color: #848484;
}

.infoCN {
  height: fit-content;
  display: grid;
  grid-template-columns: 1fr 0.4fr 0.6fr 1fr 0.3fr 1fr 0.3fr;
  padding: 5px 10px;
  gap: 5px;
  align-items: center;
  background-color: white;
  margin-bottom: 5px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  /* cursor: pointer; */
}

/* .infoCN:hover {
  border: 1px solid #84848460;
  padding: 5px 10px;
  background-color: #84848420;
} */

/* .SInfoCN {
  height: fit-content;
  display: grid;
  grid-template-columns: 1fr 0.4fr 0.6fr 1fr 0.3fr 1fr 0.3fr;
padding: 5px 10px;
  gap: 5px;
  align-items: center;
  background-color: #a5a5a52a;
  border: 1px solid #e6e6e6;
  border-left: 1px solid rgb(100, 100, 100);
  border-right: 1px solid rgb(100, 100, 100);
  margin-bottom: 5px;
  cursor: pointer;
} */

.emptyList {
  height: 50px;
  padding: 10px;
  margin: 15px 0px;

  background-color: white;
  margin-bottom: 5px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;

  display: flex;
  justify-content: center;
  align-items: center;
}
.emptyList .Text {
  font-size: 12px;
  font-weight: normal;
  color: #6b6b6b;
}

.Name {
  font-size: 12px;
  font-weight: 600;
  color: #494949;
  overflow: hidden;

  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.PostCode {
  width: fit-content;
  padding: 3px 5px;
  border-radius: 5px;
  background-color: #f5f5f5;
  color: #6b6b6b;
  font-size: 9px;
  font-weight: normal;
  margin-top: 2px;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.Item {
  color: #494949;
  font-size: 12px;
  font-weight: 450;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.middleItem {
  color: #494949;
  font-size: 10px;
  font-weight: 400;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.itemDes {
  font-size: 9px;
  font-weight: 300;
  color: #494949;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.cItem {
  font-size: 12px;
  font-weight: normal;
  text-align: center;
  color: #494949;
  cursor: pointer;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.itemIcon {
  text-align: center;
  cursor: pointer;
  padding-top: 2px;
}
.itemIconWithPadding {
  text-align: center;
  cursor: pointer;
  padding-top: 2px;
  padding-left: 5px;
}
.wrapper {
  position: relative;
}

.rFlex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
