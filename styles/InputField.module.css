.Main {
  height: "100%";
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flexRow {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.Title {
  padding: 0px;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
  color: #111111b2;
}
.inputContainer {
  position: relative;
  width: 100%;
  margin: 10px 0px;
  margin-right: 10px;
}

.inputContainer .searchingIcon {
  position: absolute;
  right: 8px;
  top: 30px;
  font-size: 15px;
}

.inputContainer .Field {
  padding: 5px;
  height: 35px;
  width: 100%;
  border: 1px solid rgb(155, 155, 155);
  border-radius: 2px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(104, 104, 104);
  outline: none;
}
.Field:focus {
  border: 1px solid rgb(80, 80, 80);
}

.flexInputfield {
  position: relative;
  width: 100%;
  margin: 10px 0px;
  margin-right: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flexInputfield .Box {
  height: 16px;
  width: 16px;
  accent-color: black;
  margin-right: 5px;
  margin-left: -0.5px;
}

.flexInputfield .Text {
  font-weight: 500;
  font-size: 14px;
  color: rgb(83, 83, 83);
}

.SelectField {
  height: 25;
  width: auto;
  padding: 5px 5px;
  margin-left: 5px;
  border: 1px solid rgb(155, 155, 155);
  border-radius: 2px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(83, 83, 83);
  outline: none;
}

.SelectField:focus {
  border: 1px solid rgb(80, 80, 80);
}

.inputContainer .DatePickerContainer {
  height: 250px;
}

.inputContainer .commentField {
  padding: 5px;
  width: 100%;
  border: 1px solid rgb(155, 155, 155);
  border-radius: 2px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(83, 83, 83);
  outline: none;
}

.commentField:focus {
  border: 1px solid rgb(80, 80, 80);
}

.errorMessage {
  position: absolute;
  opacity: 0.8;
  font-size: 9px;
  font-weight: 500;
  color: red;
  margin-top: 0px;
  /* display: flex;
  flex-direction: row;
  justify-content: flex-end;
  text-align: justify; */
  right: 0;
}
/* .errorMessage {
  position: absolute;
  opacity: 0.8;
  font-size: 10px;
  font-weight: 500;
  color: red;
  bottom: -16px;
  right: 0px;
} */
.Buttons {
  width: 100%;
  margin: 15px 0px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.Buttons .Button {
  height: 35px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid rgb(155, 155, 155);
  border-radius: 2px;
  cursor: pointer;
  padding: 5px 10px;
  margin-right: 10px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(83, 83, 83);
}
.Buttons .SButton {
  height: 35px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: rgb(204, 202, 202);
  border: 1px solid rgb(146, 146, 146);
  border-radius: 2px;
  cursor: pointer;
  padding: 5px 10px;
  margin-right: 10px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(31, 31, 31);
}
.Button:hover {
  background-color: rgb(204, 202, 202);
  color: rgb(31, 31, 31);
  border: 1px solid rgb(146, 146, 146);
}

.inputContainer .selectorContainer {
  position: absolute;
  width: 100%;
  max-height: 200px;
  background-color: white;
  z-index: 2;
  border: 1px solid #919191;
  overflow: hidden;
  overflow-y: scroll;
}

.selectorContainer .Title {
  padding: 5px;
  font-size: 12px;
  cursor: pointer;
}
.selectorContainer .Title:hover {
  background-color: #d3d3d3;
  border-left: 1px solid black;
  border-right: 1px solid black;
}

.CustomSelectField {
  width: 100%;
  border-radius: 2px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(83, 83, 83);
  outline: none;
}

.CustomCheckBox {
  padding-top: 5px;
}

.CustomCheckBox .Label {
  font-weight: 500;
  font-size: 12px;
  color: rgb(83, 83, 83);
  margin-right: 10px;

  display: flex;
  align-items: center;
}

.CustomCheckBox .Title {
  padding: 0px;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
  color: #111111b2;
  margin-top: 15px;
}

.LeftErrorMessage {
  opacity: 0.8;
  font-size: 10px;
  font-weight: 500;
  color: red;
  margin-top: 5px;
  padding-left: 2px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  text-align: justify;
}
