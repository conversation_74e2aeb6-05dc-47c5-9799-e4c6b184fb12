.Modal,
.Overlay {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  /* background-color: rgb(0, 0, 0); */
  background-color: rgba(0, 0, 0, 0.4);
}
.hidden {
  display: none;
}

.Content {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: auto;
  background-color: #fefefe;
  padding: 0px 20px;
  border: 1px solid #888;
  min-width: 25%;
  max-width: 70%;
  max-height: 90%;
  /* margin: 5px 0px; */
}

/*  For Hide ScrollBar... */
.Content::-webkit-scrollbar {
  display: none;
  /* for Chrome, Safari, and Opera */
}

.Close {
  position: absolute;
  top: 12px;
  right: 15px;
  cursor: pointer;
  color: rgb(65, 65, 65);
}
.EditIcon {
  position: absolute;
  top: 12px;
  right: 50px;
  cursor: pointer;
  color: rgb(65, 65, 65);
}

.closeHide {
  display: none;
}

.Modal .header {
  width: 100%;
  padding: 10px 0px;
  border-bottom: 1px solid rgb(211, 211, 211);
  margin-bottom: 5px;
  font-size: 16px;
  font-weight: bold;
  color: #272727b2;
  min-height: 40px;
}

.Modal .centerHeader {
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  color: #272727b2;
  border-bottom: 1px solid rgb(211, 211, 211);
  padding: 10px 0px;
  margin-bottom: 0px;
  text-align: center;
  min-height: 40px;
}

.items {
  position: relative;
  width: 100%;
  height: auto;
}

.footer {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 10px 0px;
  border-top: 1px solid rgb(221, 221, 221);
  margin-top: 25px;
}

.rButton {
  background-color: white;
  border: 1px solid rgb(175, 175, 175);
  padding: 10px 15px;
  color: rgb(56, 56, 56);
  font-size: 13px;
  font-weight: 550;
  border-radius: 2px;
  cursor: pointer;
  margin-right: 5px;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.rButton:hover {
  background-color: rgb(56, 56, 56);
  color: white;
  border: 1px solid black;
}
.cButton {
  background-color: white;
  border: 1px solid red;
  padding: 10px 15px;
  color: red;
  font-size: 13px;
  font-weight: 550;
  border-radius: 2px;
  cursor: pointer;
  margin-right: 5px;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.cButton:hover {
  background-color: red;
  border: 1px solid white;
  color: white;
}

.sButton {
  background-color: black;
  border: 1px solid black;
  cursor: pointer;
  padding: 10px 15px;
  border-radius: 2px;
  color: white;
  font-size: 13px;
  font-weight: 550;
  margin-left: 5px;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.sButton:hover {
  border: 1px solid black;
  color: black;
  background-color: white;
}
