.Main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px 50px;
  overflow-y: scroll;
}

.Main .searchContainer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 5px;
}
.searchContainer .inputContainer {
  position: relative;
  margin: 10px 0px;
  padding-right: 10px;
}
.inputContainer .Field {
  padding: 5px;
  height: 35px;
  width: 100%;
  border: 1px solid rgb(20, 20, 20);
  border-radius: 2px;
  font-weight: 550;
  font-size: 12px;
  color: rgb(83, 83, 83);
}
.Field:focus {
  outline: none !important;
  border: 1px solid rgb(46, 46, 46);
  border-radius: 2px;
  /* box-shadow: 0 0 10px #719ece; */
}
.inputContainer .Icon {
  position: absolute;
  right: 15px;
  top: 10px;
}

.Main .Header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding-left: 5px;
  margin-bottom: 5px;
  text-align: left;
}
.Header .Title {
  font-size: 13px;
  font-weight: 500;
  color: #848484;
}

.infoContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 15px 10px;
  background-color: white;
  align-items: center;
  margin-bottom: 5px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  cursor: pointer;
  text-align: left;
}
.infoContainer:hover {
  border: 1px solid #84848460;
  padding: 15px 10px;
  background-color: #84848420;
}

/* .SInfoContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 0.7fr 0.5fr 0.6fr 0.5fr 0.3fr 0.3fr;
  align-items: center;
  margin-bottom: 5px;
  border: 1px solid #84848420;
  border-left: 1px solid black;
  border-right: 1px solid black;
  border-radius: 5px;
  padding: 15px 10px;
  background-color: #8484842a;
  cursor: pointer;
} */

.address {
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.Name {
  font-size: 13px;
  font-weight: 500;
}

.balance {
  font-size: 15px;
  font-weight: 550;
  padding-left: 5px;
}
