.header {
  display: grid;
  grid-template-columns: 0.8fr 0.4fr 0.6fr 0.6fr 0.6fr 0.5fr 0.6fr 0.4fr;
  padding: 5px 10px;
}
.header .tTitle {
  font-size: 12px;
  font-weight: 450;
  color: #848484;
}

.header .mTitle {
  font-size: 10px;
  font-weight: 400;
  color: #848484;
}

.header .cTitle {
  font-size: 12px;
  font-weight: 450;
  text-align: center;
  color: #848484;
}

.infoCN {
  height: fit-content;
  display: grid;
  grid-template-columns: 0.8fr 0.4fr 0.6fr 0.6fr 0.6fr 0.5fr 0.6fr 0.4fr;
  padding: 5px 10px;
  align-items: center;
  background-color: white;
  margin-bottom: 5px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  /* cursor: pointer; */
}

/* .infoCN:hover {
  border: 1px solid #84848460;
  padding: 5px 10px;
  background-color: #84848420;
} */

.rFlex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.name {
  font-size: 12px;
  font-weight: 600;
  color: #494949;
  overflow: hidden;

  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.item {
  color: #494949;
  font-size: 12px;
  font-weight: 450;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.mItem {
  color: #494949;
  font-size: 10px;
  font-weight: 400;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.cItem {
  font-size: 12px;
  font-weight: 450;
  text-align: center;
  color: #494949;
  cursor: pointer;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
.actionIcon {
  text-align: center;
  cursor: pointer;
  padding-left: 8px;
}

.boxItem {
  background-color: rgba(13, 196, 31, 0.12);
  width: fit-content;
  border: 1px solid #0b8618de;
  border-radius: 3px;
  padding: 2px 10px;

  color: #0b8618de;
  font-size: 12px;
  font-weight: 500;
}
