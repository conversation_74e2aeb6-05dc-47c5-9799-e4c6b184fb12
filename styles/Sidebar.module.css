.Main {
  display: flex;
  flex-direction: row;
  height: 100vh;
  width: 100%;
}

.sidebarContainer {
  width: var(--sidebar-width);
  background-color: #222222;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  animation: sidebarOpenAmination 0.5s;
}

@keyframes sidebarOpenAmination {
  0% {
    width: 60px;
  }
  100% {
    width: var(--sidebar-width);
  }
}

.sidebarCollaspedContainer {
  width: 60px;
  background-color: #222222;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
}

.Content {
  width: calc(100% - 60px);
  padding-bottom: 70px;
}

.sidebarContainer .Content {
  width: calc(100% - var(--sidebar-width));
  padding-bottom: 70px;
}

.sidebarCollaspedContainer .Content {
  width: calc(100% - 60px);
  padding-bottom: 70px;
}

.logoContainer {
  width: 100%;
  height: auto;
  display: flex;
  border-bottom: 1px solid #222222;
  padding-bottom: 15px;
  align-items: center;
  justify-content: space-around;
}

.sidebarContainer .logoText {
  font-size: 22px;
  font-weight: 500;
  color: var(--font-color-bright);
}

.sidebarCollaspedContainer .logoText {
  display: none;
}

.sidebarContainer .itemsContainer {
  width: 100%;
  padding-left: 15px;
  margin-top: 5px;
}

.sidebarCollaspedContainer .itemsContainer {
  width: 100%;
  margin-top: 5px;
  padding: 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sidebarContainer .item {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  align-content: center;
  padding: 10px 10px;
  margin: 5px 0px;
}

.sidebarCollaspedContainer .item {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  align-content: center;
  padding: 10px 10px;
}

.sidebarContainer .item:hover {
  background-color: var(--background-color-bright);
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  cursor: pointer;
}

.sidebarCollaspedContainer .item:hover {
  background-color: var(--background-color-bright);
  border-radius: 5px;
  cursor: pointer;
}

.sidebarContainer .icon {
  height: 100%;
  margin-right: 10px;
  color: var(--font-color-bright);
  display: flex;
  justify-content: center;
}

.sidebarCollaspedContainer .icon {
  height: 100%;
  color: var(--font-color-bright);
}

.sidebarContainer .name {
  font-size: 16px;
  font-weight: 500;
}

.sidebarCollaspedContainer .name {
  display: none;
}

.sidebarContainer .nameText {
  color: var(--font-color-bright);
}

.sidebarCollaspedContainer .nameText {
  display: none;
}

.item:hover .icon {
  color: #222222;
}

.item:hover .nameText {
  font-size: 16px;
  font-weight: 500;
  color: #222222;
}
