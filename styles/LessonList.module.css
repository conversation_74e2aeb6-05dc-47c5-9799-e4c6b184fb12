.Header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 0.7fr 0.7fr 0.4fr;
  padding: 15px 10px;
  cursor: pointer;
}

.Header .Title {
  font-size: 13px;
  font-weight: 500;
  color: #848484;
}

.infoContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 0.7fr 0.7fr 0.4fr;
  padding: 14px 9px;
  background-color: white;
  align-items: center;
  margin-bottom: 5px;
  border: 1px solid #e6e6e6;
  cursor: pointer;
}

.infoContainer:hover {
  border: 1px solid #84848460;
  padding: 14px 9px;
  background-color: #84848420;
}

.sInfoContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 0.7fr 0.7fr 0.4fr;
  align-items: center;
  margin-bottom: 5px;
  cursor: pointer;
  border: 1px solid #84848420;
  border-left: 1px solid black;
  border-right: 1px solid black;
  padding: 14px 9px;
  background-color: #84848410;
}

.Item {
  font-size: 13px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.PostCode {
  width: fit-content;
  padding: 7px;
  border-radius: 7px;
  background-color: #f5f5f5;
}

.Status {
  font-size: 18px;
}

.itemDes {
  font-size: 10px;
  font-weight: 500;
  color: #848484;
}

.tooltipItem {
  font-size: 13px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  position: relative;
}

.tooltipComment {
  visibility: hidden;
  width: 180px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 3px;
  position: absolute;
  z-index: 1;
  bottom: 110%;
  right: 10%;
  margin: auto;
  font-size: 10px;
}

.tooltipComment::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: black transparent transparent transparent;
}

.tooltipItem:hover .tooltipComment {
  visibility: visible;
}
