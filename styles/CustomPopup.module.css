/* // Css For popUp Windo For Status */
.Modal,
.Overlay {
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(49, 49, 49, 0.8);
  position: fixed;
}

.Hidden {
  display: none;
}

.Contents {
  position: absolute;
  top: 0;
  right: 0;
  background: #f1f1f1;
  padding: 40px 25px;
  width: 300px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  animation-name: filterPopupAnimator;
  animation-duration: 0.3s;
  overflow: auto;
}

.Contents .Content {
  opacity: 1;
  animation-name: filterPopupContentAnimator;
  animation-duration: 0.3s;
  height: 100%;
  width: 100%;
  position: relative;
}

/* close-filter-popup  -- Previous Name*/
.Modal .closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px 2px 8px;
  cursor: pointer;
}

@keyframes filterPopupAnimator {
  0% {
    width: 0px;
  }
  100% {
    width: 300px;
  }
}

@keyframes filterPopupContentAnimator {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 0.25;
  }
  50% {
    opacity: 0.5;
  }
  75% {
    opacity: 0.75;
  }
  100% {
    opacity: 1;
  }
}
