/* // CSS For Delete modal....... */

.Main,
.Overlay {
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  position: fixed;
  z-index: 1;
}
.Hidden {
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  position: fixed;
  z-index: 1;

  visibility: hidden;
}

.Main .Content {
  position: absolute;
  top: 15%;
  left: 50%;
  width: 30%;
  height: auto;
  padding: 25px 22px;
  transform: translate(-50%);
  background-color: white;
  box-shadow: 0 0 20px rgba(rgb(66, 66, 66), 0.3);
  visibility: visible;
  z-index: 2;
}

.Content .Header {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.Header .Title {
  font-weight: 500;
  font-size: medium;
  padding-left: 10px;
  display: flex;
  justify-content: center;
}

.Header .Icon {
  display: flex;
  justify-content: center;
}

.Content .Buttons {
  margin-top: 15px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden;
}
.cButton {
  background-color: white;
  width: auto;
  height: 30px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: small;
  padding: 0 10px;
  text-transform: capitalize;
  overflow: hidden;
  cursor: pointer;
  color: red;
  border: 1px solid red;
}
.sButton {
  background-color: black;
  border: 1px solid black;
  width: auto;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: small;
  padding: 0 10px;
  color: white;
  text-transform: capitalize;
  overflow: hidden;
  cursor: pointer;
}
.cButton:hover {
  background: red;
  border: 1px solid white;
  color: white;
}
.sButton:hover {
  border: 1px solid black;
  color: black;
  background-color: white;
}

/* For Update Modal...

.updateContent {
  position: absolute;
  top: 15%;
  left: 50%;
  width: 25%;
  height: auto;
  padding: 25px 22px;
  transform: translate(-50%);
  background-color: white;
  box-shadow: 0 0 20px rgba(rgb(66, 66, 66), 0.3);
  visibility: visible;
  z-index: 2;
}

/* // CSS For Edit modal... */

/* Old Css Item...
.editContent {
 

  position: absolute;
  top: 15%;
  left: 50%;
  height: auto;
  width: auto;
  padding: 25px 22px;
  transform: translate(-50%);
  background-color: white;
  box-shadow: 0 0 20px rgba(rgb(66, 66, 66), 0.3);
  visibility: visible;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  overflow: hidden;
}

.editTitleContainer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #bbb9b9;
  padding-bottom: 15px;
}
.editTitle {
  font-size: 16px;
  font-weight: 600;
  padding-right: 30px;
}
.editCloseIcon {
  cursor: pointer;
  color: rgb(94, 92, 92);
}
.editContentContainer {
  padding: 5px 0px;
}

.editItemContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.inputContainer {
  width: 100%;
  margin: 10px 5px;
  position: relative;
}

.inputHeader {
  padding: 0px;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
  text-transform: capitalize;
}
.InputField {
  padding: 5px;
  height: 30px;
  width: 100%;
  border: 1px solid black;
  border-radius: 2px;
  position: relative;
}
.InputField:focus {
  outline: none !important;
  border: 1px solid black;
  
}

.checkboxContainer {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 6px 0;
}

.checkboxInput {
  height: 15px;
  width: 15px;
  margin-top: -1px;
  accent-color: black;
}

.searchingIcon {
  position: absolute;
  right: 5px;
  top: 27px;
  font-size: 15px;
}
.editButtonContainer {
  margin-top: 15px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden;
  border-top: 1px solid #bbb9b9;
  padding-top: 15px;
}

.inputContainerFlex {
  margin: 9px 0;
  display: flex;
  align-items: center;
  margin: 12px, 3px;
}
.Inpufieldflex {
  padding: 1px 4px;
  margin-left: 5px;
}

.edit-modal-item {
  position: relative;
  padding: 5px;
  width: 100%;
}

.SelectList {
  position: absolute;
  width: 100%;
  max-height: 200px;
  background-color: white;
  z-index: 1;
  border: 1px solid #919191;
  overflow: hidden;
  overflow-y: scroll;
}
.SelectItem {
  padding: 5px;
  font-size: 12px;
  cursor: pointer;
}
.SelectItem:hover {
  background-color: #d3d3d3;
  border-left: 1px solid black;
  border-right: 1px solid black;
}

.DatePickerContainer {
  margin-bottom: 10px;
  width: 100%;
  position: relative;
}
.DatePickerDesign {
  padding: 5px;
  width: 100%;
}

*/
