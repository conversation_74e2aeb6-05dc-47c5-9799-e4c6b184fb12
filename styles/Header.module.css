.Main {
  width: 100%;
  height: 65px;
  background-color: #ecedf61e;
  border-bottom: 1px solid rgba(100, 100, 100, 0.267);
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
}

.Main .Left {
  display: flex;
  align-items: center;
  padding: 0px 15px;
}

.Main .Right {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 20px;
}

.Right .Item {
  border-left: 1px solid rgba(100, 100, 100, 0.267);
  padding: 5px 10px;
  cursor: pointer;
}

.Item:hover {
  background-color: rgba(100, 100, 100, 0.267);
  border: 1px solid rgba(100, 100, 100, 0.267);
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  overflow: hidden;
}
