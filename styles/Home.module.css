.Main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px 50px;
  overflow-y: scroll;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.Main .listContainer {
  display: grid;
  grid-template-columns: 4.15fr 1fr;
  grid-gap: 15px;
  margin-top: 15px;
}

.listContainer .Left {
  height: auto;
}

.compareContainer {
  background-color: #f9f9f9;
  margin-bottom: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.compareContainer .compare {
  height: auto;
  border: none;
  outline: none;
  color: #7c7b7b;
  background-color: #f9f9f9;
  font-weight: 500;
  padding: 5px 2px;
}

.cardContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 15px;
  margin-top: 10px;
}

.cardContainer .Icon {
  font-size: 1.2vw;
  color: #3b3a3a;
}

.listContainer .Right {
  height: auto;
  width: 100%;
}
.Right .Title {
  font-size: 13px;
  font-weight: 500;
  color: #848484;
  margin: 15px 0px;
}

@media screen and (max-width: 1366px) {
  .Main {
    padding: 15px 40px;
  }

  .cardContainer {
    grid-gap: 10px;
    margin-top: 0px;
  }

  .listContainer {
    grid-gap: 10px;
    margin-top: 10px;
  }
}

@media screen and (max-width: 1024px) {
  .Main {
    padding: 15px 20px;
  }

  .cardContainer {
    grid-gap: 7px;
    margin-top: 0px;
  }

  .listContainer {
    margin-top: 7px;
    grid-gap: 7px;
  }
}
