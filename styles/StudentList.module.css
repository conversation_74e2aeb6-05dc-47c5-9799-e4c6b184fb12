.Main {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px 50px;
  overflow-y: scroll;
}

.Flex {
  width: 100%;
  display: flex;
  align-items: center;
}

.Main .headerContainer {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.headerContainer .listTitle {
  color: #222222;
  font-size: 16px;
  font-weight: 600;
}
.Main .searchContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 10px;
}
.searchContainer .inputContainer {
  position: relative;
  margin: 10px 0px;
  width: 200px;
}
.inputContainer .Field {
  padding: 5px;
  height: 35px;
  width: 100%;
  border: 1px solid rgb(155, 155, 155);
  border-radius: 2px;
  font-weight: 550;
  font-size: 12px;
  color: rgb(83, 83, 83);
  outline: none;
}
.Field:focus {
  border: 1px solid rgb(80, 80, 80);
}
.Field::placeholder {
  color: #c9c5c5;
  font-size: 12px;
  font-weight: 100;
  padding: 0px 5px;
}
.inputContainer .Icon {
  position: absolute;
  right: 10px;
  top: 10px;
  color: #5c5c5c;
}

.searchContainer .AddStu {
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0096d7;
  border-radius: 3px;
  cursor: pointer;
  margin-right: 10px;
}

.AddStu p {
  font-size: 12px;
  font-weight: 300;
  color: #ffffff;
  padding: 5px 20px;
}

.Header {
  display: grid;
  grid-template-columns: 0.9fr 1fr 0.5fr 0.6fr 0.5fr 0.5fr 0.3fr;
  padding: 5px 0px;
  /* // border-bottom: 1px solid #42424257;
  // margin-bottom: 10px; */
  cursor: pointer;
}

.Header .tTitle {
  font-size: 14px;
  font-weight: 450;
  color: #848484;
}
.Header .mTitle {
  font-size: 12px;
  font-weight: 400;
  color: #848484;
}
.Header .bTitle {
  font-size: 10px;
  font-weight: 380;
  color: #848484;
}

.Header .cTitle {
  font-size: 14px;
  font-weight: 450;
  text-align: center;
  color: #848484;
}

.infoContainer {
  display: grid;
  grid-template-columns: 0.9fr 1fr 0.5fr 0.6fr 0.5fr 0.5fr 0.3fr;
  padding: 13px 9px;
  gap: 5px;
  background-color: white;
  align-items: center;
  margin-bottom: 5px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  cursor: pointer;
}

.infoContainer:hover {
  border: 1px solid #84848460;
  padding: 13px 9px;
  background-color: #84848420;
}

.SInfoContainer {
  background-color: #a5a5a52a;
  display: grid;
  grid-template-columns: 0.9fr 1fr 0.5fr 0.6fr 0.5fr 0.5fr 0.3fr;
  gap: 5px;
  align-items: center;
  border: 1px solid #e6e6e6;
  border-left: 1px solid rgb(100, 100, 100);
  border-right: 1px solid rgb(100, 100, 100);
  /* border-radius: 5px; */
  padding: 13px 9px;
  margin-bottom: 5px;
  cursor: pointer;
}

.NameConatiner {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.Name {
  font-size: 14px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.tName {
  font-size: 12px;
  font-weight: 500;
  color: #707070;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

/* .Email {
  font-size: 13px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
} */

.Item {
  color: #222222;
  font-size: 13px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.MiddleItem {
  color: #707070;
  font-size: 10px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.itemDes {
  font-size: 9px;
  font-weight: 300;
  color: #707070;
}

.PostCode {
  width: fit-content;
  padding: 2px 4px;
  border-radius: 5px;
  background-color: #f5f5f5;
  color: #6b6b6b;
  font-size: 9px;
  font-weight: normal;
  margin-top: 2px;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.cItem {
  font-size: 13px;
  font-weight: normal;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.wrapper {
  position: relative;
}

.wrapper .tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  /* transform: translateX(-50%); */
  color: #6b6b6b;
  background-color: #fffffff6;
  border: 1px solid rgb(100, 100, 100);
  border-radius: 4px;
  font-size: 9px;
  font-weight: 300;
  padding: 8px;
  z-index: 1;

  width: 380px;

  overflow-wrap: break-word;
}

.tooltip .Flex {
  width: 100%;
  display: flex;
  align-items: center;
}
