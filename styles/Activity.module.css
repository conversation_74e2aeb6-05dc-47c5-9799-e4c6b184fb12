.mainContainer {
  display: flex;
  align-items: center;
  background: white;
  width: 100%;
  height: 70px;
  padding: 11px 10px;
  margin-bottom: 5px;
  border: 1px solid #e6e6e6;
}

.Content {
  display: flex;
  flex: row;
  align-items: center;
}

.Content .Left {
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.Content .Right {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0px 5px;
}

.Left .Image {
  height: 30px;
  width: 30px;
}

.Right .Description {
  font-size: 12px;
  color: #848484;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.Right .Date {
  margin-top: 3px;
  font-size: 9px;
  font-weight: 400;
  color: #363535e0;
}

@media screen and (max-width: 1366px) {
  .Right .Description {
    font-size: 10px;
  }
}


