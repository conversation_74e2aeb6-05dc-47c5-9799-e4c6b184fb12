.Main {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  position: relative;
}

.Main .Content {
  font-weight: 550;
  font-size: 13px;
  margin-bottom: 2px;
}

.Main .StuContent {
  font-weight: 550;
  font-size: 13px;
  position: relative;
  padding-bottom: 100px;
  /* padding-top: 40px; */
}

.Flex {
  /* width: 100%; */
  display: flex;
  align-items: center;
}

.Flex .Left {
  padding-right: 10px;
}

.Title {
  padding: 0px;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 2px;
  color: #2b2a2af3;
}

.inputContainer {
  margin-bottom: 10px;
  width: 100%;
  position: relative;
}

.inputContainer .Field {
  padding: 5px;
  height: 35px;
  width: 100%;
  border: 1px solid rgb(155, 155, 155);
  border-radius: 2px;
  font-weight: 500;
  font-size: 12px;
  color: rgb(104, 104, 104);
  outline: none;
}
.Field:focus {
  border: 1px solid rgb(80, 80, 80);
}

.checkboxContainer {
  display: flex;
  align-items: center;
  margin: 5px 0;
  font-weight: 550;
  font-size: 13px;
  color: #2b2a2af3;
  margin-bottom: 2px;
  margin-left: -3px;
}
.checkboxContainer .Field {
  height: 15px;
  width: 15px;
  accent-color: black;
  margin-right: 5;
}

/* For Button ... */

.Main .Footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  position: fixed;
  bottom: 0;
  right: 0;
  right: 0;
  width: 300px;
  background-color: white;
}

.Footer .sButton {
  background: rgb(49, 49, 49);
  color: white;
  text-align: center;
  font-weight: 600;
  text-transform: capitalize;
  width: 50%;
  border: 0px;
  border-radius: 4px;
  margin: 0px;
  height: 35px;
  margin-left: 10px;
  cursor: pointer;
}

.Footer .rButton {
  color: white;
  text-align: center;
  font-weight: 600;
  text-transform: capitalize;
  width: 50%;
  border: 0px;
  border-radius: 4px;
  margin: 0px;
  height: 35px;
  cursor: pointer;
  /* @extend .filter-popup-submit-button; */
  background: #f44336;
}

.sButton:hover {
  background: black;
}
.rButton:hover {
  background: #da190b;
}

/* For InputSelector .... */

.selectListContainer {
  /* position: absolute; */
  width: 100%;
  max-height: 200px;
  background-color: white;
  border: 1px solid #919191;
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: #919191 transparent;
  padding: 5px 0px;
}

.selectListContainer::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}
.selectListContainer::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 0;
}
.selectListContainer .List {
  padding: 5px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  color: #161616b4;
}

.List:hover {
  background-color: #d3d3d3;
  border-left: 1px solid black;
  border-right: 1px solid black;
}

.inputContainer .serchingIcon {
  position: absolute;
  right: 5px;
  top: 5px;
  font-size: 15px;
}
